import As<PERSON><PERSON>and<PERSON> from 'express-async-handler'
import Jwt from 'jsonwebtoken'
import { HttpException } from '../exceptions/httpException.js'
import { Admin } from '../models/admin/admin.js'
import { Middleware } from '../models/middleware.js'
import { User } from '../models/user/user.js'

// verify admin with jwt token form cookies destructure
export class Authentication {
	isAdmin = AsyncHandler(async (req, res) => {
		const authHeader = req.headers.authorization
		const token = authHeader.split(' ')[1]
		if (token) {
			const decodedData = Jwt.verify(
				token,
				process.env.REFRESH_TOKEN_PRIVATE_KEY,
			)
			if (decodedData) {
				const admin = await Admin.findById(decodedData?._id)
				if (!admin) {
					throw new HttpException(403, 'permission denied')
				}
				if (admin.block) {
					throw new HttpException(
						403,
						'permission denied, blocked by super admin',
					)
				}
				req.admin = admin
				next()
			}
		} else {
			throw new HttpException(401, 'not authorized')
		}
	})
	authorizedAdminAccess = AsyncHandler(async (req, res) => {
		return async (req, res, next) => {
			if (req.admin.role === 'super admin') {
				return next()
			}
			const middleware = await Middleware.findOne({ name: req.admin.role })
			if (!middleware?.features?.includes(access)) {
				throw new HttpException(403, 'permission denied for Your role')
			}
			next()
		}
	})
	isUser = AsyncHandler(async (req, res) => {
		const authHeader = req.headers.authorization
		const token = authHeader.split(' ')[1]
		if (token) {
			const decodedData = Jwt.verify(
				token,
				process.env.REFRESH_TOKEN_PRIVATE_KEY,
			)
			if (decodedData) {
				const user = await User.findById(decodedData?._id)
				if (!user) {
					throw new HttpException(403, 'permission denied')
				}
				if (!user.active) {
					throw new HttpException(
						403,
						'permission denied, blocked by super admin',
					)
				}
				req.user = user
				next()
			}
		} else {
			throw new HttpException(401, 'not authorized')
		}
	})
}
export const isAdmin = async (req, res, next) => {
	try {
		const authHeader = req.headers.authorization
		const token = authHeader?.split(' ')[1]
		if (token) {
			const decodedData = Jwt.verify(
				token,
				process.env.REFRESH_TOKEN_PRIVATE_KEY,
			)
			if (decodedData) {
				const admin = await Admin.findById(decodedData?._id)
				if (!admin) {
					return res.status(403).json({ errors: 'Permission denied' })
				}
				if (admin.block) {
					return res
						.status(403)
						.json({ errors: 'Permission denied , blocked by super admin' })
				}
				req.admin = admin
				next()
			}
		} else {
			res.status(401).json({ error: 'Not authorized' })
		}
	} catch (error) {
		// console.log(error)
		if (error.message === 'jwt expired') {
			return res.status(401).json({ success: false, errors: error.message })
		}
		res.status(500).json({ success: false, errors: error.message })
	}
}

// verify admin role for various level of access
export const authorizedAdminAccess = access => {
	return async (req, res, next) => {
		if (req.admin.role === 'super admin') {
			return next()
		}
		const middleware = await Middleware.findOne({ name: req.admin.role })
		if (!middleware?.features?.includes(access)) {
			return res
				.status(403)
				.json({ success: false, errors: 'Permission denied for Your role' })
		}
		next()
	}
}


export const authorizedAdminAccessMultiple = (accessRules) => {
  return async (req, res, next) => {
    // Super admin bypass
    if (req.admin.role === 'super admin') {
      return next();
    }

    try {
      const middleware = await Middleware.findOne({ name: req.admin.role });

      if (!middleware || !middleware.features) {
        return res
          .status(403)
          .json({ success: false, errors: 'Invalid role or missing features' });
      }

      // Check if the admin has any of the required access rules
      const hasAccess = accessRules.some(rule => middleware.features.includes(rule));

      if (!hasAccess) {
        return res
          .status(403)
          .json({ success: false, errors: 'Permission denied for your role' });
      }

      next();
    } catch (error) {
      console.error('Error in authorizedAdminAccessMultiple:', error);
      res.status(500).json({ success: false, errors: 'Internal server error' });
    }
  };
};




// verify admin role for various level of access
export const superAdminAccess = () => {
	return async (req, res, next) => {
		if (req.admin.role !== 'super admin') {
			return res.status(403).json({
				success: false,
				error: 'Permission denied, Only for Super Admin',
			})
		}
		return next()
	}
}

export const auth = async (req, res, next) => {
	const token = req.headers['x-access-token']
	if (!token) {
		return res.status(403).json({ errors: 'Access Denied No token provided' })
	}
	try {
		const tokenDetails = Jwt.verify(token, process.env.ACCESS_TOKEN_PRIVATE_KEY)
		req.admin = tokenDetails
		next()
	} catch (err) {
		res.status(403).json({ errors: 'Access Denied Invalid token' })
	}
}

export const isUser = async (req, res, next) => {
	try {
		const authHeader = req.headers.authorization
		const token = authHeader.split(' ')[1]
		if (token) {
			const decodedData = Jwt.verify(
				token,
				process.env.REFRESH_TOKEN_PRIVATE_KEY,
			)
			if (decodedData) {
				const user = await User.findById(decodedData?._id)
				if (!user) {
					return res.status(403).json({ errors: 'Permission denied' })
				}
				if (user?.block) {
					return res
						.status(403)
						.json({ errors: 'Permission denied , blocked by super admin' })
				}
				req.user = user
				next()
			}
		} else {
			res.status(401).json({ errors: 'Not authorized' })
		}
	} catch (error) {
		res.status(403).json({ errors: 'Access Denied Invalid token' })
	}
}
