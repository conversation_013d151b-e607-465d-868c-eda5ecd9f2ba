import moment from 'moment-timezone';

export const convertParamsToUTC = (paramNames) => (req, res, next) => {
    const timezone = req.headers['timezone'] || 'Asia/Kolkata'; // Default to IST if no header

    for (const param of paramNames) {
        const dateValue = req.body[param];

        if (dateValue) {
            // Convert date from provided timezone (or IST) to UTC
            const utcDate = moment.tz(dateValue, timezone).utc();

            if (utcDate.isValid()) {
                req.body[param] = utcDate.toISOString(); // Store in UTC format
            } else {
                return res.status(400).json({ error: `Invalid format for ${param} or timezone` });
            }
        }
    }

    next();
};

