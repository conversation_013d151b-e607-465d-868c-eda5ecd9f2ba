import Jwt from 'jsonwebtoken'
import { User } from '../../models/user/user.js'

// varify user with jwt token form cookies destructure
export const isUser = async (req, res, next) => {
	try {
		const token = req.signedCookies.security
		if (token) {
			const decodedData = Jwt.verify(token, process.env.JWT_SECRET)
			if (decodedData) {
				const user = await User.findById(decodedData?._id)
				if (!user) {
					return res
						.status(403)
						.json({ error: 'Permission denied, Please register to access' })
				}
				req.user = user
				next()
			} else {
				return res.status(403).json({ error: 'token not found!' })
			}
		}
	} catch (error) {
		res.status(500).json({ success: false, error: error.message })
	}
}

export const duplicateMailCheck = async (req, res, next) => {
	try {
		const { email } = req.body
		const user = await User.findOne({ email: email })
		if (user) {
			return res.status(409).json({
				success: false,
				error: 'already have an acuid with this email',
			})
		}
		next()
	} catch (error) {
		res.status(500).json({ success: false, error: error.message })
	}
}
