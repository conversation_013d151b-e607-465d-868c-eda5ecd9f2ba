import { Error as MongoError } from 'mongoose'
import { HttpException } from '../exceptions/httpException.js'
import { createErrorLog } from '../services/errorLogService.js'

export const errorMiddleware = async (err, _req, res, _next) => {
    await createErrorLog(err, _req?.user, _req?.admin, _req)

    if (err instanceof MongoError.ValidationError) {
        console.log({ err });
        res.status(400).json({
            message: 'Credentials are incorrect',
            errors: Object.values(err.errors).map(err => err.message),
        })
    }
    else if (err.code === 11000) {
        const duplicateField = Object.keys(err.keyPattern)[0];
        const duplicateValue = err.keyValue[duplicateField];

        res.status(409).json({
            message: `Duplicate value for ${duplicateField}: "${duplicateValue}". Please choose a different ${duplicateField}.`,
            errors: [`Duplicate value for ${duplicateField}: "${duplicateValue}".`],
        });
    }
    // else if (err.code === 11000) {
    //     res.status(409).json({
    //         message: 'Credentials are already in use',
    //         errors: ['Credentials are already in use'],
    //     })
    // }
    else if (err instanceof HttpException) {
        const status = err.status || 500
        const message = err.message || 'Something went wrong!'
        const errors = err.errors

        res.status(status).json({ message, errors })
    } else {
        console.log({ err })
        res.status(500).json({
            message: err.message,
            errors: [err.message],
        })
    }
}
