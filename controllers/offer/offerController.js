import AsyncHandler from "express-async-handler";
import { AdminLogService } from "../../services/adminLog.js";
import { OfferService } from "../../services/offer.js";

export class OfferController {
    offerService = new OfferService();
    AdminLogService = new AdminLogService();

    createOffer = AsyncHandler(async (req, res) => {
        const payload = {
            ...req.body,
            createdBy: req.admin._id,
        };
        const offer = await this.offerService.createOffer(payload);
        const log = {
            admin: req.admin._id,
            log: `${req.admin.name} created new offer ${offer.title} `,
        };
        await this.AdminLogService.createLog(log);
        res.status(201).json({ success: true, offer });
    });
    getAllOffers = AsyncHandler(async (req, res) => {
        const allOffers = await this.offerService.getAllOffers(req.query);
        res.json({ success: true, ...allOffers });
    });
    getAllOffersList = AsyncHandler(async (req, res) => {
        const allOffers = await this.offerService.getAllOffersList(req.query);
        res.json({ success: true, allOffers });
    });

    getOfferDetails = AsyncHandler(async (req, res) => {
        const offerDetails = await this.offerService.getOfferDetails(
            req.params.offerId
        );
        res.json({ success: true, offerDetails });
    });

    updateOfferPriority = AsyncHandler(async (req, res) => {
        const offer = await this.offerService.updateOfferPriority(
            req.params.offerId,
            req.body.priority,
            req.admin._id
        );
        const log = {
            admin: req.admin._id,
            log: `${req.admin.name} updated ${offer.title} offer priority  to ${req.body.priority}  `,
        };
        await this.AdminLogService.createLog(log);
        res.json({ success: true, offer });
    });

    updateOffer = AsyncHandler(async (req, res) => {
        const payload = {
            ...req.body,
            updatedBy: req.admin._id,
            editedDate: new Date(),
        };
        const updatedOffer = await this.offerService.updateOffer(
            req.params.offerId,
            payload
        );
        const log = {
            admin: req.admin._id,
            log: `${req.admin.name} updated multiple details of offer ${updatedOffer.title} `,
        };
        await this.AdminLogService.createLog(log);

        res.json({ success: true, offer: updatedOffer });
    });

    updateOfferActiveStatus = AsyncHandler(async (req, res) => {
        const offer = await this.offerService.updateOfferActiveStatus(
            req.params.offerId,
            req.admin._id
        );
        const log = {
            admin: req.admin._id,
            log: `${req.admin.name}${offer.active ? "deleted" : "restored"} offer ${offer.title
                } `,
        };
        await this.AdminLogService.createLog(log);
        res.json({ success: true, offer });
    });

    getAllTrendingOffers = AsyncHandler(async (req, res) => {
        const allTrendingOffers = await this.offerService.getAllTrendingOffers(
            req.query
        );
        res.json({ success: true, ...allTrendingOffers });
    });
    updateOfferTrendingStatus = AsyncHandler(async (req, res) => {
        const offer = await this.offerService.updateOfferTrendingStatus(
            req.params.offerId,
            req.admin._id
        );
        const log = {
            admin: req.admin._id,
            log: `${req.admin.name}${offer.trending ? "added to" : "removed from"
                } trending list offer ${offer.title} `,
        };
        await this.AdminLogService.createLog(log);
        res.json({ success: true, offer });
    });


    updateOfferMissedStatus = AsyncHandler(async (req, res) => {
        const offer = await this.offerService.updateOfferMissedStatus(
            req.params.offerId,
            req.admin._id
        );
        const log = {
            admin: req.admin._id,
            log: `${req.admin.name}${offer.trending ? "added to" : "removed from"
                } trending list offer ${offer.title} `,
        };
        await this.AdminLogService.createLog(log);
        res.json({ success: true, offer });
    });

    updateOfferTrendingPriority = AsyncHandler(async (req, res) => {
        const offer = await this.offerService.updateOfferTrendingPriority(
            req.params.offerId,
            req.body.trendingPriority,
            req.admin._id
        );
        const log = {
            admin: req.admin._id,
            log: `${req.admin.name} updated trending priority to ${offer.trendingPriority} of offer ${offer.title} `,
        };
        await this.AdminLogService.createLog(log);
        res.json({ success: true, offer });
    });
}

// const filterCashback = async (req, res) => {
// 	try {
// 		const {
// 			page,
// 			title,
// 			offer,
// 			store,
// 			partner,
// 			admin,
// 			activeOffer,
// 			inactiveOffer,
// 			expToday,
// 			name,
// 			lowPriority,
// 			hiPriority,
// 			firstExpiry,
// 			lastUpdated,
// 		} = req.query
// 		const initialData = {}
// 		if (title) {
// 			initialData.cashbackTitle = {
// 				$regex: title,
// 				$options: 'i',
// 			}
// 		}
// 		if (offer) {
// 			initialData.couponCode = {
// 				$regex: offer,
// 				$options: 'i',
// 			}
// 		}
// 		if (store) {
// 			initialData.storeId = store
// 		}
// 		if (partner) {
// 			initialData.affiliation = partner
// 		}
// 		if (admin) {
// 			initialData.createdBy = admin
// 		}
// 		if (activeOffer === 'true') {
// 			initialData.active = true
// 		}
// 		if (inactiveOffer === 'true') {
// 			initialData.$or = [{ $lte: new Date() }, { active: false }]
// 		}

// 		if (expToday === 'true') {
// 			const today = new Date()
// 			const startOfToday = new Date(
// 				today.getFullYear(),
// 				today.getMonth(),
// 				today.getDate(),
// 			)
// 			const endOfToday = new Date(
// 				today.getFullYear(),
// 				today.getMonth(),
// 				today.getDate() + 1,
// 			)

// 			initialData.dateExpiry = {
// 				$gte: startOfToday,
// 				$lt: endOfToday,
// 			}
// 		}
// 		const sortData = { createdAt: -1 }
// 		if (name === 'true') {
// 			sortData.cashbackOffer = 1
// 		}
// 		if (lowPriority === 'true') {
// 			sortData.priority = 1
// 		}
// 		if (hiPriority === 'true') {
// 			sortData.priority = -1
// 		}
// 		if (firstExpiry === 'true') {
// 			initialData.dateExpiry = { $gte: new Date() }
// 			sortData.dateExpiry = 1
// 		}
// 		if (lastUpdated === 'true') {
// 			sortData.updatedAt = -1
// 		}

// 		const data = await Cashback.find(initialData)
// 			.sort(sortData)
// 			.populate('createdBy', 'name')
// 			.populate('updatedBy', 'name')
// 			.populate('storeId')
// 			.populate('categoryId.category')
// 			.populate('categoryId.subCategories')
// 			.populate('affiliation', 'name')

// 		const currentPage = parseInt(page)
// 		const totalItems = data.length
// 		const maxPages = Math.ceil(totalItems / 20)
// 		const startIndex = (currentPage - 1) * 20
// 		const endIndex = startIndex + 20
// 		const cashbacks = data.slice(startIndex, endIndex)
// 		res.status(200).json({ success: true, cashbacks, maxPages })
// 	} catch (error) {
// 		res.status(500).json({ success: false, error: error.message })
// 	}
// }
