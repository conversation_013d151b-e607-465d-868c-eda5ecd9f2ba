import asyncHandler from "express-async-handler";
import { OnGoingSaleOfferService } from "../../services/onGoingSaleOffers.js";
import { AdminLogService } from "../../services/adminLog.js";

export class OnGoingSaleOfferController {
    onGoingSalesService = new OnGoingSaleOfferService()
    adminLogService = new AdminLogService()

    createOnGoingSale = asyncHandler(async (req, res) => {
        const payload = {
            ...req.body,
            createdBy: req.admin._id
        }
        const onGoingSaleOffer = await this.onGoingSalesService.createOnGoingSale(payload)
        const log = {
            admin: req.admin._id,
            log: `${req.admin.name} created new on going offer sale ${onGoingSaleOffer.saleName}`
        }
        await this.adminLogService.createLog(log)
        res.status(201).json({ success: true, onGoingSaleOffer })
    })

    getAllOnGoingSales = asyncHandler(async (req, res) => {
        const allSales = await this.onGoingSalesService.getAllOnGoingSales(req.query)
        res.json({ success: true, ...allSales })
    })

    getOnGoingSaleDetails = asyncHandler(async (req, res) => {
        const onGoingSaleDetails = await this.onGoingSalesService.getOnGoingSaleDetails(req.params.offerId)
        res.json({ success: true, onGoingSaleDetails })
    })

    updateOnGoingSale = asyncHandler(async (req, res) => {
        const onGoingSale = await this.onGoingSalesService.updateOnGoingSale(req.params.offerId, req.body)
        const log = {
            admin: req.admin._id,
            log: `${req.admin.name} updated  on going sale offer  ${onGoingSale.saleName}`
        }
        await this.adminLogService.createLog(log)

        res.json({ success: true, onGoingSale })
    })

    deleteOnGoingSale = asyncHandler(async (req, res) => {
        await this.onGoingSalesService.deleteOnGoingSale(req.params.offerId)
        const log = {
            admin: req.admin._id,
            log: `${req.admin.name} deleted on going offer sale offer `
        }
        await this.adminLogService.createLog(log)
        res.json({ success: true, onGoingSaleId: req.params.offerId })
    })

    handleBlockStatus = asyncHandler(async (req, res) => {
        await this.onGoingSalesService.handleBlock(req.params.offerId)
        const log = {
            admin: req.admin._id,
            log: `${req.admin.name} deleted on going offer sale offer `
        }
        await this.adminLogService.createLog(log)
        res.json({ success: true, onGoingSaleId: req.params.offerId })
    })

}
