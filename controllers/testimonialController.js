import asyncHand<PERSON> from "express-async-handler";
import { TestimonialService } from "../services/testimonial.js";
import { AdminLogService } from "../services/adminLog.js";

export class TestimonialController {
  testimonialService = new TestimonialService();
  adminLogService = new AdminLogService();

  createTestimonial = asyncHandler(async (req, res) => {
    const testimonial = await this.testimonialService.createTestimonial(
      req.body
    );
    const log = {
      admin: req.admin._id,
      log: `${req.admin.name} created new testimonial uid of ${testimonial.uid}`,
    };
    await this.adminLogService.createLog(log);
    res.status(201).json({ success: true, testimonial });
  });

  getTestimonialDetails = asyncHandler(async (req, res) => {
    const testimonial = await this.testimonialService.getTestimonialDetails(
      req.params.testimonialId
    );
    res.json({ success: true, testimonial });
  });

  getAllTestimonials = asyncHandler(async (req, res) => {
    const allTestimonials = await this.testimonialService.getAllTestimonials(
      req.query
    );
    res.json({ success: true, ...allTestimonials });
  });

  updateTestimonials = asyncHandler(async (req, res) => {
    const testimonial = await this.testimonialService.updateTestimonial(
      req.params.testimonialId,
      req.body
    );
    const log = {
      admin: req.admin._id,
      log: `${req.admin.name} updated the details  testimonial  uid of ${testimonial.uid}`,
    };
    await this.adminLogService.createLog(log);
    res.json({ success: true, testimonial });
  });

  updateTestimonialActiveStatus = asyncHandler(async (req, res) => {
    await this.testimonialService.updateTestimonialActiveStatus(
      req.params.testimonialId
    );
    const log = {
      admin: req.admin._id,
      log: `${req.admin.name} updated the  testimonial active status  uid of ${testimonial.uid}`,
    };
    await this.adminLogService.createLog(log);
    res.json({ success: true, testimonialId: req.params.testimonialId });
  });
}
