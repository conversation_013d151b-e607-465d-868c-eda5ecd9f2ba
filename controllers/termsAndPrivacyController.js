import AsyncHandler from "express-async-handler";
import { TermsAndPrivacyService } from "../services/termsAndPrivacy.js";
import { AdminLogService } from "../services/adminLog.js";

export class TermsAndPrivacyController {
  termsAndPrivacy = new TermsAndPrivacyService();
  adminLogService = new AdminLogService();

  createTermsAndPrivacy = AsyncHandler(async (req, res) => {
    const payload = {
      ...req.body,
      createdBy: req.admin._id,
    };
    const termsAndPrivacy = await this.termsAndPrivacy.createTermsAndPrivacy(
      payload
    );

    res.status(201).json({ success: true, termsAndPrivacy });
  });

  getAllTermsAndPrivacies = AsyncHandler(async (req, res) => {
    const query = req.query;
    const allTermsAndPolicies =
      await this.termsAndPrivacy.getAllTermsAndPrivacies(query);
    res.status(200).json({ success: true, ...allTermsAndPolicies });
  });

  getTermsAndPrivacyDetails = AsyncHandler(async (req, res) => {
    const termsAndPrivacy =
      await this.termsAndPrivacy.getTermsAndPrivacyDetails(req.params.id);
    res.status(200).json({ success: true, termsAndPrivacy });
  });

  updateTermsAndPrivacy = AsyncHandler(async (req, res) => {
    const termsAndPrivacy = await this.termsAndPrivacy.updateTermsAndPrivacy(
      req.body,
      req.params.id,
      req.admin._id
    );
    res.status(200).json({ success: true, termsAndPrivacy });
  });

  updateTermsAndPrivacyActiveStatus = AsyncHandler(async (req, res) => {
    console.log(
      "🚀 ~ TermsAndPrivacyController ~ updateIsActiveStatus=AsyncHandler ~ req.params:",
      req.params
    );
    await this.termsAndPrivacy.updateTermsAndPrivacyActiveStatus(req.params.id);

    res.json({ success: true, termsAndPrivacyId: req.params.id });
  });

  updateTermsAndPrivacyType = AsyncHandler(async (req, res) => {
    console.log(
      "🚀 ~ TermsAndPrivacyController ~ updateIsActiveStatus=AsyncHandler ~ req.params:",
      req.params
    );
    const termsAndPrivacy =
      await this.termsAndPrivacy.updateTermsAndPrivacyType(
        req.params.id,
        req.body.type
      );
    res.status(200).json({ success: true, termsAndPrivacy });
  });

  getTermsAndPrivacyLogs = AsyncHandler(async (req, res) => {
    const allTermsAndPolicyLogs =
      await this.termsAndPrivacy.getTermsAndPrivacyLogs(req.query);
    res.json({ success: true, ...allTermsAndPolicyLogs });
  });
}
