import asyncHandler from "express-async-handler";
import { IcbGiftCardOrderService } from "../services/icbGiftCardOrder.js";

export class IcbGiftCardController {
  icbGiftCardOrderService = new IcbGiftCardOrderService();

  getAllIcbGiftCardOrders = asyncHandler(async (req, res) => {
    const allIcbGiftCards =
      await this.icbGiftCardOrderService.getAllIcbGiftCardOrders(req.query);
    res.json({ success: true, ...allIcbGiftCards });
  });
}
