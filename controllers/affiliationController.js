import AsyncHandler from 'express-async-handler'
import { AdminLogService } from '../services/adminLog.js'
import { AffiliationService } from '../services/affiliation.js'

export class AffiliationController {
	affiliationService = new AffiliationService()
	adminLogService = new AdminLogService()
	createAffiliation = AsyncHandler(async (req, res) => {
		const payload = {
			...req.body,
			createdBy: req.admin._id,
		}
		const affiliation = await this.affiliationService.createAffiliation(payload)
		const log = {
			admin: req.admin._id,
			log: `${req.admin.name} created new affiliation ${req.body.name} at `,
		}
		await this.adminLogService.createLog(log)
		res.status(201).json({ success: true, affiliation })
	})

	getAllAffiliations = AsyncHandler(async (req, res) => {
		const allAffiliations = await this.affiliationService.getAllAffiliations(
			req.query,
		)
		res.json({ success: true, ...allAffiliations })
	})

	getAffiliationDetails = AsyncHandler(async (req, res) => {
		const affiliation = await this.affiliationService.getAffiliationDetails(
			req.params.affiliationId,
		)
		res.json({ success: true, affiliation })
	})

	getAllAffiliationsList = AsyncHandler(async (req, res) => {
		const allAffiliations =
			await this.affiliationService.getAllAffiliationsList()
		res.json({ success: true, allAffiliations })
	})

	updateAffiliation = AsyncHandler(async (req, res) => {
		const affiliation = await this.affiliationService.updateAffiliation(
			req.params.affiliationId,
			req.body,
		)
		res.json({ success: true, affiliation })
	})

	updateAffiliationActiveStatus = AsyncHandler(async (req, res) => {
		const affiliation = await this.affiliationService.updateAffiliationStatus(
			req.params.affiliationId,
		)
		res.json({ success: true, affiliation })
	})
}
