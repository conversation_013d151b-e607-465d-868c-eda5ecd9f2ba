import AsyncHandler from 'express-async-handler'
import { AdminLogService } from '../../services/adminLog.js'
import { BannerService } from '../../services/banner.js'

export class BannerController {
    bannerService = new BannerService()
    adminLogService = new AdminLogService()

    createBanner = AsyncHandler(async (req, res) => {
        const payload = {
            ...req.body,
            createdBy: req.admin._id,
        }
        const banner = await this.bannerService.createBanner(payload)
        res.status(201).json({ success: true, banner })
    })

    getAllBanners = AsyncHandler(async (req, res) => {
        const allBanners = await this.bannerService.getAllBanners(req.query)
        res.json({ success: true, ...allBanners })
    })

    getBannerDetails = AsyncHandler(async (req, res) => {
        const bannerDetails = await this.bannerService.getBannerDetails(
            req.params.bannerId,
        )
        res.json({ success: true, bannerDetails })
    })

    updateBanner = AsyncHandler(async (req, res) => {
        const banner = await this.bannerService.updateBanner(
            req.params.bannerId,
            req.body,
        )
        res.json({ success: true, banner })
    })

    updateBannerActiveStatus = AsyncHandler(async (req, res) => {
        const banner = await this.bannerService.updateBannerActiveStatus(
            req.params.bannerId,
        )
        res.json({ success: true, banner })
    })


    updateBannerPriority = AsyncHandler(async (req, res) => {
        const banner = await this.bannerService.updateBannerPriority(
            req.params.bannerId,
            req.body.priority,
            req.admin._id
        );
        const log = {
            admin: req.admin._id,
            log: `${req.admin.name} updated ${banner.uid} banner priority  to ${req.body.priority}  `,
        };
        await this.adminLogService.createLog(log);
        res.json({ success: true, banner });
    });



    deleteBanner = AsyncHandler(async (req, res) => {
        await this.bannerService.deleteBanner(req.params.bannerId)
        res.json({ success: true, bannerId: req.params.bannerId })
    })
}
