import AsyncHandler from 'express-async-handler'
import { AdminLogService } from '../../services/adminLog.js'
import { MobileStoryService } from '../../services/mobileStory.js'

export class MobileStoryController {
	mobileStoryService = new MobileStoryService()
	adminLogService = new AdminLogService()

	createMobileStory = AsyncHandler(async (req, res) => {
		const payload = {
			...req.body,
			createdBy: req.admin._id,
		}
		const story = await this.mobileStoryService.createMobileStory(payload)
		res.status(201).json({ success: true, story })
	})

	getAllMobileStories = AsyncHandler(async (req, res) => {
		const allMobileStories = await this.mobileStoryService.getAllMobileStories(
			req.query,
		)
		res.json({ success: true, ...allMobileStories })
	})
	getStoryDetails = AsyncHandler(async (req, res) => {
		const storyDetails = await this.mobileStoryService.getStoryDetails(
			req.params.storyId,
		)
		res.json({ success: true, storyDetails })
	})

	updateMobileStory = AsyncHandler(async (req, res) => {
		const story = await this.mobileStoryService.updateMobileStory(
			req.params.storyId,
			req.body,
		)
		res.json({ success: true, story })
	})

	updateMobileStoryActiveStatus = AsyncHandler(async (req, res) => {
		const story = await this.mobileStoryService.updateMobileStoryActiveStatus(
			req.params.storyId,
		)
		res.json({ success: true, story })
	})

	deleteMobileStory = AsyncHandler(async (req, res) => {
		await this.mobileStoryService.deleteMobileStory(req.params.storyId)
		res.json({ success: true, storyId: req.params.storyId })
	})
}
