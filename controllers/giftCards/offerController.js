import AsyncHandler from "express-async-handler";
import { AdminLogService } from "../../services/adminLog.js";
import { GiftCardOfferService } from "../../services/giftCardOffer.js";

export class giftCardOfferController {
  giftCardOfferService = new GiftCardOfferService();
  adminLogService = new AdminLogService();
  createGiftCardOffer = AsyncHandler(async (req, res) => {
    const payload = {
      ...req.body,
      createdBy: req.admin._id,
    };
    const giftcardOffer = await this.giftCardOfferService.createGiftCardOffer(
      payload
    );
    res.status(201).json({ success: true, giftcardOffer });
  });

  getAllGiftCardOffers = AsyncHandler(async (req, res) => {
    const giftCardOffers = await this.giftCardOfferService.getAllGiftCardOffers(
      req.query
    );
    res.json({ success: true, ...giftCardOffers });
  });

  getGiftCardOfferDetails = AsyncHandler(async (req, res) => {
    const giftCardOffer =
      await this.giftCardOfferService.getGiftCardOfferDetails(
        req.params.offerId
      );
    res.json({ success: true, giftCardOffer });
  });

  updateGiftCardOffer = AsyncHandler(async (req, res) => {
    const giftCardOffer = await this.giftCardOfferService.updateGiftCardOffer(
      req.params.offerId,
      req.body
    );
    res.json({ success: true, giftCardOffer });
  });

  deleteGiftCardOffer = AsyncHandler(async (req, res) => {
    const giftCardOffer = await this.giftCardOfferService.deleteGiftCardOffer(
      req.params.offerId
    );
    res.json({ success: true, giftCardOffer });
  });
}
