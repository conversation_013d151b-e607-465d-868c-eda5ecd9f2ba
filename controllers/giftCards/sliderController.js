import AsyncHand<PERSON> from "express-async-handler";
import { AdminLogService } from "../../services/adminLog.js";
import { GiftCardSliderService } from "../../services/giftCardSlider.js";

export class GiftCardSliderController {
  giftCardSliderService = new GiftCardSliderService();
  adminLogService = new AdminLogService();

  createGiftCardSlider = AsyncHandler(async (req, res) => {
    const payload = {
      ...req.body,
      createdBy: req.admin._id,
    };
    const giftCardSlider =
      await this.giftCardSliderService.createGiftCardSlider(payload);
    const log = {
      admin: req.admin._id,
      log: `${req.admin.name} created new gift card slider ${giftCardSlider.termsTitle} `,
    };
    await this.adminLogService.createLog(log);
    res.status(201).json({ success: true, giftCardSlider });
  });

  getAllGiftCardSliders = AsyncHandler(async (req, res) => {
    const allGiftCardSliders =
      await this.giftCardSliderService.getAllGiftCardSliders(req.query);
    res.json({ success: true, ...allGiftCardSliders });
  });

  getGiftCardSliderDetails = AsyncHandler(async (req, res) => {
    const giftCardSlider =
      await this.giftCardSliderService.getGiftCardSliderDetails(
        req.params.sliderId
      );
    res.json({ success: true, giftCardSlider });
  });

  updateGiftCardSlider = AsyncHandler(async (req, res) => {
    const giftCardSlider =
      await this.giftCardSliderService.updateGiftCardSlider(
        req.params.sliderId,
        req.body
      );
    const log = {
      admin: req.admin._id,
      log: `${req.admin.name} updated slider details of ${giftCardSlider.termsTitle} count-${giftCardSlider.uid} `,
    };
    await this.adminLogService.createLog(log);
    res.json({ success: true, giftCardSlider });
  });

  updateGiftCardSliderPriority = AsyncHandler(async (req, res) => {
    const giftCardSlider =
      await this.giftCardSliderService.updateGiftCardSliderPriority(
        req.params.sliderId,
        req.body.priority
      );
    const log = {
      admin: req.admin._id,
      log: `${req.admin.name} updated ${giftCardSlider.termsTitle} gift card slider to ${giftCardSlider.priority} `,
    };
    await this.adminLogService.createLog(log);
    res.json({ success: true, giftCardSlider });
  });

  deleteGiftCardSlider = AsyncHandler(async (req, res) => {
    const giftCardSlider =
      await this.giftCardSliderService.deleteGiftCardSlider(
        req.params.sliderId
      );
    const log = {
      admin: req.admin._id,
      log: `${req.admin.name} created  ${
        giftCardSlider.active ? "restored" : "deleted"
      } ${giftCardSlider.termsTitle} slider `,
    };
    await this.adminLogService.createLog(log);
    res.json({ success: true, giftCardSlider });
  });
}
