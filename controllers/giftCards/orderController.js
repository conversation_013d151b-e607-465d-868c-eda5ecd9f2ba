import AsyncHandler from "express-async-handler";
import { GiftCardOrderService } from "../../services/giftCardOrder.js";

export class GiftCardOrderController {
  giftCardOrderService = new GiftCardOrderService();
  createGiftCardOrder = AsyncHandler(async (req, res) => {
    const payload = {
      ...req.body,
      userId: req.user._id,
    };
    const giftCardOrder = await this.giftCardOrderService.createGiftCardOrder(
      payload
    );
    res.status(201).json({ success: true, giftCardOrder });
  });

  getGiftCardOrderDetails = AsyncHandler(async (req, res) => {
    const giftCardOrder =
      await this.giftCardOrderService.getGiftCardOrderDetails(
        req.params.orderId
      );
    res.json({ success: true, giftCardOrder });
  });

  updateGiftCardOrder = AsyncHandler(async (req, res) => {
    const giftCardOrder = await this.giftCardOrderService.updateGiftCardOrder(
      req.params.orderId,
      req.body
    );
    res.json({ success: true, giftCardOrder });
  });

  getAllGiftCardOrders = AsyncHandler(async (req, res) => {
    const allGiftCardOrders =
      await this.giftCardOrderService.getAllGiftCardOrders(req.query);
    res.status(201).json({ success: true, ...allGiftCardOrders });
  });
}
