import AsyncHandler from "express-async-handler";
import { AdminLogService } from "../../services/adminLog.js";
import { GiftCardService } from "../../services/giftCard.js";

export class GiftCardController {
  giftCardService = new GiftCardService();
  adminLogService = new AdminLogService();

  createGiftCard = AsyncHandler(async (req, res) => {
    const payload = {
      ...req.body,
      createdBy: req.admin._id,
    };
    const giftCard = await this.giftCardService.createGiftCard(payload);
    const log = {
      admin: req.admin._id,
      log: `${req.admin.name} created new gift card ${giftCard.name} `,
    };
    await this.adminLogService.createLog(log);
    res.status(201).json({ success: true, giftCard });
  });

  getAllGiftCards = AsyncHandler(async (req, res) => {
    const allGiftCards = await this.giftCardService.getAllGiftCards(req.query);
    res.json({ success: true, ...allGiftCards });
  });

  getAllGiftCardsList = AsyncHandler(async (req, res) => {
    const allGiftCards = await this.giftCardService.getAllGiftCardList();
    res.json({ success: true, allGiftCards });
  });

  getGiftCardDetails = AsyncHandler(async (req, res) => {
    const giftCard = await this.giftCardService.getGiftCardDetails(
      req.params.giftCardId
    );
    res.json({ success: true, giftCard });
  });

  updateGiftCard = AsyncHandler(async (req, res) => {
    const payload = {
      ...req.body,
      updatedBy: req.admin._id,
    };
    const giftCard = await this.giftCardService.updateGiftCard(
      req.params.giftCardId,
      payload
    );
    const log = {
      admin: req.admin._id,
      log: `${req.admin.name} updated gift card ${giftCard.name} details `,
    };
    await this.adminLogService.createLog(log);
    res.json({ success: true, giftCard });
  });

  deleteGiftCard = AsyncHandler(async (req, res) => {
    const giftCard = await this.giftCardService.deleteGiftCard(
      req.params.giftCardId,
      req.admin._id
    );
    const log = {
      admin: req.admin._id,
      log: `${req.admin.name}  ${giftCard.active ? "disabled" : "enabled "} a ${
        giftCard.name
      } gift card `,
    };
    await this.adminLogService.createLog(log);
    res.json({ success: true, giftCard });
  });

  updateGiftCardPriority = AsyncHandler(async (req, res) => {
    const giftCard = await this.giftCardService.updateGiftCardPriority(
      req.params.giftCardId,
      req.body.priority,
      req.admin._id
    );
    const log = {
      admin: req.admin._id,
      log: `${req.admin.name} updated a ${giftCard.name} gift card  priority to ${req.body.priority}`,
    };
    await this.adminLogService.createLog(log);
    res.json({ success: true, giftCard });
  });
}
