import AsyncHandler from "express-async-handler";
import { AdminLogService } from "../../services/adminLog.js";
import { MissingCashbackService } from "../../services/missingCashback.js";

export class MissingCashbackController {
  missingCashbackService = new MissingCashbackService();
  adminLog = new AdminLogService();
  updateMissingCashbackNotes = AsyncHandler(async (req, res) => {
    const missingCashback =
      await this.missingCashbackService.updateMissingCashbackNotes(
        req.params.cashbackId,
        req.body.notes,
        req.admin._id
      );
    res.json({ success: true, missingCashback });
  });

  updateMissingCashbackStatus = AsyncHandler(async (req, res) => {
    const missingCashback =
      await this.missingCashbackService.updateMissingCashbackStatus(
        req.params.cashbackId,
        req.body.status,
        req.admin._id
      );
    res.json({ success: true, missingCashback });
  });

  getAllMissingCashbacks = AsyncHandler(async (req, res) => {
    console.log(req.query, "query");
    const missingCashback =
      await this.missingCashbackService.getAllMissingCashback(req.query);
    res.json({ success: true, ...missingCashback });
  });

  getMissingCashbackDetails = AsyncHandler(async (req, res) => {
    const missingCashback =
      await this.missingCashbackService.getMissingCashbackDetails(
        req.params.cashbackId
      );
    res.json({ success: true, missingCashback });
  });

  deleteMissingCashback = AsyncHandler(async (req, res) => {
    const missingCashback =
      await this.missingCashbackService.deleteMissingCashback(
        req.params.cashbackId
      );
    const log = {
      admin: req.admin._id,
      log: `${req.admin.name} deleted missing cashback. count-${missingCashback.count} `,
    };
    await this.adminLog.createLog(log);
    res.json({ success: true, missingCashback });
  });
}
