import AsyncHandler from "express-async-handler";
import { PaymentRequestService } from "../../services/paymentRequest.js";
import { AdminLogService } from "../../services/adminLog.js";
import { UserService } from "../../services/user.js";

export class PaymentRequestController {
  paymentRequestService = new PaymentRequestService();
  adminLogService = new AdminLogService();
  userService = new UserService();

  getAllPaymentRequests = AsyncHandler(async (req, res) => {
    const allPaymentRequests =
      await this.paymentRequestService.getAllPaymentRequests(req.query);
    res.json({ success: true, ...allPaymentRequests });
  });

  getPaymentRequestDetails = AsyncHandler(async (req, res) => {
    const paymentRequest =
      await this.paymentRequestService.getPaymentRequestDetails(
        req.params.requestId
      );
    res.json({ success: true, paymentRequest });
  });

  approvePaymentRequest = AsyncHandler(async (req, res) => {
    const paymentRequest =
      await this.paymentRequestService.approvePaymentRequest(
        req.params.requestId,
        req.admin._id
      );

    const log = {
      admin: req.admin._id,
      log: `${req.admin.name} RS:${paymentRequest.withdrawAmount} approved the payment request of user ${paymentRequest?.withdrawer?.name} uid of ${paymentRequest?.withdrawer?.uid}. payment request uid is ${paymentRequest.uid}  `,
    };
    await this.adminLogService.createLog(log);
    res.json({ success: true, paymentRequest });
  });

  rejectPaymentRequest = AsyncHandler(async (req, res) => {
    const paymentRequest =
      await this.paymentRequestService.rejectPaymentRequest(
        req.params.requestId,
        req.admin._id
      );
    const user = await this.userService.rejectWithDrowalBalance(
      paymentRequest.withdrawer,
      paymentRequest.withdrawAmount,
      req.admin_id
    );
    const log = {
      admin: req.admin._id,
      log: `${req.admin.name} RS:${paymentRequest.withdrawAmount} rejected the payment request of user ${user.name} uid of ${user.uid}. payment request uid is ${paymentRequest.uid}  `,
    };
    await this.adminLogService.createLog(log);
    res.json({ success: true, paymentRequest });
  });

  deletePaymentRequest = AsyncHandler(async (req, res) => {
    const paymentRequest =
      await this.paymentRequestService.deletePaymentRequest(
        req.params.requestId
      );
    res.json({ success: true, paymentRequest });
  });
}
