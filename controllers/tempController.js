import <PERSON><PERSON><PERSON>and<PERSON> from "express-async-handler";
import { Earnings } from "../models/user/earnings.js";
import mongoose from 'mongoose'; // Import mongoose if you need to check ObjectId validity or for other utilities

export class TempController {
    getEarningsWithFlipkartReward = AsyncHandler(async (req, res) => {
        console.log("Attempting to fetch and update earnings for Flipkart reward points.");
        try {
            const earnings = await Earnings.find({}).populate('click');
            console.log(`Fetched ${earnings.length} earnings from the database.`);

            const updatedEarnings = []; // This variable is not strictly needed if we modify `earnings` in place and return it.
            const earningsToSave = []; // Collect earnings that need saving

            for (const earning of earnings) {
                // It's good practice to ensure earning.click is not null before accessing its properties
                if (earning.click && earning.click.title === "Flipkart") {
                    if (earning.rewardPoint !== true) {
                        console.log(`Updating rewardPoint for earning ID: ${earning._id} (Click title: Flipkart)`);
                        earning.rewardPoint = true;
                        earningsToSave.push(earning); // Add to list of earnings to be saved
                    } else {
                        // Optional: Log if already true, can be verbose
                        // console.log(`Earning ID: ${earning._id} already has rewardPoint=true for Flipkart click.`);
                    }
                }
                // No 'else' needed here unless you want to log non-Flipkart clicks or clicks with no title
            }

            if (earningsToSave.length > 0) {
                console.log(`Attempting to save ${earningsToSave.length} earnings.`);
                // Mongoose's save() is an instance method. We need to iterate and save.
                // For bulk operations, Model.bulkWrite might be more performant but more complex.
                // Given it's a temporary script, individual saves are acceptable.
                let successfullySavedCount = 0;
                for (const earningDoc of earningsToSave) {
                    try {
                        await earningDoc.save();
                        successfullySavedCount++;
                    } catch (saveError) {
                        console.error(`Failed to save earning ID: ${earningDoc._id}. Error: ${saveError.message}`, saveError);
                        // Decide if one failure should stop all, or continue. For now, continue.
                    }
                }
                console.log(`Successfully saved ${successfullySavedCount} of ${earningsToSave.length} earnings.`);
            } else {
                console.log("No earnings required an update for Flipkart reward points.");
            }

            // The original requirement was to return all earnings, not just updated ones.
            // The `earnings` array objects were modified in place if their rewardPoint changed.
            // So, this array already reflects the latest state of all earnings fetched.
            res.json({
                success: true,
                // Message reflects the number of items that were *processed* for saving,
                // and how many were *actually* saved successfully.
                message: `${earningsToSave.length} earnings were identified for update. ${successfullySavedCount} were successfully saved.`,
                data: earnings // Return the full list, which includes in-memory modifications
            });

        } catch (error) {
            console.error("Error in getEarningsWithFlipkartReward:", error.message, error.stack);
            res.status(500).json({ success: false, message: "Internal server error", error: error.message });
        }
    });
}
