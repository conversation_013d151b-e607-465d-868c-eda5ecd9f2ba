import <PERSON><PERSON><PERSON>and<PERSON> from "express-async-handler";
import { AdminLogService } from "../services/adminLog.js";
import { MiddlewareService } from "../services/middleware.js";

export class MiddlewareController {
  middlewareService = new MiddlewareService();
  adminLogService = new AdminLogService();
  createMiddleware = AsyncHandler(async (req, res) => {
    const payload = {
      name: req.body.name?.toLowerCase(),
      level: req.body.level,
      createdBy: req.admin._id,
    };
    const middleware = await this.middlewareService.createMiddleware(payload);
    const log = {
      admin: req.admin._id,
      log: `${req.admin.name} created new access control role ${req.body.name} `,
    };
    await this.adminLogService.createLog(log);

    res.status(201).json({ success: true, middleware });
  });

  addNewFeatureToMiddleware = AsyncHandler(async (req, res) => {
    const middleware = await this.middlewareService.addNewFeatureToMiddleware(
      req.params.middlewareId,
      req.body.features
    );
    const log = {
      admin: req.admin._id,
      log: `${req.admin.name} add new access ${req.body.features} to multiples role  `,
    };
    await this.adminLogService.createLog(log);

    res.json({ success: true, middleware });
  });

  removeNewFeatureFromMiddleware = AsyncHandler(async (req, res) => {
    const middleware =
      await this.middlewareService.removeNewFeatureFromMiddleware(
        req.params.middlewareId,
        req.body.features
      );
    const log = {
      admin: req.admin._id,
      log: `${req.admin.name} removed access ${req.body.features} from multiples role `,
    };
    await this.adminLogService.createLog(log);

    res.json({ success: true, middleware });
  });

  updateMiddlewareStatus = AsyncHandler(async (req, res) => {
    const middleware = await this.middlewareService.updateMiddlewareStatus(
      req.params.middlewareId
    );
    const log = {
      admin: req.admin._id,
      log: `${req.admin.name} ${
        middleware.active ? "suspended" : "activated"
      } ${middleware.role} `,
    };
    await this.adminLogService.createLog(log);
    res.json({ success: true, middleware });
  });

  updateMiddlewareName = AsyncHandler(async (req, res) => {
    const middleware = await this.middlewareService.updateMiddlewareName(
      req.params.middlewareId,
      req.body.name
    );
    const log = {
      admin: req.admin._id,
      log: `${req.admin.name} updated access control name to ${middleware.name} `,
    };
    await this.adminLogService.createLog(log);
    res.json({ success: true, middleware });
  });

  getAllMiddlewareActions = AsyncHandler(async (req, res) => {
    const allActions = await this.middlewareService.getAllMiddlewareActions();
    res.json({ success: true, allActions });
  });

  getAllMiddlewares = AsyncHandler(async (req, res) => {
    const allMiddlewares = await this.middlewareService.getAllMiddlewares(
      req.query
    );
    res.json({ success: true, ...allMiddlewares });
  });
}
