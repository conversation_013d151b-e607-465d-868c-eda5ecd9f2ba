import As<PERSON><PERSON>and<PERSON> from "express-async-handler";
import { Clicks } from "../models/clicks.js";
import { AdminLogService } from "../services/adminLog.js";
import { ClickService } from "../services/cashbackClick.js";
import { generateCode } from "../utils/uniqueCodeGenerator.js";

export class ClickController {
  clickService = new ClickService();
  AdminLogService = new AdminLogService();

  getAllClicks = AsyncHandler(async (req, res) => {
    const allClicks = await this.clickService.getAllClicks(req.query);
    res.json({ success: true, ...allClicks });
  });

  getClickDetails = AsyncHandler(async (req, res) => {
    const click = await this.clickService.getClickDetails(req.params.clickId);
    res.json({ success: true, click });
  });
}

// filter all cashback clicks
const filterCashbackClicks = async (req, res) => {
  try {
    const { page, keyword, fromDate, toDate, status, store, partner } =
      req.query;
    const filter = {};
    const sort = { createdAt: -1 };
    if (keyword) {
      filter.clickUniqueId = {
        $regex: keyword?.toString().toUpperCase(),
        $options: "i",
      };
    }
    if (fromDate && toDate) {
      filter.createdAt = { $gte: new Date(fromDate), $lte: new Date(toDate) };
    }

    if (store) {
      filter.storeId = store;
    }
    if (partner) {
      filter.affiliation = partner;
    }

    const count = await Clicks.countDocuments(filter, { sort });
    const currentPage = Number(page) || 1;
    const skip = 20 * (currentPage - 1);
    const allClicks = await Clicks.find(filter)
      .sort(sort)
      .populate("userId", "name email count")
      .populate("cashbackId", "cashbackTitle count")
      .populate("storeId", "name")
      .populate("affiliation", "name")
      .limit(20)
      .skip(skip);
    const maxPages = Math.ceil(count / 20);
    res.status(200).json({ success: true, allClicks, maxPages });
  } catch (error) {
    res.status(500).json({ success: false, errors: error.message });
  }
};
