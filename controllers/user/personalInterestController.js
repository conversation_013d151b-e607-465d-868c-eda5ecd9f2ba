import asyncHandler from "express-async-handler";
import { PersonalInterestService } from "../../services/personalInterest.js";

export class PersonalInterestController {
  personalInterestService = new PersonalInterestService()

  createPersonalInterest = asyncHandler(async (req, res) => {
    const personalInterest = await this.personalInterestService.createPersonalInterest(req.body, req.admin._id)
    res.status(201).json({ success: true, personalInterest })
  })

  getAllPersonalInterest = asyncHandler(async (req, res) => {
    const allPersonalInterests = await this.personalInterestService.getAllPersonalInterests(req.query)
    res.json({ success: true, ...allPersonalInterests })
  })

}