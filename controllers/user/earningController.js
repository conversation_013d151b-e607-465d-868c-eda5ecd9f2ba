import AsyncHandler from "express-async-handler";
import moment from "moment";
import { Store } from "../../models/stores/store.js";
import { User } from "../../models/user/user.js";
import { AdminLogService } from "../../services/adminLog.js";
import { ClickService } from "../../services/cashbackClick.js";
import { EarningService } from "../../services/earning.js";
import { UserService } from "../../services/user.js";
import {
  sendCashbackConfirmedMail,
  sendCashbackTrackedMail,
  sendConfirmedCommissionMail,
  sendGotCommissionMail,
} from "../../utils/mailgun.js";
import mongoose, { Types } from "mongoose";
import { Offer } from "../../models/offer/offer.js";
import { sendSms } from "../../utils/sendSms.js";

export class EarningController {
  earningService = new EarningService();
  adminLogService = new AdminLogService();
  clickService = new ClickService();
  userService = new UserService();

  createEarnings = AsyncHandler(async (req, res) => {
    const payload = {
      ...req.body,
      createdBy: req.admin._id,
      rewardPoint: req?.body?.flipkart ? true : false,
    };
    const earning = await this.earningService.createEarnings(payload);

    const click = await this.clickService.updateClickStatus(
      earning.click,
      "tracked"
    );
    const logs = [
      {
        admin: req.admin._id,
        log: `${req.admin.name} created new User Earning id ${earning.uid}  `,
      },
      {
        admin: req.admin._id,
        log: `${req.admin.name} updated User cashback click status to "tracked" ${click.uid} . earning uid:${earning.uid}   `,
      },
    ];
    await this.adminLogService.createMultipleLogs(logs);

    if (earning?.rewardPoint) {
      await this.userService.updateUserRewardPoints(
        earning?.user,
        earning.cashbackAmount,
        req.admin._id
      );
    }
    if (!earning?.rewardPoint) {
      await this.userService.updateUserPendingBalance(
        earning?.user,
        earning.cashbackAmount,
        req.admin._id
      );
    }

    // Fetch trending offers with a limit of 15
    const trendingOffers = await Offer.find({
      trending: true,
      dateExpiry: { $gt: new Date() },
      active: true,
    }) // Filter expired offers
      .limit(3) // Limit to 3 results
      .populate("store")
      .sort({ priority: -1, dateExpiry: -1 })
      .exec();

    const formattedTrendingOffers = trendingOffers.map((offer) => {
      return {
        title: offer?.title,
        storeName: offer?.store?.name,
        offerAmount: offer?.itemPrice - offer?.discount,
        endsIn: `${moment
          .duration(moment(offer?.dateExpiry).diff(moment()))
          .days()} ${
          moment.duration(moment(offer?.dateExpiry).diff(moment())).days() > 1
            ? "Days"
            : "Day"
        } ${moment
          .duration(moment(offer?.dateExpiry).diff(moment()))
          .hours()} Hour`,
        offerCaption: offer?.offer,
        offerUid: offer?.uid,
        offerImage: offer?.productImage?.secureUrl,
      };
    });

    // FIX ME - later change to trending stores
    const trendingStores = await Store.find({
      trending: true,
      active: true,
    })
      .limit(3) // Limit to 3 results
      .sort({ priority: -1 })
      .exec();

    const formattedTrendingStores = trendingStores.map((store) => {
      return {
        name: store?.name,
        storeImage: store?.logo?.secureUrl,
        storeOffer: store?.storeOffer,
      };
    });

    const user = await User.findById(earning?.user).select(
      "email name referralCode"
    );

    const store = await Store.findById(earning?.store).select("name logo");

    const mailPayload = {
      email: user?.email,
      name: user?.name,
      referralCode: user?.referralCode,
      referenceId: earning?.referenceId,
      date: moment(earning?.createdAt).format("D MMMM, YYYY"),
      cashback: earning?.cashbackAmount,
      status: "Tracked",
      confirmDate: moment(earning?.confirmDate).format("D MMMM, YYYY"),
      remarks: earning?.remarks,
      orderAmount: earning?.saleAmount,
      storeLogo: store?.logo?.secureUrl ?? "",
      storeName: store?.name,
      trendingOffers: formattedTrendingOffers,
      trendingStores: formattedTrendingStores,
      referralUrl: `https://${process.env.APP_URL}?r=${user?.referralCode}`,
    };

    await sendCashbackTrackedMail(mailPayload);
    //tracked sms
    if (user?.mobile) {
      const sms = `Hi user, we have successfully tracked your recent transaction at ${store?.name} via us. Actual cashback amount will be updated soon. -IndianCashback`;
      const to = user?.mobile;
      const templateId = 1307160996134040338n;
      await sendSms(to, sms, "auto_track", templateId);
    }

    if (earning?.referralCommission) {
      console.log("referral mail");
      const user = await User.findOne({
        _id: new Types.ObjectId(earning?.user),
      });

      const referrer = await User.findOne({
        _id: new Types.ObjectId(user?.referral),
      });

      await this.userService.updateUserPendingBalance(
        user?.referral,
        earning.referralCommission,
        req.admin._id
      );

      const referralMailPayload = {
        name: referrer?.name,
        commission: earning?.referralCommission,
        purchasePerson: user?.name,
        purchasePersonStatus: user?.status,
        expCommissionDate: moment(earning?.confirmDate).format("D MMMM, YYYY"),
        to: referrer?.email,
      };

      await sendGotCommissionMail(referralMailPayload);

      //send sms
      if (referrer?.mobile) {
        const sms = `Hi user, You have got Rs.${earning?.referralCommission} as referral commission for the purchase of your referral, ${user?.name}. -IndianCashback`;
        console.log(sms, "text");
        const to = referrer?.mobile;

        const templateId = 1307161206621156338n;

        await sendSms(to, sms, "ref commission", templateId);
      }
    }

    res.json({ success: true });
  });

  getAllEarnings = AsyncHandler(async (req, res) => {
    const allEarnings = await this.earningService.getAllEarnings(req.query);
    res.json({ success: true, ...allEarnings });
  });

  getAllAutoTrackedEarnings = AsyncHandler(async (req, res) => {
    const allEarnings = await this.earningService.getAllAutoTrackedEarnings(
      req.query
    );
    res.json({ success: true, ...allEarnings });
  });

  getAllPreApprovedEarnings = AsyncHandler(async (req, res) => {
    const allEarnings = await this.earningService.getAllPreApprovedEarnings(
      req?.query
    );
    res.json({ success: true, allEarnings });
  });

  earningTrackedToConfirm = AsyncHandler(async (req, res) => {
    const earning = await this.earningService.updateEarningStatus(
      req.params.earningId,
      "tracked_for_confirm",
      // "pending",
      req.admin._id
    );

    res.json({ success: true, earningId: req.params.earningId });
  });

  earningTrackedToCancel = AsyncHandler(async (req, res) => {
    const earning = await this.earningService.updateEarningStatus(
      req.params.earningId,
      "tracked_for_cancel",
      req.admin._id
    );

    res.json({ success: true, earningId: req.params.earningId });
  });

  confirmEarnings = AsyncHandler(async (req, res) => {
    const earningIds = req.body.earningIds;
    const adminId = req.admin._id;

    // Initialize arrays to hold the results and necessary data for the mail
    const processedEarnings = [];
    const errors = [];
    let user;
    let store;
    let totalReferralCommission = 0;
    let referrer;
    let referral;

    for (const earningId of earningIds) {
      try {
        const earning = await this.earningService.confirmEarnings(
          earningId,
          adminId
        );
        if (earning?.click) {
          const click = await this.clickService.updateClickStatus(
            earning?.click,
            "confirmed"
          );
        }

        referral = await User.findOne({ _id: earning?.user });
        referrer = await User.findOne({ _id: referral.referral });

        totalReferralCommission += earning?.referralCommission;

        // if (earning?.rewardPoint) {
        //   await this.userService.updateAddUserRewardPoints(
        //     earning?.user,
        //     earning.cashbackAmount,
        //     earning?.referralCommission,
        //     adminId
        //   ); 
        // } else {
          await this.userService.updateAddUserBalance(
            earning?.user,
            earning.cashbackAmount,
            earning.referralCommission,
            adminId
          );
        // }

        processedEarnings.push(earning);

        // Store user and store data for the first earning processed
        if (!user) {
          user = await User.findById(earning?.user).select("email name mobile");
        }
        if (!store) {
          store = await Store.findById(earning?.store).select("name logo");
        }
      } catch (error) {
        errors.push({ earningId, error: error.message });
      }
    }

    if (errors.length > 0) {
      return res.status(500).json({ success: false, errors });
    }

    // Send the referral commission email
    if (totalReferralCommission > 0) {
      const referralMailPayload = {
        name: referrer?.name,
        commission: totalReferralCommission,
        purchasePerson: referral.name,
        purchasePersonStatus: referral?.status,
        to: referrer?.email,
      };

      await sendConfirmedCommissionMail(referralMailPayload);
    }

    // Fetch trending offers and stores once
    const trendingOffers = await Offer.find({
      trending: true,
      dateExpiry: { $gt: new Date() },
      active: true,
    })
      .limit(3)
      .populate("store")
      .sort({ priority: -1, dateExpiry: -1 })
      .exec();

    const formattedTrendingOffers = trendingOffers.map((offer) => ({
      title: offer?.title,
      storeName: offer?.store?.name,
      offerAmount: offer?.itemPrice - offer?.discount,
      endsIn: `${moment
        .duration(moment(offer?.dateExpiry).diff(moment()))
        .days()} ${
        moment.duration(moment(offer?.dateExpiry).diff(moment())).days() > 1
          ? "Days"
          : "Day"
      } ${moment
        .duration(moment(offer?.dateExpiry).diff(moment()))
        .hours()} Hour`,
      offerCaption: offer?.offer,
      offerUid: offer?.uid,
      offerImage: offer?.productImage?.secureUrl,
    }));

    const trendingStores = await Store.find({
      trending: true,
      active: true,
    })
      .limit(3)
      .sort({ priority: -1 })
      .exec();

    const formattedTrendingStores = trendingStores.map((store) => ({
      name: store?.name,
      storeImage: store?.logo?.secureUrl,
      storeOffer: store?.storeOffer,
    }));

    const mailPayload = {
      email: user?.email,
      name: user?.name,
      referenceId: processedEarnings.map((e) => e.referenceId).join(", "),
      date: moment(processedEarnings[0]?.createdAt).format("D MMMM, YYYY"),
      cashback: processedEarnings.reduce((sum, e) => sum + e.cashbackAmount, 0),
      status: "Confirmed",
      confirmDate: moment().format("D MMMM, YYYY"),
      remarks: processedEarnings
        .map((e) => e.remarks)
        .filter((remark) => remark) // Filter out empty remarks
        .join("."),
      orderAmount: processedEarnings.reduce((sum, e) => sum + e.saleAmount, 0),
      storeName: store?.name,
      storeLogo: store?.logo?.secureUrl ?? "",
      trendingOffers: formattedTrendingOffers,
      trendingStores: formattedTrendingStores,
    };

    await sendCashbackConfirmedMail(mailPayload);

    const sms = `Hi ${user?.name}, your cashback from ${store?.name} has been confirmed. For details/redeem, visit https://icashbk.in/account. -IndianCashback`;
    const to = user?.mobile;
    const templateId = 1307160996380903801n;

    await sendSms(to, sms, "mob_otp", templateId);

    res.json({ success: true, processedEarnings });
  });

  // confirmEarnings = AsyncHandler(async (req, res) => {
  //
  //     const earning = await this.earningService.confirmEarnings(
  //         req.params.earningId,
  //         req.admin._id
  //     );
  //
  //     const click = await this.clickService.updateClickStatus(
  //         earning?.click,
  //         "confirmed"
  //     );
  //
  //     if (earning?.rewardPoint) {
  //         await this.userService.updateAddUserRewardPoints(
  //             earning?.user,
  //             earning.cashbackAmount,
  //             req.admin._id
  //         );
  //     } else {
  //         const referral = await User.findOne({ _id: earning?.user });
  //         const referrer = await User.findOne({ _id: referral.referral });
  //
  //         const referralMailPayload = {
  //             name: referrer?.name,
  //             commission: earning?.referralCommission,
  //             purchasePerson: referral.name,
  //             purchasePersonStatus: referral?.status,
  //             to: referrer?.email,
  //         };
  //
  //         await sendConfirmedCommissionMail(referralMailPayload);
  //
  //         await this.userService.updateAddUserBalance(
  //             earning?.user,
  //             earning.cashbackAmount,
  //             earning.referralCommission,
  //             req.admin._id
  //         );
  //     }
  //     // Fetch trending offers with a limit of 15
  //     const trendingOffers = await Offer.find({
  //         trending: true,
  //         dateExpiry: { $gt: new Date() },
  //         active: true,
  //     }) // Filter expired offers
  //         .limit(3) // Limit to 3 results
  //         .populate("store")
  //         .sort({ priority: -1, dateExpiry: -1 })
  //         .exec();
  //
  //     const formattedTrendingOffers = trendingOffers.map((offer) => {
  //         return {
  //             title: offer?.title,
  //             storeName: offer?.store?.name,
  //             offerAmount: offer?.itemPrice - offer?.discount,
  //             endsIn: `${moment
  //                 .duration(moment(offer?.dateExpiry).diff(moment()))
  //                 .days()} ${moment.duration(moment(offer?.dateExpiry).diff(moment())).days() > 1
  //                     ? "Days"
  //                     : "Day"
  //                 } ${moment
  //                     .duration(moment(offer?.dateExpiry).diff(moment()))
  //                     .hours()} Hour`,
  //             offerCaption: offer?.offer,
  //             offerUid: offer?.uid,
  //             offerImage: offer?.productImage?.secureUrl,
  //         };
  //     });
  //
  //
  //     const trendingStores = await Store.find({
  //         trending: true,
  //         active: true,
  //     })
  //         .limit(3) // Limit to 3 results
  //         .sort({ priority: -1 })
  //         .exec();
  //
  //     const formattedTrendingStores = trendingStores.map((store) => {
  //         return {
  //             name: store?.name,
  //             storeImage: store?.logo?.secureUrl,
  //             storeOffer: store?.storeOffer,
  //         };
  //     });
  //
  //
  //
  //     const user = await User.findById(earning?.user).select("email name");
  //
  //     const store = await Store.findById(earning?.store).select("name");
  //
  //     const mailPayload = {
  //         email: user?.email,
  //         name: user?.name,
  //         referenceId: earning?.referenceId,
  //         date: moment(earning?.createdAt).format("D MMMM, YYYY"),
  //         cashback: earning?.cashbackAmount,
  //         status:
  //             earning?.status.charAt(0).toUpperCase() + earning?.status.slice(1),
  //         confirmDate: moment(earning?.confirmDate).format("D MMMM, YYYY"),
  //         remarks: earning?.remarks,
  //         orderAmount: earning?.saleAmount,
  //         storeName: store?.name,
  //         trendingOffers: formattedTrendingOffers,
  //         trendingStores: formattedTrendingStores
  //     };
  //
  //     await sendCashbackConfirmedMail(mailPayload);
  //
  //     res.json({ success: true });
  // });

  cancelEarnings = AsyncHandler(async (req, res) => {
    const earning = await this.earningService.cancelEarnings(
      req.params.earningId,
      req.admin._id
    );
    if (earning?.click) {
      const click = await this.clickService.updateClickStatus(
        earning.click,
        "cancelled"
      );
    }

    if (earning?.status === "confirmed") {
      await this.userService.updateRemoveUserBalance(
        earning?.user,
        earning.cashbackAmount,
        earning.referralCommission,
        req.admin._id
      );
    }
    res.json({ success: true });
  });

  cancelEarningsBulk = AsyncHandler(async (req, res) => {
    const earningIds = req.body.earningIds;
    const adminId = req.admin._id;
    for (const earningId of earningIds) {
      try {
        const earning = await this.earningService.cancelEarnings(
          earningId,
          adminId
        );
        if (earning?.click) {
          const click = await this.clickService.updateClickStatus(
            earning?.click,
            "cancelled"
          );
        }

        if (earning?.status === "confirmed") {
          await this.userService.updateRemoveUserBalance(
            earning?.user,
            earning.cashbackAmount,
            earning.referralCommission,
            req.admin._id
          );
        }
      } catch (error) {
        console.log(error);
      }
    }
    res.json({ success: true });
  });

  getEarningDetails = AsyncHandler(async (req, res) => {
    const earningDetails = await this.earningService.getEarningDetails(
      req.params.earningId
    );
    res.json({ success: true, earningDetails });
  });

  permanentDeleteEarning = AsyncHandler(async (req, res) => {
    await this.earningService.deleteEarnings(req.params.earningId);
    res.json({ success: true, earningId: req.params.earningId });
  });

  updateEarningsCancelStatus = AsyncHandler(async (req, res) => {});

  updateEarning = AsyncHandler(async (req, res) => {
    const payload = {
      ...req.body,
      updatedBy: req.admin._id,
      rewardPoint: req?.body?.flipkart ? true : false,
      autoUpdated: false,
    };
    const earning = await this.earningService.updateEarnings(
      req.params.earningId,
      payload
    );
    const log = {
      admin: req.admin._id,
      log: `${req.admin.name} updated Earning details of earning id ${earning.uid} `,
    };
    await this.adminLogService.createLog(log);
    res.json({ success: true, earning });
  });
}
