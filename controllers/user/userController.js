import As<PERSON><PERSON>and<PERSON> from "express-async-handler";
import { AdminLogService } from "../../services/adminLog.js";
import { UserService } from "../../services/user.js";

export class UserController {
  userService = new UserService();
  adminLogService = new AdminLogService();

  getAllUsers = AsyncHandler(async (req, res) => {
    const allUsers = await this.userService.getAllUsers(req.query);
    res.json({ success: true, ...allUsers });
  });

  getAllUsersList = AsyncHandler(async (req, res) => {
    const allUsers = await this.userService.getAllUsersList(req.query);
    res.json({ success: true, allUsers });
  });

  updateUserActiveStatus = AsyncHandler(async (req, res) => {
    const user = await this.userService.updateUserActiveStatus(
      req.params.userId
    );
    const log = {
      admin: req.admin._id,
      log: `${req.admin.name} updated user block status ${user.email} `,
    };
    await this.adminLogService.createLog(log);
    res.json({ success: true, user });
  });

  writeUserNotes = AsyncHandler(async (req, res) => {
    const user = await this.userService.writeUserNotes(
      req.params.userId,
      req.body.notes,
      req.admin._id
    );
    const log = {
      admin: req.admin._id,
      log: `${req.admin.name} updated user user notes ${user.email} `,
    };
    await this.adminLogService.createLog(log);
    res.json({ success: true, user });
  });

  getUserStatistics = AsyncHandler(async (req, res) => {
    const user = await this.userService.getUserStatistics(req.params.userId);
    res.json({ success: true, user });
  });
}
