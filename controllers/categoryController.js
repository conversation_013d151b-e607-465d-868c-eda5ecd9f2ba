import AsyncHand<PERSON> from "express-async-handler";
import { AdminLogService } from "../services/adminLog.js";
import { CategoryService } from "../services/categories.js";
import { SubCategoryService } from "../services/subCategory.js";

export class CategoryController {
  categoryService = new CategoryService();
  adminLogService = new AdminLogService();

  createCategory = AsyncHandler(async (req, res) => {
    const payload = {
      ...req.body,
      createdBy: req.admin._id,
    };
    const category = await this.categoryService.createCategory(payload);
    const log = {
      admin: req.admin._id,
      log: `${req.admin.name} created new category ${req.body.title}  `,
    };
    this.adminLogService.createLog(log);
    res.json({ success: true, category });
  });

  getAllCategories = AsyncHandler(async (req, res) => {
    const allCategories = await this.categoryService.getAllCategories(
      req.query
    );
    res.json({ success: true, ...allCategories });
  });
  getAllCategoriesList = AsyncHandler(async (req, res) => {
    const allCategories = await this.categoryService.getAllCategoriesList();
    res.json({ success: true, allCategories });
  });
  getCategoryDetails = AsyncHandler(async (req, res) => {
    const category = await this.categoryService.getCategoryDetails(
      req.params.categoryId
    );
    res.json({ success: true, category });
  });

  updateCategoryTrendingStatus = AsyncHandler(async (req, res) => {
    const category = await this.categoryService.updateCategoryTrendingStatus(
      req.params.categoryId
    );
    res.json({ success: true, category });
  });

  updateCategoryTopStatus = AsyncHandler(async (req, res) => {
    const category = await this.categoryService.updateCategoryTopStatus(
      req.params.categoryId
    );
    res.json({ success: true, category });
  });

  updateCategory = AsyncHandler(async (req, res) => {
    const category = await this.categoryService.updateCategory(
      req.params.categoryId,
      req.body
    );
    const log = {
      admin: req.admin._id,
      log: `${req.admin.name} updated category details  `,
    };
    this.adminLogService.createLog(log);
    res.json({ success: true, category });
  });

  updateCategoryTrendingPriority = AsyncHandler(async (req, res) => {
    const category = await this.categoryService.updateCategoryTrendingPriority(
      req.params.categoryId,
      req.body.priority
    );
    const log = {
      admin: req.admin._id,
      log: `${req.admin.name} updated category trending priority to ${category.trendingPriority}  `,
    };
    this.adminLogService.createLog(log);
    res.json({ success: true, category });
  });

  updateCategoryPriority = AsyncHandler(async (req, res) => {
    const category = await this.categoryService.updateCategoryPriority(
      req.params.categoryId,
      req.body.priority
    );
    const log = {
      admin: req.admin._id,
      log: `${req.admin.name} updated category  priority to ${category.priority}  `,
    };
    this.adminLogService.createLog(log);
    res.json({ success: true, category });
  });

  removeSubcategory = AsyncHandler(async (req, res) => {
    const category = await this.categoryService.removeSubcategory(
      req.params.categoryId,
      req.body.subCatIds
    );
    const log = {
      admin: req.admin._id,
      log: `${req.admin.name} removed some sub categories from ${category.title} category. `,
    };
    this.adminLogService.createLog(log);
    res.json({ success: true, category });
  });

  deleteCategory = AsyncHandler(async (req, res) => {
    const category = await this.categoryService.deleteCategory(
      req.params.categoryId
    );
    const log = {
      admin: req.admin._id,
      log: `${req.admin.name} ${
        category.active ? "deleted" : "restored"
      } category ${category.title} `,
    };
    this.adminLogService.createLog(log);
    res.json({ success: true, category });
  });
}

export class SubCategoryController {
  subCategoryService = new SubCategoryService();
  categoryService = new CategoryService();
  adminLogService = new AdminLogService();

  createSubCategory = AsyncHandler(async (req, res) => {
    const payload = {
      ...req.body,
      createdBy: req.admin._id,
      categoryId: req.params.categoryId,
    };
    const subCategory = await this.subCategoryService.createSubCategory(
      payload
    );
    await this.categoryService.addSubCategories(
      req.params.categoryId,
      subCategory._id
    );
    const log = {
      admin: req.admin._id,
      log: `${req.admin.name} created new subcategory ${req.body.name}  `,
    };
    await this.adminLogService.createLog(log);
    res.status(201).json({ success: true, subCategory });
  });

  updateSubCategory = AsyncHandler(async (req, res) => {
    const subCategory = await this.subCategoryService.updateSubCategory(
      req.params.categoryId,
      req.body
    );
    const log = {
      admin: req.admin._id,
      log: `${req.admin.name} updated details of subcategory  ${subCategory.name}  `,
    };
    await this.adminLogService.createLog(log);
    res.json({ success: true, subCategory });
  });

  getSubCategoryDetails = AsyncHandler(async (req, res) => {
    const subCategory = await this.subCategoryService.getSubCategoryDetails(
      req.params.categoryId
    );
    res.json({ success: true, subCategory });
  });

  getAllSubCategories = AsyncHandler(async (req, res) => {
    const allSubCategories = await this.subCategoryService.getAllSubCategories(
      req.query
    );
    res.json({ success: true, ...allSubCategories });
  });

  getAllSubCategoriesList = AsyncHandler(async (req, res) => {
    const allSubCategories =
      await this.subCategoryService.getAllSubCategoriesList();
    res.json({ success: true, allSubCategories });
  });

  deleteSubCategory = AsyncHandler(async (req, res) => {
    const subcategory = await this.subCategoryService.deleteSubCategory(
      req.params.categoryId
    );
    const log = {
      admin: req.admin._id,
      log: `${req.admin.name} deleted subcategory ${subcategory.name} `,
    };
    await this.adminLogService.createLog(log);

    res.json({ success: true, subcategory });
  });
}
