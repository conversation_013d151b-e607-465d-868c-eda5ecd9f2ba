import AsyncHandler from 'express-async-handler'
import { QuickAccessService } from '../services/quickAccess.js'

export class QuickAccessController {
	quickAccessService = new QuickAccessService()

	createQuickAccess = AsyncHandler(async (req, res) => {
		const payload = {
			...req.body,
			createdBy: req.admin._id,
		}
		const quickAccess = await this.quickAccessService.createQuickAccess(payload)
		res.status(201).json({ success: true, quickAccess })
	})

	getAllQuickAccess = AsyncHandler(async (req, res) => {
		const allQuickAccess = await this.quickAccessService.getAllQuickAccess(
			req.query,
		)
		res.json({ success: true, ...allQuickAccess })
	})
	getQuickAccessDetails = AsyncHandler(async (req, res) => {
		const quickAccessDetails =
			await this.quickAccessService.getQuickAccessDetails(
				req.params.quickAccessId,
			)
		res.json({ success: true, quickAccessDetails })
	})
	updateQuickAccess = AsyncHandler(async (req, res) => {
		const quickAccess = await this.quickAccessService.updateQuickAccess(
			req.params.quickAccessId,
			req.body,
		)
		res.json({ success: true, quickAccess })
	})
	updateActiveStatusQuickAccess = AsyncHandler(async (req, res) => {
		const quickAccess =
			await this.quickAccessService.updateActiveStatusQuickAccess(
				req.params.quickAccessId,
			)
		res.json({ success: true, quickAccess })
	})
	deleteQuickAccess = AsyncHandler(async (req, res) => {
		await this.quickAccessService.deleteQuickAccess(req.params.quickAccessId)
		res.json({ success: true, quickAccessId: req.params.quickAccessId })
	})
}
