import <PERSON><PERSON><PERSON>and<PERSON> from 'express-async-handler'
import Jwt from 'jsonwebtoken'
import { HttpException } from '../../exceptions/httpException.js'
import AdminToken from '../../models/admin/adminToken.js'
import { AuthenticationService } from '../../services/authentication.js'
import { verifyRefreshToken } from '../../utils/jwt.js'

// admin login with email and password verification
export class AuthController {
	authService = new AuthenticationService()
	adminLogin = AsyncHandler(async (req, res) => {
		const result = await this.authService.adminLogin(
			req.body.email,
			req.body.password,
		)
		res.json({ success: true, ...result })
	})
	refreshToken = AsyncHandler(async (req, res) => {
		if (!req.body.refreshToken) {
			throw new HttpException(401, 'please login to access the token')
		}
		const result = await this.authService.refreshToken(req.body.refreshToken)
		res.json({ success: true, ...result })
	})
}

const refreshToken = async (req, res) => {
	try {
		const oldToken = req.body.refreshToken
		if (!oldToken) {
			return res
				.status(400)
				.json({ success: false, errors: 'please login to access the token' })
		}
		const tokenData = await AdminToken.findOne({ token: oldToken })

		if (!tokenData) {
			return res.status(403).json({
				success: false,
				errors: 'jwt token not found! please login to access ',
			})
		}

		if (!tokenData?.session) {
			await AdminToken.updateMany(
				{ admin: tokenData.admin },
				{
					$set: { session: false },
				},
			)
			return res.status(200).json({
				success: false,
				token_hack: true,
				errors: 'refresh token misused from invalid user',
			})
		}
		verifyRefreshToken(oldToken)
			.then(async ({ tokenDetails }) => {
				await AdminToken.updateOne(
					{ token: oldToken },
					{
						$set: { session: false },
					},
				)
				const payload = {
					_id: tokenDetails._id,
					name: tokenDetails.name,
					role: tokenDetails.role,
				}
				const accessToken = Jwt.sign(
					payload,
					process.env.ACCESS_TOKEN_PRIVATE_KEY,
					{ expiresIn: '1m' },
				)
				const refreshToken = Jwt.sign(
					payload,
					process.env.REFRESH_TOKEN_PRIVATE_KEY,
					{ expiresIn: '1d' },
				)
				await AdminToken.create({
					admin: tokenDetails._id,
					token: refreshToken,
				})
				res.status(200).json({
					success: true,
					errors: false,
					message: 'Access token created successfully',
					accessToken,
					refreshToken,
				})
			})
			.catch(error => {
				return res.status(400).json({ success: false, errors: error.message })
			})
	} catch (error) {
		return res.status(500).json({ success: false, errors: error.message })
	}
}
// logout admin
const logout = async (req, res) => {
	try {
		res.status(204).json({ success: true })
	} catch (error) {
		res.status(500).json({ success: false, errors: error.message })
	}
}

export { logout, refreshToken }
