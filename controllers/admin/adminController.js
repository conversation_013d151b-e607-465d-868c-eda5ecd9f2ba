import AsyncHandler from 'express-async-handler'
import { AdminService } from '../../services/admin.js'
import { AdminLogService } from '../../services/adminLog.js'

export class AdminController {
	AdminService = new AdminService()
	AdminLogService = new AdminLogService()

	createAdmin = AsyncHandler(async (req, res) => {
		const admin = await this.AdminService.createAdmin(req.body)
		// const log = {
		// 	admin: req.admin._id,
		// 	log: `${req.admin.name} created new admin ${req.body.name} for ${req.body.role} role `,
		// }
		// await this.AdminLogService.createLog(log)
		res.status(201).json({
			success: true,
			admin,
		})
	})

	getAdminDetails = AsyncHandler(async (req, res) => {
		const admin = await this.AdminService.getAdminDetailsWithId(
			req.params.adminId,
		)
		res.json({ success: true, admin })
	})

	updateAdminDetails = AsyncHandler(async (req, res) => {
		const admin = await this.AdminService.updateAdminDetails(
			req.body,
			req.params.adminId,
		)
		const log = {
			admin: req.admin._id,
			log: `${req.admin.name} updated admin details of ${admin.name} `,
		}
		await this.AdminLogService.createLog(log)
		res.json({ success: true, admin })
	})

	updateAdminPassword = AsyncHandler(async (req, res) => {
		const admin = await this.AdminService.updateAdminPassword(
			req.body,
			req.params.adminId,
		)
		const log = {
			admin: req.admin._id,
			log: `${req.admin.name} updated admin details of ${admin.name} `,
		}
		await this.AdminLogService.createLog(log)
		res.json({ success: true, admin })
	})

	resetAdminPassword = AsyncHandler(async (req, res) => {
		console.log("🚀 ~ AdminController ~ resetAdminPassword=AsyncHandler ~ req.body:", req.body)
		const admin = await this.AdminService.resetAdminPassword(req.body)
		const log = {
			admin: req.admin._id,
			log: `${req.admin.name} updated ${admin.name}'s password `,
		}
		await this.AdminLogService.createLog(log)
		res.json({ success: true })
	})

	updateAdminRole = AsyncHandler(async (req, res) => {
		const admin = await this.AdminService.updateAdminRole(
			req.params.adminId,
			req.body.role,
		)
		const log = {
			admin: req.admin._id,
			log: `${req.admin.name} changed admin ${admin.name}'s to ${req.body.role} role `,
		}
		await this.AdminLogService.createLog(log)
		res.json({ success: true, admin })
	})

	updateAdminActiveStatus = AsyncHandler(async (req, res) => {
		const admin = await this.AdminService.updateAdminActiveStatus(
			req.params.adminId,
		)
		const log = {
			admin: req.admin._id,
			log: `${req.admin.name} ${admin.block ? 'unblocked' : 'blocked'} ${admin.name
				} `,
		}
		await this.AdminLogService.createLog(log)

		res.json({ success: true, admin })
	})

	getAllAdminsList = AsyncHandler(async (req, res) => {
		const allAdmins = await this.AdminService.getAllAdminList()
		res.json({ success: true, allAdmins })
	})

	getAllAdmins = AsyncHandler(async (req, res) => {
		const allAdmins = await this.AdminService.getAllAdmins(req.query)
		res.json({ success: true, ...allAdmins })
	})

	getAllAdminLogs = AsyncHandler(async (req, res) => {
		const allAdminLogs = await this.AdminLogService.getAllLogs(req.query)
		res.json({ success: true, ...allAdminLogs })
	})

	deleteAdmin = AsyncHandler(async (req, res) => {
		const admin = await this.AdminService.getAdminDetailsWithId(
			req.params.adminId,
		)
		const log = {
			admin: req.admin._id,
			log: `${req.admin.name} permanently deleted ${admin.name}'s account with unique id of ${admin._id} and ${admin.uid} `,
		}
		await this.AdminService.deleteAdmin(req.params.adminId)
		await this.AdminLogService.createLog(log)

		res.json({ success: true, admin })
	})
}
