import AsyncHandler from 'express-async-handler'
import { StoreReviews } from '../../models/stores/reviews.js'
import { AdminLogService } from '../../services/adminLog.js'
import { StoreService } from '../../services/store.js'
import { StoreReviewService } from '../../services/storeReview.js'

export class StoreReviewController {
	storeReviewService = new StoreReviewService()
	storeService = new StoreService()
	adminLogService = new AdminLogService()

	createStoreReview = AsyncHandler(async (req, res) => {
		await this.storeService.getStoreDetails(req.params.storeId)
		const payload = {
			...req.body,
			storeId: req.params.storeId,
			reviewer: req.user._id,
		}
		const storeReview = await this.storeReviewService.createStoreReview(payload)
		res.status(201).json({ success: true, storeReview })
	})

	getAllStoreReviews = AsyncHandler(async (req, res) => {
		const allStoreReviews = await this.storeReviewService.getAllStoreReviews(
			req.query,
		)
		res.json({ success: true, ...allStoreReviews })
	})

	updateStoreReview = AsyncHandler(async (req, res) => {
		const storeReview = await this.storeReviewService.updateStoreReview(
			req.params.reviewId,
			req.body,
		)
		res.json({ success: true, storeReview })
	})

	updateStoreReviewActiveStatus = AsyncHandler(async (req, res) => {
		const storeReview =
			await this.storeReviewService.updateStoreReviewActiveStatus(
				req.params.reviewId,
			)
		const log = {
			admin: req.admin._id,
			log: `${req.admin.name}  ${storeReview.active ? 'enabled' : 'disabled'
				}  store review.  `,
		}
		await this.adminLogService.createLog(log)
		res.json({ success: true, storeReview })
	})

	permanentDeleteStoreReview = AsyncHandler(async (req, res) => {
		await this.storeReviewService.permanentDeleteStoreReview(
			req.params.reviewId,
		)
		res.json({ success: true })
	})
}

// filter reviews
const filterReviews = async (req, res) => {
	try {
		const { store, page, frange, lrange, rating } = req.query
		const filter = {}
		const sort = { createdAt: -1 }
		if (frange && lrange) {
			filter.createdAt = { $gte: lrange, $lte: frange }
			sort.createdAt = 1
		}
		if (store) {
			filter.storeId = store
		}
		if (rating) {
			filter.rating = rating
		}
		const currentPage = Number(page) || 1
		const skip = 20 * (currentPage - 1)
		const count = await StoreReviews.countDocuments(filter, { sort })
		const reviews = await StoreReviews.find(filter)
			.sort(sort)
			.populate('storeId', 'name')
			.populate('reviewer', 'name')
			.limit(20)
			.skip(skip)

		const maxPages = Math.ceil(count / 20)
		res.status(200).json({ success: true, reviews, maxPages })
	} catch (error) {
		res.status(500).json({ success: false, errors: error.message })
	}
}
