import AsyncHandler from 'express-async-handler'
import { StoreCategoryHistoryService } from '../../services/storeHistory.js'

// filter all store categories histories
export class StoreCategoryHistoryController {
	storeCategoryHistoryService = new StoreCategoryHistoryService()

	getAllStoreCategoryHistory = AsyncHandler(async (req, res) => {
		const allStoreCategoryHistory =
			await this.storeCategoryHistoryService.getAllCategoryHistory(req.query)

		res.json({ success: true, ...allStoreCategoryHistory })
	})
}
// const filterCategories = async (req, res) => {
//   try {
//     const {
//       page,
//       keyword,
//       name,
//       storeId,
//       lowRate,
//       highRate,
//       app,
//       website,
//       startDate,
//       endDate,
//     } = req.query;
//     let filter = {};
//     let sort = { createdAt: -1 };
//     if (keyword) {
//       filter.name = {
//         $regex: keyword,
//         $options: "i",
//       };
//     }
//     if (name === "true") {
//       sort.name = 1;
//     }
//     if (lowRate === "true") {
//       sort.newUserCb = 1;
//     }
//     if (highRate === "true") {
//       sort.newUserCb = -1;
//     }
//     if (storeId) {
//       filter.storeId = storeId;
//     }
//     if (app === "true") {
//       filter.device = "app";
//     }
//     if (website === "true") {
//       filter.device = "website";
//     }

//     if (startDate && endDate) {
//       filter.createdAt = { $gte: startDate, $lte: endDate };
//     }
//     const currentPage = Number(page) || 1;
//     const skip = 20 * (currentPage - 1);
//     const count = await StoreCategoryHistory.countDocuments(filter, { sort });
//     const histories = await StoreCategoryHistory.find(filter)
//       .sort(sort)
//       .populate("affiliation", "name")
//       .populate("storeId", "name")
//       .populate("createdBy", "name")
//       .limit(20)
//       .skip(skip);
//     const maxPages = Math.ceil(count / 20);
//     res.status(200).json({ success: true, histories, maxPages });
//   } catch (error) {
//     res.status(500).json({ success: false, error: error.message });
//   }
// };
