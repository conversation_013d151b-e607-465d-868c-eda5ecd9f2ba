import Async<PERSON>and<PERSON> from "express-async-handler";
import { AdminLog } from "../../models/admin/adminLog.js";
import { StoreLog } from "../../models/stores/log.js";
import { Store } from "../../models/stores/store.js";
import { AdminLogService } from "../../services/adminLog.js";
import { StoreService } from "../../services/store.js";
import { StoreLogService } from "../../services/storeLog.js";

export class StoreController {
  storeService = new StoreService();
  storeLogService = new StoreLogService();
  adminLogService = new AdminLogService();

  createStore = AsyncHandler(async (req, res) => {
    const payload = {
      ...req.body,
      createdBy: req.admin._id,
    };
    const store = await this.storeService.createStore(payload);
    const storeLog = {
      updateBy: req.admin._id,
      storeId: store._id,
      updatedContent: `${req.admin.name} has established a new store named ${store.name}.`,
    };

    await this.storeLogService.createStoreLog(storeLog);
    const log = {
      admin: req.admin._id,
      log: `${req.admin.name} created new store ${store.name} `,
    };
    await this.adminLogService.createLog(log);
    res.status(201).json({ success: true, store });
  });

  updateStore = AsyncHandler(async (req, res) => {
    const store = await this.storeService.updateStore(
      req.params.storeId,
      req.body
    );

    const storeLog = {
      updateBy: req.admin._id,
      storeId: store._id,
      updatedContent: `${req.admin.name} has updated multiple details of ${store.name} store.`,
    };

    await this.storeLogService.createStoreLog(storeLog);
    const log = {
      admin: req.admin._id,
      log: `${req.admin.name} updated ${store.name} store details `,
    };
    await this.adminLogService.createLog(log);
    res.json({ success: true, store });
  });

  getAllStores = AsyncHandler(async (req, res) => {
    const allStores = await this.storeService.getAllStores(req.query);
    res.json({ success: true, ...allStores });
  });

  getAllStoresList = AsyncHandler(async (req, res) => {
    const allStores = await this.storeService.getAllStoresList();
    res.json({ success: true, allStores });
  });

  getStoreDetails = AsyncHandler(async (req, res) => {
    const storeDetails = await this.storeService.getStoreDetails(
      req.params.storeId
    );
    res.json({ success: true, storeDetails });
  });

  updateStoreDeleteStatus = AsyncHandler(async (req, res) => {
    const store = await this.storeService.updateStoreDeleteStatus(
      req.params.storeId,
      req.admin._id
    );
    const storeLog = {
      updateBy: req.admin._id,
      storeId: store._id,
      updatedContent: `${req.admin.name} updated deleted status to ${
        store.isDeleted ? "enabled" : "disabled"
      } of ${store.name} store.`,
    };

    await this.storeLogService.createStoreLog(storeLog);
    const log = {
      admin: req.admin._id,
      log: `${req.admin.name} updated ${store.name} store deleted status. `,
    };
    await this.adminLogService.createLog(log);
    res.json({ success: true, store });
  });

  updateStoreActiveStatus = AsyncHandler(async (req, res) => {
    const store = await this.storeService.updateStoreActiveStatus(
      req.params.storeId,
      req.admin._id
    );
    const storeLog = {
      updateBy: req.admin._id,
      storeId: store._id,
      updatedContent: `${req.admin.name} updated active status to ${
        store.active ? "enabled" : "disabled"
      } of ${store.name} store.`,
    };

    await this.storeLogService.createStoreLog(storeLog);
    const log = {
      admin: req.admin._id,
      log: `${req.admin.name} updated ${store.name} store active status. `,
    };
    await this.adminLogService.createLog(log);
    res.json({ success: true, store });
  });

  updateStorePriority = AsyncHandler(async (req, res) => {
    const store = await this.storeService.updateStorePriority(
      req.params.storeId,
      req.body.priority,
      req.admin._id
    );
    const storeLog = {
      updateBy: req.admin._id,
      storeId: store._id,
      updatedContent: `${req.admin.name} updated priority  to ${req.body.priority} of ${store.name} store.`,
    };

    await this.storeLogService.createStoreLog(storeLog);
    const log = {
      admin: req.admin._id,
      log: `${req.admin.name} updated ${store.name} store priority. `,
    };
    await this.adminLogService.createLog(log);
    res.json({ success: true, store });
  });

  updateAutoCheckStatus = AsyncHandler(async (req, res) => {
    const store = await this.storeService.updateAutoCheckStatus(
      req.params.storeId,
      req.admin._id
    );
    const storeLog = {
      updateBy: req.admin._id,
      storeId: store._id,
      updatedContent: `${req.admin.name} updated auto track feature to ${
        store.autoCheck ? "enabled" : "disabled"
      } of ${store.name} store.`,
    };

    await this.storeLogService.createStoreLog(storeLog);
    const log = {
      admin: req.admin._id,
      log: `${req.admin.name} updated ${store.name} store auto check status. `,
    };
    await this.adminLogService.createLog(log);
    res.json({ success: true, store });
  });

  updateStoreDeepLinkStatus = AsyncHandler(async (req, res) => {
    const store = await this.storeService.updateStoreDeepLinkStatus(
      req.params.storeId,
      req.admin._id
    );

    const storeLog = {
      updateBy: req.admin._id,
      storeId: store._id,
      updatedContent: `${req.admin.name} updated deep link status status to ${
        store.deepLinkEnable ? "enabled" : "disabled"
      } of ${store.name} store.`,
    };

    await this.storeLogService.createStoreLog(storeLog);
    const log = {
      admin: req.admin._id,
      log: `${req.admin.name} updated ${store.name} store deep link status. `,
    };
    await this.adminLogService.createLog(log);
    res.json({ success: true, store });
  });

  updateStatus = AsyncHandler(async (req, res) => {
    const store = await this.storeService.updateStatus(
      req.params.storeId,
      req.admin._id
    );

    const storeLog = {
      updateBy: req.admin._id,
      storeId: store._id,
      updatedContent: `${req.admin.name} updated deep link status status to ${
        store.deepLinkEnable ? "enabled" : "disabled"
      } of ${store.name} store.`,
    };

    await this.storeLogService.createStoreLog(storeLog);
    const log = {
      admin: req.admin._id,
      log: `${req.admin.name} updated ${store.name} store deep link status. `,
    };
    await this.adminLogService.createLog(log);
    res.json({ success: true, store });
  });

  updateIsSpecialLimit = AsyncHandler(async (req, res) => {
    const store = await this.storeService.updateIsSpecialLimit(
      req.params.storeId,
      req.body.isSpecial,
      req.admin._id
    );
    const storeLog = {
      updateBy: req.admin._id,
      storeId: store._id,
      updatedContent: `${req.admin.name} updated special limit  to ${req.body.isSpecial} of ${store.name} store.`,
    };

    await this.storeLogService.createStoreLog(storeLog);
    const log = {
      admin: req.admin._id,
      log: `${req.admin.name} updated ${store.name} store special limit . `,
    };
    await this.adminLogService.createLog(log);
    res.json({ success: true, store });
  });
}
// create new store
const createStore = async (req, res) => {
  try {
    const newStoreData = req.body;
    newStoreData.createdBy = req.admin._id;

    const allStores = await Store.find()
      .populate("offerId", "dateExpiry cashbackTitle active")
      .populate("affiliation", "name")
      .populate("updatedBy", "name ");

    newStoreData.no = allStores.length + 1;
    const store = await Store.create(newStoreData);
    const logData = {
      updateBy: req.admin._id,
      storeId: store._id,
      updatedContent: `${req.admin.name} has established a new store named ${store.name}.`,
    };
    await StoreLog.create(logData);
    const log = {
      admin: req.admin._id,
      log: `${req.admin.name} created new store ${store.name} `,
    };
    await AdminLog.create(log);
    res.status(200).json({
      success: true,
      store,
    });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};

// get single store
const getStore = async (req, res) => {
  try {
    const { storeId } = req.params;
    const store = await Store.findById(storeId)
      .populate("offer", "dateExpiry cashbackTitle active")
      .populate("affiliation", "name")
      .populate("updatedBy", "name ");
    if (!store) {
      return res.status(404).json({ success: false, error: "Store not found" });
    }
    res.status(200).json({
      success: true,
      store,
    });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};

const getAllStoresList = async (req, res) => {
  try {
    const allStores = await Store.find({}, "name active count");
    res.status(200).json({ success: true, allStores });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};
// get admin based stores

const getAdminBasedStores = async (req, res) => {
  try {
    const { adminId } = req.params;
    const stores = await Store.find({ createdBy: adminId })
      .populate("offerId", "dateExpiry cashbackTitle active")
      .populate("affiliation", "name")
      .populate("updatedBy", "name ");
    res.status(200).json({ success: true, stores });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};

// get all stores
const getAllStores = async (req, res) => {
  try {
    const query = Store.find({ active: true })
      .populate("offerId", "dateExpiry cashbackTitle active")
      .populate("affiliation", "name")
      .populate("updatedBy", "name");

    if (req.query.instant === "active") {
      query.where("name").regex(/instant/i);
    }
    const totalItems = await Store.countDocuments(query);
    const maxPages = Math.ceil(totalItems / 20);

    const stores = await query.sort({ createdAt: -1 }).limit(20).exec();

    const activeStCount = await Store.countDocuments({ active: true });
    const inactiveStCount = await Store.countDocuments({ active: false });
    const allStoresCount = await Store.countDocuments();
    const instantActiveCount = await Store.countDocuments({
      name: /instant/i,
      active: true,
    });
    const instantInactiveCount = await Store.countDocuments({
      name: /instant/i,
      active: false,
    });

    res.status(200).json({
      success: true,
      stores,
      activeStCount,
      inactiveStCount,
      allStoresCount,
      instantActiveCount,
      instantInactiveCount,
      maxPages,
    });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};

// update store details
const updateStores = async (req, res) => {
  try {
    const { storeId } = req.params;
    const data = req.body;
    data.updatedBy = req.admin._id;
    const store = await Store.findById(storeId);
    if (!store) {
      return res
        .status(404)
        .json({ success: false, error: "store not found!" });
    }
    await Store.updateOne({ _id: storeId }, { $set: data });
    const log = {
      admin: req.admin._id,
      log: `${req.admin.name}  updated multiple details  ${store.name} store  `,
    };
    await AdminLog.create(log);
    res.status(200).json({ success: true });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};

// change auto track status
const changeAutoTrackStatus = async (req, res) => {
  try {
    const { storeId } = req.params;
    const store = await Store.findById(storeId);
    if (!store) {
      return res.status(404).json({ success: false, error: "store not found" });
    }
    await Store.updateOne(
      { _id: storeId },
      { $set: { autoCheck: !store.autoCheck } }
    );
    const log = {
      admin: req.admin._id,
      log: `${req.admin.name}  updated ${store.name}s auto check status ${
        store.autoCheck ? "enabled" : "disabled"
      } `,
    };
    await AdminLog.create(log);

    res.status(200).json({ success: true, message: "updated successfully!" });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};

// change store priority
const changePriority = async (req, res) => {
  try {
    const { storeId } = req.params;
    const { priority } = req.body;
    const store = await Store.findById(storeId);
    if (!store) {
      return res.status(404).json({ success: false, error: "store not found" });
    }
    await Store.updateOne(
      { _id: storeId },
      { $set: { priority: priority, updatedBy: req.admin._id } }
    );
    const log = {
      admin: req.admin._id,
      log: `${req.admin.name} changed priority of ${store.name} from ${store.priority} to ${priority} `,
    };
    await AdminLog.create(log);
    res.status(200).json({ success: true, message: "updated successfully!" });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};

// change deep link status enable and disable
const changeDeepLinkStatus = async (req, res) => {
  try {
    const { storeId } = req.params;
    const store = await Store.findById(storeId);
    if (!store) {
      return res.status(404).json({ success: false, error: "store not found" });
    }
    await Store.updateOne(
      { _id: storeId },
      {
        $set: {
          deepLinkEnable: !store.deepLinkEnable,
          updatedBy: req.admin._id,
        },
      }
    );
    const log = {
      admin: req.admin._id,
      log: `${req.admin.name} changed deep link status of ${store.name} store ${
        store.deepLinkEnable ? "disabled" : " enabled"
      } `,
    };
    await AdminLog.create(log);
    res.status(200).json({ success: true, message: "updated successfully!" });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};

// change is special value
const changeIsSpecialLimit = async (req, res) => {
  try {
    const { storeId } = req.params;
    const { value } = req.body;
    const store = await Store.findById(storeId);
    if (!store) {
      return res.status(404).json({ success: false, error: "store not found" });
    }
    await Store.updateOne(
      { _id: storeId },
      { $set: { isSpecial: value, updatedBy: req.admin._id } }
    );
    const log = {
      admin: req.admin._id,
      log: `${req.admin.name} changed  special limit  status of ${
        store.name
      } store ${store.deepLinkEnable ? "disabled" : " enabled"} `,
    };
    await AdminLog.create(log);
    res.status(200).json({ success: true, message: "updated successfully!" });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};

// filter store
const filterStores = async (req, res) => {
  try {
    const {
      page,
      keyword,
      partner,
      campType,
      offers,
      status,
      actSt,
      inacSt,
      instSt,
      inacInstSt,
      lowOffer,
      hiOffer,
      lowPriority,
      hiPriority,
      name,
      lastUpdated,
      storeId,
    } = req.query;
    const initialData = {};
    if (partner) {
      initialData.affiliation = partner;
    }

    if (campType) {
      initialData.campaignType = campType?.toLocaleLowerCase();
    }

    if (actSt === "true") {
      initialData.active = true;
    }

    if (inacSt === "true") {
      initialData.active = false;
    }

    if (storeId) {
      initialData._id = storeId;
    }
    if (keyword) {
      initialData.name = {
        $regex: keyword,
        $options: "i",
      };
    }

    if (instSt === "true") {
      initialData.name = {
        $regex: "instant",
        $options: "i",
      };
      initialData.active = true;
    }
    if (inacInstSt === "true") {
      initialData.name = {
        $regex: "instant",
        $options: "i",
      };
      initialData.active = false;
    }
    const sort = { createdAt: -1 };
    if (lowPriority === "true") {
      sort.priority = 1;
    }
    if (hiPriority === "true") {
      sort.priority = -1;
    }
    if (name === "true") {
      sort.name = 1;
    }
    if (lastUpdated === "true") {
      sort.updatedAt = -1;
    }
    let stores = await Store.find(initialData)
      .sort(sort)
      .populate("offerId", "dateExpiry cashbackTitle active")
      .populate("affiliation", "name")
      .populate("updatedBy", "name ");

    if (status === 0) {
      const data = [];
      stores.map((item) => {
        item.offerId.map((ofr) => {
          if (ofr.active) {
            data.push(item);
          }
        });
      });
      stores = data;
    }
    if (status === 1) {
      const data = [];
      stores.map((item) => {
        item.offerId.map((offr) => {
          if (!offr.active) {
            data.push(item);
          }
        });
      });
      stores = data;
    }
    const currentPage = Number(page) || 1;
    const startIndex = (currentPage - 1) * 20;
    const endIndex = startIndex + 20;

    if (offers === 0) {
      stores = stores.filter((item) => item?.offerId?.length === 0);
      if (lowOffer === "true") {
        stores.sort((a, b) => a.offerId.length - b.offerId.length);
      }
      if (hiOffer === "true") {
        stores.sort((a, b) => b.offerId.length - a.offerId.length);
      }
      const totalItems = stores.length;
      const maxPages = Math.ceil(totalItems / 20);
      stores = stores.slice(startIndex, endIndex);
      return res.status(200).json({ success: true, stores, maxPages });
    }
    if (offers === 1) {
      stores = stores.filter((item) => item?.offerId?.length === 1);

      if (lowOffer === "true") {
        stores.sort((a, b) => a.offerId.length - b.offerId.length);
      }
      if (hiOffer === "true") {
        stores.sort((a, b) => b.offerId.length - a.offerId.length);
      }
      const totalItems = stores.length;
      const maxPages = Math.ceil(totalItems / 20);
      stores = stores.slice(startIndex, endIndex);
      return res.status(200).json({ success: true, stores, maxPages });
    }

    if (lowOffer === "true") {
      stores.sort((a, b) => a.offerId.length - b.offerId.length);
    }
    if (hiOffer === "true") {
      stores.sort((a, b) => b.offerId.length - a.offerId.length);
    }
    const totalItems = stores.length;
    const maxPages = Math.ceil(totalItems / 20);
    stores = stores.slice(startIndex, endIndex);
    const activeStCount = await Store.countDocuments({ active: true });
    const inactiveStCount = await Store.countDocuments({ active: false });
    const allStoresCount = await Store.countDocuments();
    const instantActiveCount = await Store.countDocuments({
      name: /instant/i,
      active: true,
    });
    const instantInactiveCount = await Store.countDocuments({
      name: /instant/i,
      active: false,
    });

    res.status(200).json({
      success: true,
      stores,
      activeStCount,
      inactiveStCount,
      allStoresCount,
      instantActiveCount,
      instantInactiveCount,
      maxPages,
    });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};

// get all stores including active, inactive(deleted),inactive instant,active instant
const allStores = async (req, res) => {
  try {
    const {
      orders,
      priority,
      name,
      updated,
      status,
      offers,
      affiliation,
      campaignType,
      expiredHomeOffer,
      search,
    } = req.query;
    const pageSize = Number(req.query.pageSize) || 10;
    const page = Number(req.query.page) || 1;
    const sort = { createdAt: -1 };
    const keyword = search
      ? {
          $or: [
            {
              name: {
                $regex: search,
                $options: "i",
              },
            },
          ],
        }
      : {};
    if (name) {
      if (name === 1) {
        sort.name = 1;
      }
      if (name === 0) {
        sort.name = -1;
      }
    }
    if (updated) {
      if (updated === 1) {
        sort.updatedAt = 1;
      }
      if (updated === 0) {
        sort.updatedAt = -1;
      }
    }
    if (affiliation) {
      keyword.affiliation = affiliation;
    }
    if (campaignType) {
      keyword.campaignType = campaignType;
    }
    if (priority) {
      if (priority === 1) {
        sort.priority = 1;
      }
      if (priority === 0) {
        sort.priority = -1;
      }
    }
    const allStores = await Store.find({})
      .populate("offerId", "dateExpiry cashbackTitle active")
      .populate("affiliation", "name")
      .populate("updatedBy", "name")
      .sort(sort);

    res.status(200).json({ success: true, allStores, page, pageSize });
  } catch (error) {
    console.log(error);
    res.status(500).json({ success: false, error: error.message });
  }
};

const searchStores = async (req, res) => {
  try {
    const { page, keyword } = req.query;
    const stores = await Store.find({
      name: {
        $regex: keyword,
        $options: "i",
      },
    });
    res.status(200).json({ success: true, stores });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};

// delete store
const deleteStore = async (req, res) => {
  try {
    const { storeId } = req.params;
    const store = await Store.findById(storeId);
    if (!store) {
      return res.status(404).json({ success: false, error: "store not found" });
    }
    await Store.updateOne(
      { _id: storeId },
      { $set: { active: !store.active, updatedBy: req.admin._id } }
    );
    const log = {
      admin: req.admin._id,
      log: `${req.admin.name} ${store.active ? "deleted" : "restored"} ${
        store.name
      } store `,
    };
    await AdminLog.create(log);
    res.status(200).json({ success: true, message: "updated successfully!" });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};

export {
  createStore,
  getStore,
  getAllStores,
  getAdminBasedStores,
  changeAutoTrackStatus,
  changePriority,
  deleteStore,
  changeDeepLinkStatus,
  changeIsSpecialLimit,
  filterStores,
  allStores,
  searchStores,
  updateStores,
  getAllStoresList,
};
