import AsyncHand<PERSON> from "express-async-handler";
import { AdminLogService } from "../../services/adminLog.js";
import { StoreCategoryService } from "../../services/storeCategory.js";
import { StoreCategoryHistoryService } from "../../services/storeHistory.js";

export class StoreCategoryController {
  storeCategoryService = new StoreCategoryService();
  AdminLogService = new AdminLogService();
  StoreCategoryHistoryService = new StoreCategoryHistoryService();

  createStoreCategory = AsyncHandler(async (req, res) => {
    const payload = {
      ...req.body,
      createdBy: req.admin._id,
    };
    const storeCategory = await this.storeCategoryService.createStoreCategory(
      payload
    );

    const storeCategoryHistory =
      await this.StoreCategoryHistoryService.createStoreCategoryHistory(
        storeCategory
      );

    const logs = [
      {
        admin: req.admin._id,
        log: `${req.admin.name}  created new  store category name ${storeCategory.name} `,
      },
      {
        admin: req.admin._id,
        log: `${req.admin.name}  created new  store category history name ${storeCategory.name} and id of ${storeCategoryHistory.count} `,
      },
    ];
    await this.AdminLogService.createMultipleLogs(logs);
    res.status(201).json({ success: true, storeCategory });
  });

  getAllStoreCategories = AsyncHandler(async (req, res) => {
    const allStoreCategories =
      await this.storeCategoryService.getAllStoreCategories(req.query);
    res.json({ success: true, ...allStoreCategories });
  });
  getAllStoreCategoryList = AsyncHandler(async (req, res) => {
    const allCategories =
      await this.storeCategoryService.getAllStoreCategoryList();
    res.json({ success: true, allCategories });
  });
  getStoreCategoryDetails = AsyncHandler(async (req, res) => {
    const storeCategory =
      await this.storeCategoryService.getStoreCategoryDetails(
        req.params.categoryId
      );
    res.json({ success: true, storeCategory });
  });
  updateStoreCategory = AsyncHandler(async (req, res) => {
    const payload = {
      ...req.body,
      updatedBy: req.admin._id,
    };
    const storeCategory = await this.storeCategoryService.updateStoreCategory(
      req.params.categoryId,
      payload
    );
    await this.StoreCategoryHistoryService.createStoreCategoryHistory({
      ...payload,
      active: false,
    });
    const logs = [
      {
        admin: req.admin._id,
        log: `${req.admin.name}  updated multiple details of  ${storeCategory.name} `,
      },
      {
        admin: req.admin._id,
        log: `${req.admin.name} created new store category history with old details of  ${storeCategory.name} `,
      },
    ];
    await this.AdminLogService.createMultipleLogs(logs);
    res.json({ success: true, storeCategory });
  });

  updateStoreCategoryActiveStatus = AsyncHandler(async (req, res) => {
    const storeCategory =
      await this.storeCategoryService.updateStoreCategoryActiveStatus(
        req.params.categoryId,
        req.admin._id
      );
    const historyPayload = storeCategory;
    await this.StoreCategoryHistoryService.createStoreCategoryHistory({
      ...historyPayload,
      active: false,
    });
    const logs = [
      {
        admin: req.admin._id,
        log: `${req.admin.name}  ${
          storeCategory.active ? "restored" : "deleted"
        }  store category ${storeCategory.name} `,
      },
      {
        admin: req.admin._id,
        log: `${req.admin.name}created new  store category history for ${storeCategory.name} and id of ${storeCategory.count} `,
      },
    ];
    await this.AdminLogService.createMultipleLogs(logs);
    res.json({ success: true, storeCategory });
  });
}
