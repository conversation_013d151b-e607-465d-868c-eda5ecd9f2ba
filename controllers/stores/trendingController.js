import AsyncHandler from 'express-async-handler'
import { AdminLogService } from '../../services/adminLog.js'
import { StoreService } from '../../services/store.js'
import { TrendingStoreService } from '../../services/trendingStore.js'

export class TrendingStoreController {
    trendingStoreService = new TrendingStoreService()
    adminLogService = new AdminLogService()
    storeService = new StoreService()

    createTrendingStore = AsyncHandler(async (req, res) => {

        const trendingStore = await this.trendingStoreService.createTrendingStore(
            req.body.storeId,
            {
                store: req.body.storeId,
                createdBy: req.admin._id.toString()
            }
        )
        const store = await this.storeService.updateStoreTrending(
            req.body.storeId,
            req.admin._id,
            true
        )
        const log = {
            admin: req.admin._id,
            log: `${req.admin.name} added a new store ${store.name} to trending store`,
        }
        await this.adminLogService.createLog(log)
        res.status(201).json({ success: true, trendingStore })
    })

    getAllTrendingStores = AsyncHandler(async (req, res) => {
        const allTrendingStores =
            await this.trendingStoreService.getAllTrendingStores(req.query)
        res.json({ success: true, ...allTrendingStores })
    })

    updateTrendingStoresPriority = AsyncHandler(async (req, res) => {
        const trendingStore =
            await this.trendingStoreService.updateTrendingStorePriority(
                req.params.storeId,
                req.body.priority,
            )
        const log = {
            admin: req.admin._id,
            log: `${req.admin.name} updated trending store priority to ${req.body.priority} of ${trendingStore.count} trending store`,
        }
        await this.adminLogService.createLog(log)
        res.json({ success: true, trendingStore })
    })
    deleteTrendingStore = AsyncHandler(async (req, res) => {
        await this.trendingStoreService.deleteTrendingStore(req.params.storeId)
        const store = await this.storeService.updateStoreTrending(
            req.params.storeId,
            req.admin._id,
            false
        )
        const log = {
            admin: req.admin._id,
            log: `${req.admin.name} updated trending store updated trending status ${store.trending ? 'enabled' : 'disabled'
                } `,
        }
        await this.adminLogService.createLog(log)
        res.json({ success: true })
    })
}
// const filterTrending = async (req, res) => {
//   try {
//     const { page, keyword } = req.query;
//     let filter = {};
//     let sort = { createdAt: -1 };
//     const count = await TrendingStores.countDocuments(filter, { sort });
//     const currentPage = Number(page) || 1;
//     const skip = 20 * (currentPage - 1);
//     const trendingStores = await TrendingStores.aggregate([
//       {
//         $lookup: {
//           from: "stores", // Name of the child collection
//           localField: "storeId", // Field in the parent collection
//           foreignField: "_id", // Field in the child collection
//           as: "storeId", // Name of the output array field
//         },
//       },
//       {
//         $lookup: {
//           from: "admins", // Name of the child collection
//           localField: "createdBy", // Field in the parent collection
//           foreignField: "_id", // Field in the child collection
//           as: "createdBy", // Name of the output array field
//         },
//       },
//       {
//         $match: {
//           "storeId.name": {
//             $regex: keyword, // Specify the keyword you want to match
//             $options: "i", // Use 'i' option for case-insensitive search
//           },
//         },
//       },
//     ])
//       .skip(skip)
//       .limit(20);
//     const maxPages = Math.ceil(count / 20);
//     res.status(200).json({ success: true, trendingStores, maxPages });
//   } catch (error) {
//     res.status(500).json({ success: false, error: error.message });
//   }
// };
