<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Retro Futuristic Calculator</title>
    <style>
        :root {
            --background: oklch(0.1000 0 0);
            --foreground: oklch(0.8000 0.2000 120);
            --card: oklch(0.1500 0 0);
            --card-foreground: oklch(0.8500 0.2000 120);
            --primary: oklch(0.7000 0.3000 300);
            --primary-foreground: oklch(1.0000 0 0);
            --input: oklch(0.2500 0 0);
            --accent: oklch(0.5000 0.2000 120);
            --border: oklch(0.3000 0 0);
            --font-mono: 'Space Mono', monospace;
        }
        
        body {
            background: var(--background) !important;
            color: var(--foreground) !important;
            font-family: var(--font-mono) !important;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
            margin: 0;
        }
        
        .crt-container {
            width: 320px;
            border-radius: 8px 8px 12px 12px;
            overflow: hidden;
            box-shadow: 0 0 25px var(--primary), 
                        inset 0 0 10px rgba(0,0,0,0.8);
            position: relative;
            border: 2px solid var(--border);
        }
        
        .crt-scanlines {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: repeating-linear-gradient(
                0deg,
                transparent,
                transparent 1px,
                rgba(0, 255, 0, 0.05) 1px,
                rgba(0, 255, 0, 0.05) 2px
            );
            pointer-events: none;
            animation: scan 1.2s linear infinite;
        }
        
        @keyframes scan {
            0% { background-position: 0 0; }
            100% { background-position: 0 100%; }
        }
        
        .display-header {
            background: var(--input);
            color: var(--accent);
            text-shadow: 0 0 5px var(--accent);
            letter-spacing: 1px;
            padding: 4px 0;
            text-align: center;
            font-size: 1.1rem;
            border-bottom: 1px solid var(--border);
        }
        
        .display-screen {
            background: var(--card);
            color: var(--card-foreground);
            text-shadow: 0 0 5px var(--accent);
            font-size: 2.2rem;
            padding: 16px;
            text-align: right;
            min-height: 80px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .keypad {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 1px;
            background: var(--input);
        }
        
        .key {
            background: var(--input);
            color: var(--foreground);
            border: none;
            padding: 16px 8px;
            font-family: var(--font-mono);
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.1s ease;
            position: relative;
            box-shadow: 
                inset 2px 2px 4px rgba(0,0,0,0.7),
                inset -2px -2px 4px var(--primary);
        }
        
        .key:hover {
            box-shadow: 
                inset 1px 1px 3px rgba(0,0,0,0.5),
                inset -1px -1px 3px var(--primary),
                0 0 8px var(--primary);
            transform: scale(1.05);
            z-index: 10;
        }
        
        .key:active {
            box-shadow: 
                inset 4px 4px 8px rgba(0,0,0,0.9),
                0 0 12px var(--primary);
            transform: scale(0.95);
        }
        
        .function-key {
            background: var(--input);
            color: var(--accent);
            text-shadow: 0 0 3px var(--accent);
        }
        
        .equals-key {
            background: var(--primary);
            color: var(--primary-foreground);
            font-weight: bold;
            text-shadow: 0 0 5px rgba(255,0,255,0.7);
        }
        
        .crt-curvature {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 8px 8px 12px 12px;
            pointer-events: none;
            box-shadow: inset 0 0 20px rgba(0,0,0,0.8);
            animation: curvature 1.5s infinite alternate;
        }
        
        @keyframes curvature {
            0% { border-radius: 8px 8px 12px 12px; }
            100% { border-radius: 10px 10px 15px 15px; }
        }
    </style>
</head>
<body>
    <div class="crt-container">
        <div class="crt-scanlines"></div>
        <div class="crt-curvature"></div>
        
        <div class="display-header">
            R E T R O C A L C U L A T O R
        </div>
        
        <div class="display-screen">
            <div class="text-xs opacity-70">0.0000000000000000000000</div>
            <div class="text-2xl mt-1">1234567890</div>
        </div>
        
        <div class="keypad">
            <button class="key">7</button>
            <button class="key">8</button>
            <button class="key">9</button>
            <button class="key function-key">÷</button>
            <button class="key function-key">C</button>
            
            <button class="key">4</button>
            <button class="key">5</button>
            <button class="key">6</button>
            <button class="key function-key">×</button>
            <button class="key function-key">√</button>
            
            <button class="key">1</button>
            <button class="key">2</button>
            <button class="key">3</button>
            <button class="key function-key">-</button>
            <button class="key function-key">x²</button>
            
            <button class="key">0</button>
            <button class="key">.</button>
            <button class="key equals-key">=</button>
            <button class="key function-key">+</button>
            <button class="key function-key">π</button>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const calculator = document.querySelector('.crt-container');
            
            // Power on effect
            calculator.style.transform = 'scale(0.8)';
            calculator.style.opacity = '0';
            
            setTimeout(() => {
                calculator.style.transition = 'all 0.6s ease-out';
                calculator.style.transform = 'scale(1)';
                calculator.style.opacity = '1';
            }, 100);
            
            // Button press effects
            document.querySelectorAll('.key').forEach(button => {
                button.addEventListener('mousedown', function() {
                    this.style.transform = 'scale(0.95)';
                });
                
                button.addEventListener('mouseup', function() {
                    this.style.transform = 'scale(1.05)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 100);
                });
            });
        });
    </script>
</body>
</html>