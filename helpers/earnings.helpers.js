export function buildAllEranings(query) {
  const aggregatePipeline = [];

  aggregatePipeline.push({
    $lookup: {
      from: "users",
      localField: "user",
      foreignField: "_id",
      as: "user",
    },
  });

  // aggregatePipeline.push({
  //   $unwind: "$user",
  // });

  aggregatePipeline.push({
    $lookup: {
      from: "users",
      localField: "user.referral",
      foreignField: "_id",
      as: "user.referral",
    },
  });

  // aggregatePipeline.push({
  //   $unwind: "$user.referral",
  // });
  aggregatePipeline.push({
    $lookup: {
      from: "affiliation",
      localField: "affiliation",
      foreignField: "_id",
      as: "affiliationData",
    },
  });

  // aggregatePipeline.push({
  //   $unwind: "$affiliationData",
  // });

  aggregatePipeline.push({
    $lookup: {
      from: "stores",
      localField: "store",
      foreignField: "_id",
      as: "store",
    },
  });

  aggregatePipeline.push({
    $unwind: "$store",
  });

  aggregatePipeline.push({
    $lookup: {
      from: "clicks",
      localField: "click",
      foreignField: "_id",
      as: "click",
    },
  });

  aggregatePipeline.push({
    $unwind: "$click",
  });

  aggregatePipeline.push({
    $lookup: {
      from: "offers",
      localField: "offer",
      foreignField: "_id",
      as: "offer",
    },
  });

  // aggregatePipeline.push({
  //   $unwind: "$offer",
  // });

  aggregatePipeline.push({
    $lookup: {
      from: "admins",
      localField: "createdBy",
      foreignField: "_id",
      as: "createdBy",
    },
  });

  aggregatePipeline.push({
    $unwind: "$createdBy",
  });

  aggregatePipeline.push({
    $skip: (Number(query.page) - 1) * Number(query.pageSize),
  });

  aggregatePipeline.push({
    $limit: 10,
  });

  // aggregatePipeline.push({
  //   $group: {
  //     _id: null,
  //     totalCount: { $sum: 1 },
  //   },
  // });

  aggregatePipeline.push({
    $project: {
      _id: 0,
      userId: "$user._id",
      userUid: "$user.uid",
      userName: "$user.name",
      userReferral: "$user",
      referenceId: 1,
      uid: 1,
      cashbackAmount: 1,
      referralCommission: 1,
      amountGot: 1,
      amountPromised: 1,
      saleAmount: 1,
      percentGot: 1,
      notes: 1,
      orderCount: 1,
      advertiserInfo: 1,
      affiliationData: 1,
      storeName: "$store.name",
      storeId: "$store._id",
      storeUid: "$store.uid",
      createdBy: "$createdBy.name",
      // pages: {
      //   $ceil: { $divide: ["$totalCount", query.pageSize] },
      // },
    },
  });

  return aggregatePipeline;
}
