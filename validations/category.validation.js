import * as yup from 'yup'
const image = yup
	.object({
		publicId: yup.string().required(),
		secureUrl: yup.string().required(),
	})
	.required()
const name = yup.string().required()
const description = yup.string().required()

export const createCategoryVal = yup.object({
	body: yup
		.object({
			name,
			image,
		})
		.required(),
})

export const updateCategoryWithParamsVal = yup.object({
	params: yup.object({
		categoryId: yup.string().required(),
	}),
})

export const updateCategoryVal = yup.object({
	body: yup
		.object({
			name,
			image,
		})
		.required(),
	params: yup
		.object({
			categoryId: yup.string().required(),
		})
		.required(),
})

export const removeSubcategoryVal = yup.object({
	body: yup
		.array({
			subCatIds: yup.string().required(),
		})
		.required(),
	params: yup
		.object({
			categoryId: yup.string().required(),
		})
		.required(),
})

export const updateCategoryTrendingPriorityVal = yup.object({
	body: yup
		.object({
			priority: yup.number().required(),
		})
		.required(),
	params: yup
		.object({
			categoryId: yup.string().required(),
		})
		.required(),
})