import * as yup from "yup";

export const updateTrendingStoreWithParamsVal = yup.object({
  params: yup.object({
    storeId: yup.string().required(),
  }),
});

export const createTrendingStoreVal = yup.object({
  body: yup
    .object({
      storeId: yup.string().required(),
    })
    .required(),
});

export const updateTrendingStoreVal = yup.object({
  body: yup
    .object({
      terms: yup.string().required(),
    })
    .required(),
  params: yup.object({
    storeId: yup.string().required(),
  }),
});

export const updateTrendingStorePriorityVal = yup.object({
  body: yup
    .object({
      priority: yup.number().required(),
    })
    .required(),
  params: yup.object({
    storeId: yup.string().required(),
  }),
});
