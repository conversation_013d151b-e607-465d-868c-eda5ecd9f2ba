import * as yup from 'yup'
import { ImageVal } from './common.validation.js'
const string = yup.string().required()
const array = yup.array().of(yup.string()).required();

export const createOnGoingSaleVal = yup.object({
  body: yup
    .object({
      saleName: string,
      saleStartDate: string,
      saleEndDate: string,
      saleLogo: ImageVal,
      offers: array
    })
    .required(),
})


export const updateOnGoingSaleVal = yup.object({
  body: yup
    .object({
      saleName: string,
      saleStartDate: string,
      saleEndDate: string,
      saleLogo: ImageVal,
      offers: array
    })
    .required(),
  params: yup.object({
    offerId: yup.string().required(),
  }),
})

export const updateOnGoingSaleWithParamsVal = yup.object({
  params: yup.object({
    offerId: yup.string().required(),
  }),
})
