import * as yup from 'yup'
const string = yup.string().required()
const image = yup.object({
    secureUrl: string,
    publicId: string,
})

export const createBannerVal = yup.object({
    body: yup.object({
        expiryDate: string,
        mobileBanner: image,
        desktopBanner: image,
        redirectUrl: string,
    }),
})
export const updateBannerVal = yup.object({
    body: yup.object({
        expiryDate: string,
        mobileBanner: image,
        desktopBanner: image,
        redirectUrl: string,
    }),
    params: yup.object({
        bannerId: string,
    }),
})

export const updateBannerParamsVal = yup.object({
    params: yup.object({
        bannerId: string,
    }),
})


export const updateBannerPriorityVal = yup.object({
    body: yup
        .object({
            priority: string,
        })
        .required(),
    params: yup.object({
        bannerId: yup.string().required(),
    }),
});


