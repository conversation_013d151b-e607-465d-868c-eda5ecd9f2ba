import * as yup from 'yup'
const string = yup.string().required()
const number = yup.number().required()
const date = yup.date().required()

export const createGiftCardOrderVal = yup.object({
	body: yup
		.object({
			store: string,
			name: string,
			email: string,
			mobile: string,
			amount: number,
			qcExpDate: date,
			offlineGcExpiry: string,
		})
		.required(),
})

export const updateGiftCardOrderVal = yup.object({
	body: yup
		.object({
			store: string,
			name: string,
			email: string,
			mobile: string,
			amount: number,
			qcExpDate: date,
			offlineGcExpiry: string,
		})
		.required(),
	params: yup.object({
		orderId: string,
	}),
})

export const updateGiftCardOrderWithParamsVal = yup.object({
	params: yup.object({
		orderId: string,
	}),
})
