import * as yup from 'yup'
const string = yup.string().required()
const image = yup.object({
	secureUrl: string,
	publicId: string,
})

export const createMobileBannerVal = yup.object({
	body: yup.object({
		expiryDate: string,
		bannerImage: image,
		redirectUrl: string,
	}),
})
export const updateMobileBannerVal = yup.object({
	body: yup.object({
		expiryDate: string,
		bannerImage: image,
		redirectUrl: string,
	}),
	params: yup.object({
		bannerId: string,
	}),
})

export const updateMobileBannerParamsVal = yup.object({
	params: yup.object({
		bannerId: string,
	}),
})
