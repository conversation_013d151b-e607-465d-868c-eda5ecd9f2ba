import * as yup from 'yup'
import { ImageVal } from './common.validation.js'
const string = yup.string().required()

export const createTestimonialVal = yup.object({
    body: yup
        .object({
            reviewerName: yup.string().required(),
            reviewerAvatar: ImageVal,
            rating: yup.number().oneOf([1, 2, 3, 4, 5]).required(),
        })
        .required(),
})

export const updateTestimonialVal = yup.object({
    body: yup
        .object({
            reviewerName: string,
            reviewerAvatar: ImageVal,
            rating: 1 || 2 || 3 || 4 || 5
        })
        .required(),
    params: yup.object({
        testimonialId: yup.string().required(),
    }),
})

export const updateTestimonialWithParamsVal = yup.object({
    params: yup.object({
        testimonialId: yup.string().required(),
    }),
})

