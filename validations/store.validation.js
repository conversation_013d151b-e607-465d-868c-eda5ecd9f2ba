import * as yup from "yup";
import { ImageVal, categoriesVal } from "./common.validation.js";
const date = yup.date().required();
const string = yup.string().required();
const number = yup.number().required();
const boolean = yup.boolean();

export const createStoreVal = yup.object({
    body: yup
        .object({
            name: string,
            logo: ImageVal,
            categories: categoriesVal,
            noAppSaleCategory: boolean,
            noMobileWebSaleCategory: boolean,
            noDesktopWebSaleCategory: boolean,
            minimumAmount: number,
            maximumAmount: number,
            description: string,
            affiliation: string,
            campaignType: string,
            storeOffer: string,
            reliability: number,
            isTrackable: boolean,
            storeHowToGet: string,
            storeTerms: string,
            confirmationTime: string,
            trackingTime: string,
            bgColor: string,
        })
        .required(),
});

export const updateStoreVal = yup.object({
    body: yup
        .object({
            name: string, //
            logo: ImageVal,
            categories: categoriesVal,
            noAppSaleCategory: boolean,
            noMobileWebSaleCategory: boolean,
            noDesktopWebSaleCategory: boolean,
            minimumAmount: number,
            maximumAmount: number,
            description: string,
            // affiliation: string, //
            campaignType: string,
            storeOffer: string,
            reliability: number,
            isTrackable: boolean,
            storeHowToGet: string,
            storeTerms: string,
            confirmationTime: string,
            trackingTime: string,
            bgColor: string, //
        })
        .required(),
    params: yup.object({
        storeId: yup.string().required(),
    }),
});

export const updateStoreWithParamsVal = yup.object({
    params: yup.object({
        storeId: yup.string().required(),
    }),
});

export const updateStorePriorityVal = yup.object({
    body: yup
        .object({
            priority: number,
        })
        .required(),
    params: yup.object({
        storeId: yup.string().required(),
    }),
});

export const updateStoreIsSpecialVal = yup.object({
    body: yup
        .object({
            isSpecial: string,
        })
        .required(),
    params: yup.object({
        storeId: yup.string().required(),
    }),
});
