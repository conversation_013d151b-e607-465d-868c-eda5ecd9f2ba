import * as yup from 'yup'
const string = yup.string().required()
const number = yup.number().required()
import { categoriesVal, arrayVal } from './common.validation.js'

export const createGiftCardVal = yup.object({
	body: yup
		.object({
			name: string,
			discountGetting: number,
			cashbackGiving: number,
			categories: categoriesVal,
			notes: string,
			terms: string,
			description: string,
			provider: string,
			validity: string,
			relatedCbStore: string,
			minimumAmount: number,
			maximumAmount: number,
			howToUse: arrayVal
		})
		.required(),
})

export const updateGiftCardVal = yup.object({
	body: yup
		.object({
			name: string,
			discountGetting: number,
			cashbackGiving: number,
			categories: categoriesVal,
			notes: string,
			terms: string,
			description: string,
			provider: string,
			validity: string,
			relatedCbStore: string,
			minimumAmount: number,
			maximumAmount: number,
			howToUse: arrayVal
		})
		.required(),
	params: yup.object({
		giftCardId: string,
	}),
})

export const updateGiftCardWithParamsVal = yup.object({
	params: yup.object({
		giftCardId: string,
	}),
})

export const updateGiftCardPriorityVal = yup.object({
	body: yup
		.object({
			priority: string,
		})
		.required(),
	params: yup.object({
		giftCardId: string,
	}),
})
