import * as yup from "yup";
const string = yup.string().required();
const email = yup
  .string("email must be string")
  .email("Not a valid email")
  .min(10)
  .max(50)
  .required("email is required");
const password = yup
  .string("password must be string")
  .min(6)
  .max(18)
  .required("Password is required");
const mobile = yup
  .string("mobile must be string")
  .min(10)
  .max(12)
  .required("mobile number is required");

export const userLoginWithCredentialVal = yup.object({
  body: yup.object({
    email,
    password,
  }),
});

export const updateUserWithParamsVal = yup.object({
  params: yup.object({
    userId: string,
  }),
});

export const updateUserNoteVal = yup.object({
  params: yup.object({
    userId: string,
  }),
  body: yup.object({
    notes: string,
  }),
});

export const createUserWithCredentialsVal = yup.object({
  body: yup.object({
    name: string,
    email,
    password,
    mobile,
  }),
});
