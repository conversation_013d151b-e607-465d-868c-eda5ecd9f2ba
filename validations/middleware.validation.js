import * as yup from 'yup'
const number = yup.number().required()
const string = yup.string().required()
const date = yup.string().required()

export const createMiddlewareVal = yup.object({
	body: yup
		.object({
			name: string,
			level: number
		})
		.required(),
})

export const updateMiddlewareNameVal = yup.object({
	body: yup
		.object({
			name: string,
		})
		.required(),
	params: yup.object({
		middlewareId: yup.string().required(),
	}),
})

export const updateMiddlewareWithParamsVal = yup.object({
	params: yup.object({
		middlewareId: yup.string().required(),
	}),
})

export const updateMiddlewareFeatureVal = yup.object({
	body: yup
		.object({
			features: string,
		})
		.required(),
	params: yup.object({
		middlewareId: yup.string().required(),
	}),
})
