import * as yup from 'yup'
const apiName = yup.string().required()
const apiKey = yup.string().required()
const name = yup.string().required()

export const createAffiliationSchema = yup.object({
	body: yup
		.object({
			name,
			apiName,
			apiKey,
		})
		.required(),
})

export const updateAffiliationDetailsWithParams = yup.object({
	params: yup.object({
		affiliationId: yup.string().required(),
	}),
})

export const updateAffiliationSchema = yup.object({
	body: yup
		.object({
			name,
			apiName,
			apiKey,
		})
		.required(),
	params: yup.object({
		affiliationId: yup.string().required(),
	}),
})
