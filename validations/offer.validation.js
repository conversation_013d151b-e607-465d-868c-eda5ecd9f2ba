import * as yup from "yup";
import { ImageVal, categoriesVal } from "./common.validation.js";
const string = yup.string().required();
const number = yup.number().required();
const optionalNumber = yup
  .number()
  .transform((value, originalValue) => (originalValue === "" ? null : value))
  .nullable()
  .optional();
const boolean = yup.boolean().required();
const date = yup.date().required();

export const createOfferVal = yup.object({
  body: yup
    .object({
      store: string,
      categories: categoriesVal,
      title: string,
      affiliation: string,
      discount: optionalNumber,
      itemPrice: optionalNumber,
      productImage: ImageVal,
      description: string,
      link: string,
      terms: string,
      stockEnds: boolean,
      dateExpiry: date,
      dateStart: date,
    })
    .required(),
});

export const updateOfferPriorityVal = yup.object({
  body: yup
    .object({
      priority: number,
    })
    .required(),
  params: yup.object({
    offerId: yup.string().required(),
  }),
});

export const updateOfferWithParamsVal = yup.object({
  params: yup.object({
    offerId: yup.string().required(),
  }),
});

export const updateOfferVal = yup.object({
  body: yup
    .object({
      store: string,
      categories: categoriesVal,
      title: string,
      affiliation: string,
      discount: optionalNumber, // Changed from number to optionalNumber
      itemPrice: optionalNumber, // Changed from number to optionalNumber
      productImage: ImageVal,
      description: string,
      link: string,
      terms: string,
      stockEnds: boolean,
      dateExpiry: date,
      dateStart: date,
    })
    .required(),
  params: yup.object({
    offerId: yup.string().required(),
  }),
});

export const updateOfferTrendingPriorityVal = yup.object({
  body: yup
    .object({
      trendingPriority: number,
    })
    .required(),
  params: yup.object({
    offerId: yup.string().required(),
  }),
});
