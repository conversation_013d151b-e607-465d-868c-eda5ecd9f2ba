import * as yup from "yup";
const number = yup.number().required();
const string = yup.string().required();

export const createStoreCategoryVal = yup.object({
  body: yup
    .object({
      store: string,
      affiliation: string,
      name: string,
      description: string,
      gettingNewUserRate: number,
      gettingOldUserRate: number,
      sectionLink: string,
    })
    .required(),
});

export const updateStoreCategoryVal = yup.object({
  body: yup
    .object({
      store: string,
      affiliation: string,
      name: string,
      description: string,
      gettingNewUserRate: number,
      gettingOldUserRate: number,
      sectionLink: string,
    })
    .required(),
  params: yup.object({
    categoryId: yup.string().required(),
  }),
});

export const updateStoreCategoryWithParamsVal = yup.object({
  params: yup.object({
    categoryId: yup.string().required(),
  }),
});
