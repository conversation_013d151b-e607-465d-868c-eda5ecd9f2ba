import * as yup from 'yup'

const string = yup.string().required()
const enumVal = yup.mixed().oneOf(['terms', 'privacy'])

export const updateTermsAndPrivacyWithParamsVal = yup.object({
	params: yup.object({
		id: yup.string().required(),
	}),
})

export const createTermsAndPrivacyVal = yup.object({
	body: yup.object({
		content: string,
		type: enumVal
	}).required(),
})

export const updateTermsAndPrivacyVal = yup.object({
	body: yup
		.object({
			content: string,
			type: enumVal
		})
		.required(),
	params: yup.object({
		id: yup.string().required(),
	}),
})

export const updateTermsAndPrivacyTypeVal = yup.object({
	body: yup
		.object({
			type: enumVal
		})
		.required(),
	params: yup.object({
		id: yup.string().required(),
	}),
})
