import * as yup from 'yup'
const string = yup.string().required()
const image = yup.object({
	secureUrl: string,
	publicId: string,
})

export const createMobileStoryVal = yup.object({
	body: yup
		.object({
			title: string,
			description: string,
			store: string,
			expiryDate: string,
			buttonText: string,
			image,
		})
		.required(),
})
export const updateMobileStoryVal = yup.object({
	body: yup
		.object({
			title: string,
			description: string,
			store: string,
			expiryDate: string,
			buttonText: string,
			image,
		})
		.required(),
	params: yup.object({
		storyId: string,
	}),
})

export const updateMobileStoryParamsVal = yup.object({
	params: yup.object({
		storyId: string,
	}),
})
