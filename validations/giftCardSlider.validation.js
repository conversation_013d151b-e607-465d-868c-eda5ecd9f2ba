import * as yup from 'yup'
import { ImageVal } from './common.validation.js'
const string = yup.string().required()
const number = yup.number().required()
const date = yup.date().required()
export const createGiftCardSliderVal = yup.object({
	body: yup
		.object({
			termsTitle: string,
			termsContent: string,
			redirectUrl: string,
			mobileBanner: ImageVal,
			desktopBanner: ImageVal,
			expiryDate: date,
		})
		.required(),
})

export const updateGiftCardSliderVal = yup.object({
	body: yup
		.object({
			termsTitle: string,
			termsContent: string,
			redirectUrl: string,
			mobileBanner: ImageVal,
			desktopBanner: ImageVal,
			expiryDate: date,
		})
		.required(),
	params: yup.object({
		sliderId: string,
	}),
})

export const updateGiftCardSliderWithParamsVal = yup.object({
	params: yup.object({
		sliderId: string,
	}),
})

export const updateGiftCardSliderPriorityVal = yup.object({
	body: yup
		.object({
			priority: string,
		})
		.required(),
	params: yup.object({
		sliderId: string,
	}),
})
