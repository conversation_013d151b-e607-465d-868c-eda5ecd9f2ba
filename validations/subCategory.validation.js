import * as yup from 'yup'
const image = yup.object({
	publicId: yup.string().required(),
	secureUrl: yup.string().required(),
})
const string = yup.string().required()

export const createSubCategoryVal = yup
	.object({
		body: yup.object({
			name: string,
			description: string,
			category: string,
			image,
		}),
		params: yup.object({
			categoryId: yup.string().required(),
		}),
	})
	.required()

export const updateSubCategoryWithParamsVal = yup.object({
	params: yup.object({
		categoryId: yup.string().required(),
	}),
})

export const updateSubCategoryVal = yup.object({
	body: yup.object({
		name: string,
		description: string,
		image,
		category: string
	}),
	params: yup.object({
		categoryId: yup.string().required(),
	}),
})
