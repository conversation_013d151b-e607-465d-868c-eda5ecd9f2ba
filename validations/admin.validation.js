import * as yup from 'yup'
const email = yup.string().email().required()
const password = yup.string().required()
const name = yup.string().required()

export const adminLoginSchema = yup.object({
	body: yup
		.object({
			email,
			password,
		})
		.required(),
})

export const createAdminSchema = yup.object({
	body: yup
		.object({
			name,
			email,
			password,
			role: yup.string().required(),
		})
		.required(),
})

export const updateAdminRoleSchema = yup.object({
	body: yup
		.object({
			role: yup.string().required(),
		})
		.required(),
	params: yup.object({
		adminId: yup.string().required(),
	}),
})

export const updateAdminDetailsWithParams = yup.object({
	params: yup.object({
		adminId: yup.string().required(),
	}),
})

export const updateAdminSchema = yup.object({
	body: yup
		.object({
			name,
			email,
			role: yup.string().required(),
		})
		.required(),
	params: yup.object({
		adminId: yup.string().required(),
	}),
})

export const updateAdminPasswordSchema = yup.object({
	body: yup
		.object({
			email,
			password: yup.string().required(),
		})
		.required(),
	params: yup.object({
		adminId: yup.string().required(),
	}),
})
