import * as yup from 'yup'
const string = yup.string().required()
const date = yup.date().required()

export const createGiftCardOfferVal = yup.object({
	body: yup
		.object({
			name: string,
			giftCard: string,
			rateGetting: string,
			rateGiving: string,
			startDate: date,
			endDate: date,
			terms: string,
		})
		.required(),
})

export const updateGiftCardOfferVal = yup.object({
	body: yup
		.object({
			name: string,
			giftCard: string,
			rateGetting: string,
			rateGiving: string,
			startDate: date,
			endDate: date,
			terms: string,
		})
		.required(),
	params: yup.object({
		offerId: string,
	}),
})

export const updateGiftCardOfferWithParamsVal = yup.object({
	params: yup.object({
		offerId: string,
	}),
})
