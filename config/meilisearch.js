import { MeiliSearch } from "meilisearch";
import dotenv from "dotenv";
import moment from "moment-timezone";
dotenv.config();

export class MeiliSearchService {
  constructor() {
    this.indexName = process.env.MEILISEARCH_NAME;
    this.client = new MeiliSearch({
      host: process.env.MEILISEARCH_URL,
      apiKey: process.env.MEILISEARCH_KEY,
    });
  }

  async checkOrCreateIndex(indexName) {
    try {
      const index = await this.client.getIndex(indexName);
      if (index && index.uid === indexName) {
        await index.updateFilterableAttributes([
          "name",
          "active",
          "dateExpiry",
          "id",
          "type",
        ]);
        return index;
      }
    } catch (error) {
      await this.client.createIndex(indexName, {
        primaryKey: "id",
      });
      const newIndex = await this.client.getIndex(indexName);
      await newIndex.updateSearchableAttributes([
        "type",
        "id",
        "uid",
        "active",
        "name",
        "description",
        "detailedDescription",
        "offerWarning",
        "giftCard",
        "categories",
        "subcategories",
        "storecategories",
        "cashbackGiving",
        "relatedInstantStore",
        "title",
        "offerType",
        "couponCode",
        "offerPercent",
        "offerAmount",
        "store",
        "dateExpiry",
      ]);
      await newIndex.updateFilterableAttributes([
        "name",
        "active",
        "dateExpiry",
      ]);
      await new Promise((resolve) => setTimeout(resolve, 15000));
      return newIndex;
    }
  }

  async addDocuments(documents) {
    const index = await this.checkOrCreateIndex(this.indexName);
    const response = await index.addDocuments([documents]);
    console.log("Added document to MeiliSearch:", response);
    return response;
  }

  async updateDocuments(documents) {
    const index = await this.checkOrCreateIndex(this.indexName);
    const response = await index.updateDocuments([documents]);
    return response;
  }

  async deleteDocuments(documents) {
    const index = await this.checkOrCreateIndex(this.indexName);
    const response = await index.deleteDocuments([documents]);
    return response;
  }
  // New function to remove inactive offers from MeiliSearch
  async removeExpiredOffers() {
    const index = await this.checkOrCreateIndex(this.indexName);
    const now = await this.convertLocalToUTC();

    // Convert the current date to a Unix timestamp (milliseconds)
    const nowTimestamp = moment(now).valueOf(); // This gives the timestamp in milliseconds

    // Deletes documents where the "active" field is set to `false`
    const response = await index.deleteDocuments({
      filter: `dateExpiry < ${nowTimestamp} AND  type = "offer"`,
    });
    return response;
  }
  catch(error) {
    console.error("Error deleting inactive offers from MeiliSearch:", error);
    throw error;
  }

  async removeSelectedOffer(id) {
    const index = await this.checkOrCreateIndex(this.indexName);
    try {
      // Deletes documents where the "id" matches and "type" is "offer"
      const response = await index.deleteDocuments({
        filter: `id = ${id} AND type = "offer"`,
      });
      return response;
    } catch (error) {
      console.error("Error deleting offer from MeiliSearch:", error);
      throw error;
    }
  }

  async checkDeletionStatus(taskUid) {
    const index = await this.checkOrCreateIndex(this.indexName);
    try {
      const taskStatus = await index.getTask(taskUid);
      console.log("Task status:", taskStatus);
      return taskStatus;
    } catch (error) {
      console.error("Error checking task status:", error);
    }
  }

  async removeStore(id) {
    const index = await this.checkOrCreateIndex(this.indexName);
    try {
      const response = await index.deleteDocuments({
        filter: `id = ${id} AND type = "store"`,
      });
      return response;
    } catch (error) {
      console.error("Error deleting store from MeiliSearch:", error);
      throw error;
    }
  }

  async convertLocalToUTC(timezone = "Asia/Kolkata") {
    // Get the current time in the provided time zone
    const localTime = moment.tz(timezone);

    // Convert the local time to UTC and return it as ISO string
    return localTime.utc().toISOString();
  }
}
