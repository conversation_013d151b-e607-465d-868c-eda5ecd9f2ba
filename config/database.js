import dotenv from "dotenv";
import As<PERSON><PERSON>and<PERSON> from "express-async-handler";
import mongoose from "mongoose";
dotenv.config();

const connectDatabase = AsyncHandler(async () => {
  try {
    const connect = await mongoose.connect(process.env.DB_HOST_URL, {
      dbName: process.env.DB_NAME,
    });
    console.log(`MongoDB Connected to :${connect.connection.host}`);
  } catch (error) {
    console.log(error);
    process.exit(1);
  }
});

export default connectDatabase;
