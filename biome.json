{"$schema": "https://biomejs.dev/schemas/1.5.3/schema.json", "organizeImports": {"enabled": true}, "formatter": {"ignore": ["dist", "node_modules"], "enabled": true}, "javascript": {"formatter": {"enabled": true, "semicolons": "asNeeded", "quoteProperties": "asNeeded", "quoteStyle": "single", "arrowParentheses": "asNeeded", "indentStyle": "tab"}, "parser": {"unsafeParameterDecoratorsEnabled": true}}, "linter": {"ignore": ["dist", "node_modules"], "enabled": true, "rules": {"style": {"all": true, "useNamingConvention": "off", "useWhile": "warn", "noUselessElse": "error", "useAsConstAssertion": "warn", "useShorthandAssign": "warn", "noDefaultExport": "off", "noParameterProperties": "off", "useConst": "error"}, "recommended": true}}}