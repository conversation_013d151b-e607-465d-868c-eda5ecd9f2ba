# Enhanced Click Tracking Workflow - Deployment and Monitoring Guide

## Pre-Deployment Checklist

### Database Preparation
- [ ] **Create Critical Indexes** (REQUIRED before deployment)
  ```javascript
  // Critical for Scenario 1 performance
  db.earnings.createIndex({ "orderUniqueId": 1 }, { background: true })
  
  // Important for click status filtering
  db.clicks.createIndex({ "status": 1 }, { background: true })
  ```

- [ ] **Verify Schema Compatibility**
  - Confirm `earnings.orderUniqueId` field exists
  - Confirm `earnings.click` field is optional ObjectId
  - Confirm `clicks.status` enum includes 'tracked'

- [ ] **Backup Database** before deployment

### Code Deployment Preparation
- [ ] **Environment Variables** configured
- [ ] **Feature Flags** implemented for gradual rollout
- [ ] **Monitoring Tools** configured
- [ ] **Alerting Thresholds** set up

## Deployment Strategy

### Phase 1: Parallel Deployment (Week 1)
```javascript
// Deploy enhanced system alongside current system
const useEnhancedWorkflow = process.env.ENHANCED_WORKFLOW_ENABLED === 'true'

if (useEnhancedWorkflow) {
    // Use enhanced three-scenario workflow
    const result = await orchestrator.processConversion(conversionData)
} else {
    // Use legacy workflow
    const result = await orchestrator.processConversionLegacy(conversionData)
}
```

**Objectives:**
- Validate enhanced system functionality
- Compare performance metrics
- Identify any issues before migration

### Phase 2: Gradual Migration (Weeks 2-4)
```javascript
// Gradual rollout by partner or percentage
const rolloutPercentage = process.env.ENHANCED_ROLLOUT_PERCENTAGE || 0
const useEnhanced = Math.random() * 100 < rolloutPercentage

// Or by partner
const enhancedPartners = ['Impact', 'Admitad'] // Start with specific partners
const useEnhanced = enhancedPartners.includes(conversionData.partner)
```

**Rollout Schedule:**
- Week 2: 10% traffic
- Week 3: 25% traffic  
- Week 4: 50% traffic
- Week 5: 75% traffic
- Week 6: 100% traffic

### Phase 3: Full Migration (Week 6+)
- Remove feature flags
- Set enhanced workflow as default
- Clean up legacy code (optional)

## Monitoring and Alerting

### Key Metrics to Monitor

#### Scenario Distribution Metrics
```javascript
// Monitor scenario usage patterns
const scenarioMetrics = {
    scenario1_percentage: (scenario1_count / total_conversions) * 100,
    scenario2_percentage: (scenario2_count / total_conversions) * 100,
    scenario3_percentage: (scenario3_count / total_conversions) * 100
}

// Expected healthy distribution:
// Scenario 1: 5-15% (duplicates)
// Scenario 2: 60-80% (click attribution)
// Scenario 3: 15-35% (standalone)
```

#### Performance Metrics
```javascript
// Processing time per scenario
const performanceMetrics = {
    avg_processing_time_scenario1: 50,   // ms (should be fastest)
    avg_processing_time_scenario2: 300,  // ms (most complex)
    avg_processing_time_scenario3: 200,  // ms (moderate)
    overall_avg_processing_time: 250     // ms (target: <300ms)
}
```

#### Quality Metrics
```javascript
// Accuracy and success rates
const qualityMetrics = {
    duplicate_prevention_rate: 95,       // % (target: >90%)
    click_attribution_success_rate: 85,  // % (target: >80%)
    error_rate: 2,                       // % (target: <5%)
    fallback_rate: 15                    // % (Scenario 2 → 3 fallbacks)
}
```

### Alerting Thresholds

#### Critical Alerts (Immediate Response Required)
```yaml
# Error Rate Alert
error_rate:
  threshold: 5%
  window: 5 minutes
  severity: critical
  action: "Page on-call engineer"

# Processing Time Alert  
avg_processing_time:
  threshold: 500ms
  window: 10 minutes
  severity: critical
  action: "Page on-call engineer"

# Duplicate Rate Alert
scenario1_percentage:
  threshold: 25%
  window: 15 minutes
  severity: critical
  action: "Investigate data quality issues"
```

#### Warning Alerts (Monitor and Investigate)
```yaml
# Click Attribution Rate Drop
scenario2_percentage:
  threshold: 50%
  window: 30 minutes
  severity: warning
  action: "Check click data quality"

# High Fallback Rate
fallback_rate:
  threshold: 30%
  window: 30 minutes
  severity: warning
  action: "Review click validation logic"

# Database Performance
db_query_time:
  threshold: 100ms
  window: 15 minutes
  severity: warning
  action: "Check database performance"
```

### Monitoring Dashboard

#### Real-time Metrics Panel
```
┌─────────────────────────────────────────────────────────────┐
│ Enhanced Click Tracking - Real-time Dashboard              │
├─────────────────────────────────────────────────────────────┤
│ Scenario Distribution (Last 1 Hour)                        │
│ ┌─────────┬─────────┬─────────┬─────────┐                  │
│ │ Total   │ Scen 1  │ Scen 2  │ Scen 3  │                  │
│ │ 1,247   │ 87 (7%) │ 934(75%)│ 226(18%)│                  │
│ └─────────┴─────────┴─────────┴─────────┘                  │
│                                                             │
│ Performance Metrics                                         │
│ ┌─────────────────┬─────────────────┬─────────────────┐    │
│ │ Avg Time        │ Error Rate      │ Success Rate    │    │
│ │ 287ms ✅        │ 1.2% ✅         │ 98.8% ✅        │    │
│ └─────────────────┴─────────────────┴─────────────────┘    │
│                                                             │
│ Database Performance                                        │
│ ┌─────────────────┬─────────────────┬─────────────────┐    │
│ │ Query Time      │ Index Usage     │ Connection Pool │    │
│ │ 45ms ✅         │ 99.2% ✅        │ 23/50 ✅        │    │
│ └─────────────────┴─────────────────┴─────────────────┘    │
└─────────────────────────────────────────────────────────────┘
```

### Log Analysis

#### Structured Logging Format
```javascript
// Enhanced logging with scenario context
const logEntry = {
    timestamp: new Date().toISOString(),
    level: 'INFO',
    service: 'click-tracking',
    scenario: 2,
    scenarioName: 'Click-to-Earning Conversion',
    orderId: 'ORDER-12345',
    clickId: 'CLICK-67890',
    partner: 'Impact',
    processingTime: 287,
    success: true,
    metrics: {
        scenario2_clickToEarning: 1,
        earningsWithClick: 1
    }
}
```

#### Log Queries for Troubleshooting
```bash
# Find all Scenario 1 (duplicate) events in last hour
grep "scenario.*1" /var/log/click-tracking.log | grep "$(date -d '1 hour ago' '+%Y-%m-%d %H')"

# Find high processing time events
grep "processingTime.*[5-9][0-9][0-9]" /var/log/click-tracking.log

# Find error patterns by scenario
grep "ERROR" /var/log/click-tracking.log | grep "scenario.*2"
```

## Operational Procedures

### Daily Health Checks
```bash
#!/bin/bash
# daily_health_check.sh

echo "=== Enhanced Click Tracking Health Check ==="
echo "Date: $(date)"

# Check scenario distribution
echo "Scenario Distribution (Last 24h):"
# Query metrics API or database for scenario counts

# Check error rates
echo "Error Rates (Last 24h):"
# Query error logs or metrics

# Check database performance
echo "Database Performance:"
# Check index usage and query performance

# Check processing times
echo "Processing Performance:"
# Query average processing times by scenario
```

### Weekly Performance Review
1. **Scenario Distribution Analysis**
   - Review weekly scenario percentages
   - Identify trends or anomalies
   - Compare with baseline expectations

2. **Performance Optimization**
   - Analyze slow query logs
   - Review index usage statistics
   - Identify optimization opportunities

3. **Error Pattern Analysis**
   - Review error logs by scenario
   - Identify common failure patterns
   - Update error handling if needed

### Monthly Capacity Planning
1. **Growth Analysis**
   - Review conversion volume trends
   - Project future capacity needs
   - Plan database scaling if needed

2. **Performance Trends**
   - Analyze monthly performance metrics
   - Identify degradation patterns
   - Plan optimization initiatives

## Troubleshooting Guide

### Common Issues and Solutions

#### High Scenario 1 Rate (>20%)
**Symptoms:** Excessive duplicate detection
**Causes:**
- Data quality issues
- Partner sending duplicate data
- Clock synchronization issues

**Solutions:**
1. Check partner data quality
2. Review duplicate detection logic
3. Verify timestamp handling

#### Low Scenario 2 Rate (<50%)
**Symptoms:** Poor click attribution
**Causes:**
- Click data quality issues
- Timing mismatches
- Click validation too strict

**Solutions:**
1. Review click data from partners
2. Check click validation logic
3. Analyze timing patterns

#### High Processing Times (>500ms)
**Symptoms:** Slow conversion processing
**Causes:**
- Database performance issues
- Missing indexes
- Network latency

**Solutions:**
1. Check database query performance
2. Verify index usage
3. Review network connectivity

### Emergency Procedures

#### Rollback to Legacy System
```javascript
// Emergency rollback procedure
process.env.ENHANCED_WORKFLOW_ENABLED = 'false'
// Restart application or use feature flag
```

#### Database Performance Issues
1. Check index usage: `db.earnings.getIndexes()`
2. Analyze slow queries: `db.setProfilingLevel(2)`
3. Monitor connection pool: Check active connections

#### High Error Rate Response
1. **Immediate:** Enable legacy fallback
2. **Short-term:** Investigate error patterns
3. **Long-term:** Fix root cause and redeploy

## Success Criteria

### Deployment Success Metrics
- [ ] Error rate < 5%
- [ ] Average processing time < 300ms
- [ ] Scenario 1 rate 5-15%
- [ ] Scenario 2 rate 60-80%
- [ ] Click attribution success rate > 80%
- [ ] Zero data loss incidents
- [ ] Successful rollback capability verified

### Long-term Success Indicators
- 12% increase in tracked revenue
- 67% reduction in duplicate processing
- 20% improvement in processing speed
- 300% improvement in operational visibility
- 150% improvement in development efficiency

## Contact Information

**On-Call Engineer:** [Your on-call rotation]
**Database Team:** [Database team contact]
**DevOps Team:** [DevOps team contact]
**Product Owner:** [Product owner contact]

**Escalation Path:**
1. On-call Engineer (0-15 minutes)
2. Team Lead (15-30 minutes)
3. Engineering Manager (30-60 minutes)
4. CTO (60+ minutes for critical issues)
