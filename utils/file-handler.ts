import { v2 as cloudinary } from 'cloudinary';

import dotenv from 'dotenv';

// Load environment variables from .env file
dotenv.config();




// Configure your Cloudinary account
cloudinary.config({
    cloud_name: process.env.CLOUDINARY_NAME,
    api_key: process.env.CLOUDINARY_API_KEY,
    api_secret: process.env.CLOUDINARY_API_SECRET,
});


// Function to generate the transformation URL
export const generateTransformationUrl = (publicId) => {
    // return cloudinary.image(publicId, {
    //     transformation: [
    //         { fetch_format: "png" }
    //     ]
    // })
    //
    //
    return cloudinary.url(publicId, {
        format: 'png',
    });
};
