import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'
/* cSpell:disable */
import FormData from 'form-data'
// import Mailgun from "mailgun.js";
import {
	SendSmtpEmail,
	TransactionalEmailsApi,
	TransactionalEmailsApiApiKeys,
} from '@getbrevo/brevo'
import dotenv from 'dotenv'
import { convertSvgToPngUrl } from './file-handler.js'

// Load environment variables from .env file
dotenv.config()

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// mailgun code
// export async function sendMail(payload) {
//   const mailgun = new Mailgun(FormData);
//   const client = mailgun.client({
//     username: "api",
//     key: process.env.MAILGUN_API_KEY,
//   });

//   try {
//     const res = await client.messages.create(
//       process.env.MAILGUN_DOMAIN,
//       payload
//     );
//     console.log("🚀 ~ sendMail ~ res:", res);
//     return res;
//   } catch (error) {
//     console.log(error);
//   }
// }

const apiInstance = new TransactionalEmailsApi()
apiInstance.setApiKey(
	TransactionalEmailsApiApiKeys.apiKey,
	process.env.BREVO_API_KEY,
)
export async function sendMail(payload) {
	try {
		const sendSmtpEmail = new SendSmtpEmail()

		sendSmtpEmail.subject = payload.subject
		sendSmtpEmail.htmlContent = payload.text // You can modify this to accept HTML content
		sendSmtpEmail.sender = {
			name: 'IndianCashback',
			email: process.env.MAILGUN_FROM,
		}
		sendSmtpEmail.to = [{ email: payload.to }]

		const { response } = await apiInstance.sendTransacEmail(sendSmtpEmail)
		return response
	} catch (error) {
		console.error('Error sending mail:', error)
		throw error
	}
}

function sanitizeString(input) {
	return input
		?.replace(/<strong>|<\/strong>/gi, '') // Remove <strong> tags
		.replace(/%/g, 'pc') // Replace % with pc
		.replace(/[^\w\s]/gi, '') // Remove all special characters
}
export function generateProductUrl(storeName, offerTitle) {
	// Sanitize inputsw
	const sanitizedStoreName = sanitizeString(storeName)
	const sanitizedTitle = sanitizeString(offerTitle)

	// Concatenate and sanitize the URL
	let url = `${sanitizedStoreName}_${sanitizedTitle}`
	url = url.substring(0, 60) // Limit to 60 characters

	// Replace spaces with dashes and convert to lowercase
	url = url.toLowerCase().replace(/\s+/g, '-')

	return url
}

async function fetchSvgAsBase64(url) {
	try {
		// Fetch the SVG content from the URL
		const response = await fetch(url)

		// Ensure the response is OK
		if (!response.ok) {
			throw new Error(`Failed to fetch SVG: ${response.statusText}`)
		}

		// Read the response as an array buffer
		const arrayBuffer = await response.arrayBuffer()

		// Convert the array buffer to a buffer
		const buffer = Buffer.from(arrayBuffer)

		// Convert the buffer to a base64 string
		const base64 = buffer.toString('base64')

		// Create the data URL
		const dataUrl = `data:image/svg+xml;base64,${base64}`

		return dataUrl
	} catch (error) {
		console.error('Error:', error)
		throw error
	}
}

async function generateStoreHtml(store) {
	let storeImage = store.storeImage
	storeImage = convertSvgToPngUrl(storeImage)

	return `<td style="padding: 0 10px; text-align: center;">
                        <a href="https://www.indiancashback.com/store/${store?.name}" target="_blank"
                          style="text-decoration: none; color: unset;">
                          <table
                            style="background: rgb(223, 223, 223); width: 120px; height: 70px; border-radius: 5px; margin-bottom: 5px;">
                            <tr>
                              <td align="center" valign="center">
                                <img
                                  src="${storeImage}"
                                  alt="centered image" height="30" width="85%" style="object-fit: contain;">
                              </td>
                            </tr>
                          </table>
                          <span style="font-size: 10px; font-weight: 600; margin-top: 8px;">${store?.storeOffer}</span>
                          <span style="display:block; font-size: 12px; font-weight: 500; margin-top: 5px;">${store?.name}</span>
                        </a>
                      </td>`
}

function generateOfferHtml(offer) {
	return `
       <table width="640" style="margin:auto;">
                    <tbody>
                      <tr>
                        <td width="620" valign="top" align="left">
                          <table width="620" cellspacing="0" cellpadding="0" border="0">
                            <tbody>
                              <tr>
                                <td width="150" align="center" valign="top"
                                  style='background-color: white; margin-right:5px;'> <a
                                    href="https://www.indiancashback.com/store/${
																			offer?.storeName
																		}/${generateProductUrl(
																			offer?.storeName,
																			offer?.title,
																		)}?uid=${offer?.offerUid}"
                                    rel="noreferrer" target="_blank"> <img
                                      src=${offer?.offerImage}
                                      alt="" border="0" style='object-fit: contain; width:140px; height:140px'> </a>
                                </td>
                                <td width="365" valign="top" align="left" style="padding-left: 10px;">
                                  <table cellspacing="0" cellpadding="0" border="0">
                                    <tbody>
                                      <tr>
                                        <td> <span
                                            style="font-family:Arial,Sans-Serif;font-size:15px;font-style:normal;text-align:left;color:#336699">
                                            <a 
                                    href="https://www.indiancashback.com/store/${
																			offer?.storeName
																		}/${generateProductUrl(
																			offer?.storeName,
																			offer?.title,
																		)}?uid=${offer?.offerUid}"
                                              style="font-size:14px;text-decoration:underline;color:#336699"
                                              rel="noreferrer" target="_blank"> ${
																								offer?.title
																							}</a> </span> </td>
                                      </tr>
                                      <tr>
                                        <td> <span
                                            style="font-family:Arial,Helvetica,Sans-Serif;font-size:12px;font-weight:normal;font-style:normal;text-decoration:none;color:#333;text-align:left">
                                            <br> by <a
                                              href="https://www.indiancashback.com/store/${
																								offer?.storeName
																							}"
                                              style="text-decoration:none;font-size:12px;color:#336699" rel="noreferrer"
                                              target="_blank">${
																								offer?.storeName
																							}</a> </span> </td>
                                      </tr>
                                      <tr>
                                        <td height="10">
                                          <div width="1" style="font-size:10px;line-height:10px">
                                            &nbsp;
                                          </div>
                                        </td>
                                      </tr>
                                      <tr>
                                        <td> <span style="font-family:Arial,Helvetica,sans-serif;font-size:12px">
                                            <span>
                                              Ends In: </span> <span
                                              style="font-size:13px;font-weight:bold;color:#cc0000">${
																								offer?.endsIn
																							}</span>
                                          </span> </td>
                                      </tr>
                                      <tr>
                                        <td> <span style="font-family:Arial,Helvetica,sans-serif;font-size:12px">
                                            <span>
                                              Offer: </span> <span
                                              style="font-size:12px;font-weight:normal;color:#cc0000">${
																								offer?.offerCaption
																							}</span> </span> </td>
                                      </tr>
                                    </tbody>
                                  </table>
                                </td>
                                <td width="105" align="center" valign="center">
                                  <a 
                                  
                                           href="https://www.indiancashback.com/store/${
																							offer?.storeName
																						}/${generateProductUrl(
																							offer?.storeName,
																							offer?.title,
																						)}?uid=${offer?.offerUid}"              
                                    target="_blank"
                                    style="font-weight: bold; text-decoration: none; color:white; font-size: 12px;">
                                    <table
                                      style="padding: 0 5px; height:30px; background: #7366d9;color: white;border-radius: 5px;">
                                      <tbody>
                                        <tr>
                                          <td>
                                            Grab Deal
                                          </td>
                                        </tr>
                                      </tbody>
                                    </table>
                                  </a>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                          <div style='margin:10px 0; border-top:2px solid #eaebec; height:1px; width:620px;' />
                        </td>
                      </tr>
                    </tbody>
                  </table>
      `
}

export async function sendCashbackConfirmedMail(payload) {
	const htmlFilePath = path.resolve(__dirname, 'cashback-confirmed.html')
	const htmlTemplate = fs.readFileSync(htmlFilePath, 'utf-8')

	// Generate the offers HTML
	let offersHtml = ''
	for (const index in payload.trendingOffers) {
		offersHtml += generateOfferHtml(payload.trendingOffers[index])
	}

	let storeHtml = ''
	for (const index in payload.trendingStores) {
		storeHtml += await generateStoreHtml(payload.trendingStores[index])
	}

	// console.log(storeHtml , "store html")

	const htmlContent = htmlTemplate
		.replace('{{name}}', payload.name)
		.replace('{{date}}', payload.date)
		.replace('{{status}}', payload.status)
		.replace('{{cashback}}', payload.cashback)
		.replace('{{orderAmount}}', payload.orderAmount)
		.replace('{{confirmDate}}', payload.confirmDate)
		.replace('{{remarks}}', payload.remarks)
		.replace('{{storeName}}', payload.storeName)
		.replace('{{referenceId}}', payload.referenceId)
		.replace('<div id="trending-offers"></div>', offersHtml)
		.replace('<div id="trending-stores"></div>', storeHtml)
		.replace('{{logoUrl}}', payload.storeLogo)

	const messageData = {
		to: payload.email,
		subject: 'Your Cashback Confirmed',
		text: htmlContent,
	}
	return sendMail(messageData)
}

export async function sendCashbackTrackedMail(payload) {
	const htmlFilePath = path.resolve(__dirname, 'cashback-tracked.html')
	const htmlTemplate = fs.readFileSync(htmlFilePath, 'utf-8')

	// Generate the offers HTML
	let offersHtml = ''
	for (const index in payload.trendingOffers) {
		offersHtml += generateOfferHtml(payload.trendingOffers[index])
	}

	let storeHtml = ''
	for (const index in payload.trendingStores) {
		storeHtml += await generateStoreHtml(payload.trendingStores[index])
	}

	// console.log(storeHtml, 'store html')
	console.log(payload.storeLogo, 'store logo issue')

	const htmlContent = htmlTemplate
		.replace('{{name}}', payload.name)
		.replace('{{date}}', payload.date)
		.replace('{{status}}', payload.status)
		.replace(
			'{{cashback}}',
			payload.cashback === 'To be updated'
				? payload.cashback
				: `₹ ${payload.cashback}`,
		)
		.replace(/{{referralUrl}}/g, payload.referralUrl)
		.replace('{{orderAmount}}', payload.orderAmount)
		.replace('{{confirmDate}}', payload.confirmDate)
		.replace('{{remarks}}', payload.remarks)
		.replace('{{storeName}}', payload.storeName)
		.replace('{{referenceId}}', payload.referenceId)
		.replace('<div id="trending-offers"></div>', offersHtml)
		.replace('<div id="trending-stores"></div>', storeHtml)
		.replace('{{logoUrl}}', payload.storeLogo)

	const messageData = {
		to: payload.email,
		subject: 'Your Cashback is Tracked',
		text: htmlContent,
	}
	return sendMail(messageData)
}

export async function sendGotCommissionMail(payload) {
	const htmlFilePath = path.resolve(__dirname, 'got-referral-comission.html')
	const htmlTemplate = fs.readFileSync(htmlFilePath, 'utf-8')

	const htmlContent = htmlTemplate
		.replace('{{name}}', payload.name)
		.replace('{{expCommissionDate}}', payload.expCommissionDate)
		.replace('{{commission}}', payload.commission)
		.replace('{{purchasePerson}}', payload.purchasePerson)
		.replace('{{purchasePersonStatus}}', payload.purchasePersonStatus)

	const messageData = {
		to: payload.to,
		subject: 'Your Got Referral Commission',
		text: htmlContent,
	}
	return sendMail(messageData)
}

export async function sendConfirmedCommissionMail(payload) {
	const htmlFilePath = path.resolve(
		__dirname,
		'referral-comission-confirmed.html',
	)
	const htmlTemplate = fs.readFileSync(htmlFilePath, 'utf-8')

	const htmlContent = htmlTemplate
		.replace('{{name}}', payload.name)
		.replace('{{commission}}', payload.commission)
		.replace('{{purchasePerson}}', payload.purchasePerson)
		.replace(
			'{{purchasePersonStatus}}',
			payload.purchasePersonStatus.charAt(0).toUpperCase() +
				payload.purchasePersonStatus.slice(1),
		)

	const messageData = {
		to: payload.to,
		subject: 'Your Referral Commission Confirmed!',
		text: htmlContent,
	}
	return sendMail(messageData)
}
