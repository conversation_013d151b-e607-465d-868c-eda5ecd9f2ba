import moment from "moment-timezone";
export const dateFormatter = (dt) => {
  const months = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];
  const date = new Date(dt);
  const month = months[date.getMonth()];
  const year = date.getFullYear();
  const day = date.getUTCDate();
  const result = `${day} ${month} ${year}`;
  return result;
};

export const convertLocalToUTC = (timezone = "Asia/Kolkata") => {
  // Get the current time in the provided time zone
  const localTime = moment.tz(timezone);

  // Convert the local time to UTC and return it as ISO string
  return localTime.utc().toISOString();
};



export const convertToUTCDateTime = (dateTime, timezone = "Asia/Kolkata") => {
  if (typeof timezone !== "string") {
    throw new Error(`Time zone name must be a string, got ${timezone} [${typeof timezone}]`);
  }

  // Ensure dateTime is a valid date
  const date = moment(dateTime);
  if (!date.isValid()) {
    throw new Error(`Invalid date time provided: ${dateTime}`);
  }

  // Convert the given datetime to UTC based on the provided timezone
  const utcDateTime = date.tz(timezone).utc();
  return utcDateTime.toISOString();
};




/**
 * Converts a date range to UTC based on the provided timezone.
 * @param {string} startDate - Start date in "YYYY-MM-DD" format.
 * @param {string} endDate - End date in "YYYY-MM-DD" format.
 * @param {string} [timezone="Asia/Kolkata"] - Timezone to use for conversion (default: IST).
 * @returns {{ startUTC: Date, endUTC: Date }} - Start and end dates in UTC.
 */
export const convertDatesToUTC = (startDate, endDate, timezone = "Asia/Kolkata") => {
  // Validate timezone
  if (typeof timezone !== "string" || !moment.tz.zone(timezone)) {
    throw new Error(`Invalid timezone: ${timezone}`);
  }

  // Convert start date to the start of the day in UTC
  const startUTC = moment.tz(startDate, timezone).startOf('day').utc().toDate();

  // Convert end date to the end of the day in UTC
  const endUTC = moment.tz(endDate, timezone).endOf('day').utc().toDate();

  return { startUTC, endUTC };
};



