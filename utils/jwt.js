import Jwt from 'jsonwebtoken'
import AdminToken from '../models/admin/adminToken.js'

const generateTokens = async admin => {
	try {
		const payload = { _id: admin._id, role: admin.role, name: admin.name }
		const accessToken = Jwt.sign(
			payload,
			process.env.ACCESS_TOKEN_PRIVATE_KEY,
			{ expiresIn: '30m' },
		)
		const refreshToken = Jwt.sign(
			payload,
			process.env.REFRESH_TOKEN_PRIVATE_KEY,
			{ expiresIn: '1d' },
		)
		const adminToken = await AdminToken.findOne({ adminId: admin._id })
		if (adminToken) {
			await adminToken.remove()
		}

		await new AdminToken({ adminId: admin._id, token: refreshToken }).save()
		return Promise.resolve({ accessToken, refreshToken })
	} catch (err) {
		return Promise.reject(err)
	}
}

const verifyRefreshToken = refreshToken => {
	const privateKey = process.env.REFRESH_TOKEN_PRIVATE_KEY
	return new Promise((resolve, reject) => {
		Jwt.verify(refreshToken, privateKey, (err, tokenDetails) => {
			if (err) {
				return reject({ error: true, message: 'Invalid refresh token' })
			}
			resolve({
				tokenDetails,
				error: false,
				message: 'Valid refresh token',
			})
		})
	})
}

export { generateTokens, verifyRefreshToken }
