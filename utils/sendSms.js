export async function sendSms(to, sms, campaign, templateId) {
  const smsUrl = `http://api.msg91.com/api/v2/sendsms?campaign=${campaign}&message=${sms}&authkey=${process.env.SMS_AUTH_KEY}&mobiles=${to}&route=${process.env.SMS_ROUTE}&sender=${process.env.SMS_SENDER}&country=${process.env.SMS_COUNTRY}&DLT_TE_ID=${templateId}`;
  try {
    const response = await fetch(smsUrl, {
      method: "POST",
      headers: {
        "Content-Type": "multipart/form-data",
        "Cache-Control": "no-cache",
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    console.log("SMS sent successfully:", response);
  } catch (error) {
    console.error("Error sending SMS:", error);
  }
}

// export async function sendSms(to, sms, campaign, templateId) {
//     console.log(templateId, "templateid in send")
//     await sendSmsHelper({ to, sms, templateId, campaign: campaign })
// }
//
