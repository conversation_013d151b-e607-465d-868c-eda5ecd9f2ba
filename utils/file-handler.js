import { v2 as cloudinary } from 'cloudinary';




// // Configure your Cloudinary account
// cloudinary.config({
//     cloud_name: process.env.CLOUDINARY_NAME,
//     api_key: process.env.CLOUDINARY_API_KEY,
//     api_secret: process.env.CLOUDINARY_API_SECRET,
// });



// Function to convert SVG URL to PNG URL by adding the f_png transformation
export const convertSvgToPngUrl = (svgUrl) => {

    // Check if the URL is a Cloudinary URL
    if (!svgUrl.includes('res.cloudinary.com')) {
        // If not a Cloudinary URL, return the original URL
        return svgUrl;
    }

    if (!svgUrl.endsWith('.svg')) {
        return svgUrl;
    }

    // Extract the base URL and public ID
    const urlParts = svgUrl.split('/upload/');
    if (urlParts.length !== 2) {
        throw new Error('Invalid SVG URL');
    }

    const baseUrl = urlParts[0];
    const uploadPathAndId = urlParts[1].replace('.svg', '');

    // Construct the new URL with the f_png transformation
    const pngUrl = `${baseUrl}/upload/f_png/${uploadPathAndId}.png`;
    return pngUrl;
};








// Function to generate the transformation URL
// export const generateTransformationUrl = (publicId) => {
//     return cloudinary.url(publicId, {
//         type: "fetch", transformation: [
//             { fetch_format: "png" }
//         ]
//     })

//     //
//     // return cloudinary.url(publicId, {
//     //     format: 'png',
//     // });
// }
