import nodeMailer from 'nodemailer'

const transporter = nodeMailer.createTransport({
	port: 465,
	host: 'smtp.gmail.com',
	auth: {
		user: process.env.NODEMAILER_USERNAME,
		pass: process.env.NODEMAILER_PASSWORD,
	},
	secure: true,
})

export const sendEmail = async (email, data, userDetails) => {
	return new Promise((resolve, reject) => {
		const mailOptions = {
			from: process.env.NODEMAILER_USERNAME,
			to: email,
			subject: 'Hello from my app',
			text: 'This is the body of the email',
		}

		transporter.sendMail(mailOptions, (error, info) => {
			if (error) {
				reject(error)
			} else {
				console.log(`Email sent: ${info.response}`)
				resolve(info.response)
			}
		})
	})
}
