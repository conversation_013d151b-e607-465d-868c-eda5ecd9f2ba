import { Clicks } from "../models/clicks.js";
import { User } from "../models/user/user.js";

// Generate a random alphanumeric string of the specified length
function generateUniqueId(length, string) {
  const characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
  let result = string;
  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * characters.length);
    result += characters.charAt(randomIndex);
  }
  return result;
}

// Check if the generated ID already exists in the collection
async function checkUniqueId(uniqueId) {
  const count = await User.countDocuments({ referralCode: uniqueId });
  return count === 0;
}

// Generate and assign a unique ID to a document
export const AssignUniqueID = async () => {
  try {
    let uniqueId = generateUniqueId(8, string);
    while (!(await checkUniqueId(uniqueId))) {
      uniqueId = generateUniqueId(8, string);
    }

    return uniqueId;
  } catch (error) {
    return { error };
  }
};

// Check if the generated ID already exists in the collection
async function checkUniqueCode(uniqueId) {
  const count = await Clicks.countDocuments({
    clickUniqueId: uniqueId,
  });
  return count === 0;
}

// Generate a random alphanumeric string of the specified length
function generateUniqueCode() {
  let code = "ICB";
  const characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
  const codeLength = 10; // Remaining characters to be generated after "icb"

  for (let i = 0; i < codeLength; i++) {
    const randomIndex = Math.floor(Math.random() * characters.length);
    code += characters.charAt(randomIndex);
  }

  return code;
}

export const generateCode = async () => {
  try {
    let uniqueId = generateUniqueCode();
    while (!(await checkUniqueCode(uniqueId))) {
      uniqueId = generateUniqueCode();
    }

    return uniqueId;
  } catch (error) {
    console.log(error);
    return { error };
  }
};
