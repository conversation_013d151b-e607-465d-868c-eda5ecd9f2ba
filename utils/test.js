[
  {
    user: "userDetails",
    stores: [
      {
        store: "storeName",
        earningsDetails: {
          saleAmmount: 100,
          amountGot: 100,
          cashbackAmount: 100,
        },
      },
      {
        store: "storeName",
        earningsDetails: {
          saleAmmount: 100,
          amountGot: 100,
          cashbackAmount: 100,
        },
      },
    ],
  },
  {
    user: "userDetails",
    stores: [
      {
        store: "storeName",
        earningsDetails: {
          saleAmmount: 100,
          amountGot: 100,
          cashbackAmount: 100,
        },
      },
      {
        store: "storeName",
        earningsDetails: {
          saleAmmount: 100,
          amountGot: 100,
          cashbackAmount: 100,
        },
      },
    ],
  },
];
