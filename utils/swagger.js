import swaggerJsDoc from 'swagger-jsdoc'

const options = {
	definition: {
		openapi: '3.1.0',
		info: {
			title: ' Express API with Swagger',
			version: '1.0.0',
			description:
				'This is a simple CRUD API application made with Express and documented with Swagger',
			license: {
				name: 'MIT',
				url: 'https://spdx.org/licenses/MIT.html',
			},
			contact: {
				name: 'Indian cashback',
				url: 'https://indiancashback.com',
				email: '<EMAIL>',
			},
		},
		servers: [
			{
				url: 'http://localhost:4000/api',
			},
		],
	},
	apis: ['./routes/*.js', './routes/user/*.js', './routes/admin/*.js'],
}

const specs = swaggerJsDoc(options)

export default specs
