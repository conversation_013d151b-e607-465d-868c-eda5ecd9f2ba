import { GetObjectCommand, S3Client } from "@aws-sdk/client-s3";

import { getSignedUrl } from "@aws-sdk/s3-request-presigner";

const awsClient = new S3Client({
  region: process.env.AWS_REGION,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  },
});

export async function getAwsInvoiceUrl(filename) {
  const result = new GetObjectCommand({
    Bucket: process.env.AWS_BUCKET_NAME,
    Key: filename,
  });
  let signedUrl;
  try {
    signedUrl = await getSignedUrl(awsClient, result, {
      expiresIn: 10000,
    });
  } catch (err) {
    console.log(err, "err");
    // return err;
  }

  return {
    location: signedUrl,
    key: signedUrl,
  };
}
