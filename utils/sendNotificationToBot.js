/**
 * Sends detailed tracking information to the bot API endpoint for admin
 *
 * @async
 * @function sendNotificationToAdmin
 * @param {string} detailedInfo - The HTML formatted detailed tracking information
 * @param {string} threadId - The threadId of the bot thread
 * @returns {Promise<void>} - A promise that resolves when the message is sent
 */
export async function sendNotificationToAdmin(detailedInfo, threadId) {
	try {
		const response = await fetch(
			'https://api-main.indiancashback.com/bot/send-message',
			{
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					threadId,
					msg: detailedInfo,
				}),
			},
		)

		if (!response.ok) {
			const errorText = await response.text()
			console.error(`Bot API Error ${response.status}:`, errorText)
			console.error('Request payload:', {
				threadId,
				msg: '[MESSAGE_CONTENT_REDACTED]'
			})
			return { success: false, error: `HTTP ${response.status}` }
		}

		const result = await response.json()
		console.log('Successfully sent detailed info to bot API')
		return { success: true, result }
	} catch (error) {
		console.error('Error sending detailed info to bot API:', error.message)
		// Don't throw the error to prevent breaking the main flow
		// The tracking should continue even if bot notification fails
		return { success: false, error: error.message }
	}
}
