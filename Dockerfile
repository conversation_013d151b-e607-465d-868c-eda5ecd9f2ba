# Use the official Bun image as the base
FROM oven/bun:latest

# Set the working directory in the container to /app
WORKDIR /app

# Copy package.json and bun.lockb (if exists) to the working directory
COPY package.json bun.lock ./

# Install the project dependencies using Bun
RUN bun install

# Copy the rest of the code
COPY . .

# Expose port 4000 for the app to be accessible
EXPOSE 4000

# Define the command to run the app using Bun
CMD [ "bun", "index.js" ]
