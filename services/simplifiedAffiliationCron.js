/**
 * Simplified Affiliation Cron - Replacement for the complex affilitaionCron.js
 * This file provides a clean interface using the new modular click tracking system
 */

import {
	processAllConversions,
	processPartnerConversions,
} from './clickTracking/index.js'

/**
 * Main function to process all affiliate conversions
 * Replaces the complex logic in affilitaionCron.js
 */
export async function processAllAffiliateConversions() {
	try {
		console.log('🚀 Starting simplified affiliate conversion processing...')

		const metrics = await processAllConversions()

		console.log('✅ Affiliate conversion processing completed')
		console.log('📊 Final Metrics:', metrics)

		return metrics
	} catch (error) {
		console.error('❌ Error in affiliate conversion processing:', error)
		throw error
	}
}

/**
 * Individual partner processing functions
 * Direct access to new modular system
 */

/**
 * Process Impact conversions only
 */
export async function processImpactConversions() {
	try {
		console.log('Processing Impact conversions...')
		const metrics = await processPartnerConversions('impact')
		console.log('Impact processing completed:', metrics)
		return metrics
	} catch (error) {
		console.error('Impact processing failed:', error)
		throw error
	}
}

/**
 * Process Affalliances conversions only
 */
export async function processAffalliancesConversions() {
	try {
		console.log('Processing Affalliances conversions...')
		const metrics = await processPartnerConversions('affalliances')
		console.log('Affalliances processing completed:', metrics)
		return metrics
	} catch (error) {
		console.error('Affalliances processing failed:', error)
		throw error
	}
}

/**
 * Process Admitad conversions only
 */
export async function processAdmitadConversions() {
	try {
		console.log('Processing Admitad conversions...')
		const metrics = await processPartnerConversions('admitad')
		console.log('Admitad processing completed:', metrics)
		return metrics
	} catch (error) {
		console.error('Admitad processing failed:', error)
		throw error
	}
}

/**
 * Utility function to get processing statistics
 */
export function getProcessingStats() {
	return {
		lastRun: new Date().toISOString(),
		version: '2.0.0-simplified',
		modules: [
			'AffiliateDataFetcher',
			'ClickMatcher',
			'DuplicateDetector',
			'CurrencyConverter',
			'StoreValidator',
			'EarningCreator',
			'NotificationManager',
			'ClickTrackingOrchestrator',
		],
	}
}
