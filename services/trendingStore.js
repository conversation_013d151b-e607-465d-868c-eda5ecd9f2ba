import mongoose from 'mongoose'
import { HttpException } from '../exceptions/httpException.js'
import { TrendingStore } from '../models/stores/trending.js'

export class TrendingStoreService {
    createTrendingStore = async (storeId, payload) => {

        console.log("cretin ....")
        console.log(storeId, "store ID")
        console.log(payload, "payload")


        const store = await TrendingStore.findOne({ storeId })
        if (store) {
            throw new HttpException(409, 'the store is already trending ')
        }


        try {
            const store = await TrendingStore.findOne({ storeId })
            if (store) {
                console.log("Store is already trending")
                throw new HttpException(409, 'The store is already trending')
            }
            const trendingStore = await TrendingStore.create(payload)
            console.log("Trending store created successfully:", trendingStore)
            return trendingStore
        } catch (error) {
            console.error("Error creating trending store:", error)
            throw error
        }


    }

    getAllTrendingStores = async query => {
        const pageSize = Number(query.pageSize) || 5
        const page = Number(query.page) || 1
        const sort = query.sort ? query.sort : { _id: -1 }

        const keyword = query.search
            ? {
                $or: [
                    {
                        name: {
                            $regex: query.search,
                            $options: 'i',
                        },
                    },
                ],
            }
            : {}
        if (query.admin) {
            keyword.storeId = query.admin
        }
        const count = await TrendingStore.countDocuments({ ...keyword })
        const allTrendingStores = await TrendingStore.find({ ...keyword })
            .populate('createdBy', 'name uid avatar active')
            .populate('store', 'name uid active')
            .sort(sort)
            .limit(pageSize)
            .skip((page - 1) * pageSize)

        return {
            search: query.search,
            allTrendingStores,
            page,
            pageSize,
            pages: Math.ceil(count / pageSize),
        }
    }

    getTrendingStoreDetails = async store => {
        const trendingStore = await TrendingStore.findOne({ store }).populate(
            'store',
        )
        if (!trendingStore) {
            throw new HttpException(404, 'resource not found!')
        }
        return trendingStore
    }

    deleteTrendingStore = async storeId => {

        const trendingStore = await TrendingStore.findOne({ store: new mongoose.Types.ObjectId(storeId) })

        if (!trendingStore) {
            throw new HttpException(404, 'resource not found!')
        }
        const data = await TrendingStore.deleteOne({ storeId })
        return true
    }

    updateTrendingStorePriority = async (store, priority) => {
        const trendingStore = await TrendingStore.findById(store)
        if (!trendingStore) {
            throw new HttpException(404, 'resource not found!')
        }
        trendingStore.priority = priority
        await trendingStore.save()
        return trendingStore
    }
}
