import { HttpException } from '../exceptions/httpException.js'
import { QuickAccess } from '../models/quickAccess.js'

export class QuickAccessService {
	createQuickAccess = async payload => {
		const quickAccess = await QuickAccess.create(payload)
		if (!quickAccess) {
			throw new HttpException(404, 'failed to create new quick access')
		}
		return quickAccess
	}
	getAllQuickAccess = async query => {
		const pageSize = Number(query.pageSize) || 5
		const page = Number(query.page) || 1
		const sort = query.sort ? query.sort : { _id: -1 }

		const keyword = query.search
			? {
					$or: [
						{
							title: {
								$regex: query.search,
								$options: 'i',
							},
						},
					],
			  }
			: {}
		if (query.admin) {
			keyword.createdBy = query.admin
		}
		const count = await QuickAccess.countDocuments({ ...keyword })
		const allQuickAccess = await QuickAccess.find({ ...keyword })
			.populate('createdBy', 'name ')
			.sort({ _id: -1 })
			.limit(pageSize)
			.skip((page - 1) * pageSize)

		return {
			search: query.search,
			allQuickAccess,
			page,
			pageSize,
			pages: Math.ceil(count / pageSize),
		}
	}
	getQuickAccessDetails = async id => {
		const quickAccess = await QuickAccess.findById(id)
		if (!quickAccess) {
			throw new HttpException(404, 'resource not found!')
		}
		return quickAccess
	}
	updateQuickAccess = async (id, payload) => {
		const quickAccess = await QuickAccess.findById(id)
		if (!quickAccess) {
			throw new HttpException(404, 'resource not found!')
		}
		await QuickAccess.updateOne({ _id: id }, { $set: payload })
		return quickAccess
	}
	updateActiveStatusQuickAccess = async id => {
		const quickAccess = await QuickAccess.findById(id)
		if (!quickAccess) {
			throw new HttpException(404, 'resource not found!')
		}
		quickAccess.isActive = !quickAccess.isActive
		await quickAccess.save()
		return quickAccess
	}
	deleteQuickAccess = async id => {
		const quickAccess = await QuickAccess.findById(id)
		if (!quickAccess) {
			throw new HttpException(404, 'resource not found!')
		}
		await QuickAccess.deleteOne({ _id: id })
		return true
	}
}
