/**
 * Validates store settings for click tracking
 */
export class StoreValidator {
	constructor(storeService) {
		this.storeService = storeService
	}

	/**
	 * Validate store settings for auto-tracking
	 */
	async validateStoreSettings(storeId) {
		try {
			const store = await this.storeService.findStoreById(storeId)

			if (!store) {
				console.log(`❌ Store not found: ${storeId}`)
				return { valid: false, reason: 'Store not found' }
			}

			if (!store.active) {
				console.log(`❌ Store ${store.name} is inactive`)
				return { valid: false, reason: 'Store is inactive' }
			}

			// if (!store.autoCheck) {
			//   console.log(`❌ Store ${store.name} has autoCheck disabled`);
			//   return { valid: false, reason: 'AutoCheck disabled' };
			// }

			console.log(`✅ Store ${store.name} validation passed`)
			return { valid: true, store }
		} catch (error) {
			console.error('Store validation error:', error)
			return { valid: false, reason: 'Validation error' }
		}
	}

	/**
	 * Get store details for processing
	 */
	async getStoreForProcessing(storeId) {
		const validation = await this.validateStoreSettings(storeId)

		if (!validation.valid) {
			return null
		}

		return validation.store
	}
}
