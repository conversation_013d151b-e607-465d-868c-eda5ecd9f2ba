import { sendSms } from '../../utils/sendSms.js'
import { sendNotificationToAdmin } from '../../utils/sendNotificationToBot.js'

/**
 * Manages all notifications for tracked clicks
 */
export class NotificationManager {
	constructor(
		earningService,
		userService,
		storeService,
		adminLogService,
		adminService,
	) {
		this.earningService = earningService
		this.userService = userService
		this.storeService = storeService
		this.adminLogService = adminLogService
		this.adminService = adminService
	}

	/**
	 * Send all tracking notifications
	 */
	async sendTrackingNotifications(earning, clickData, conversionData) {
		try {
			// Validate required parameters
			if (!earning) {
				console.error('Cannot send notifications: earning is null')
				return
			}

			if (!conversionData) {
				console.error('Cannot send notifications: conversionData is null')
				return
			}

			// Determine scenario type
			const isStandaloneEarning = !clickData // Scenario 3: No click data
			const scenarioType = isStandaloneEarning
				? 'Scenario 3 (Standalone)'
				: 'Scenario 2 (Click-to-Earning)'

			console.log(
				`📧 Sending notifications for earning ${earning.uid} - ${scenarioType}`,
			)

			// For Scenario 3 (Standalone Earnings): Skip email and SMS notifications
			if (isStandaloneEarning) {
				console.log(
					'🚫 Skipping email notification - Standalone earning (Scenario 3)',
				)
				console.log(
					'🚫 Skipping SMS notification - Standalone earning (Scenario 3)',
				)
			} else {
				// Scenario 2: Send email and SMS notifications
				await this.sendEmailNotification(earning)

				if (clickData?.user && clickData.store) {
					await this.sendSMSNotification(clickData.user, clickData.store)
				} else {
					console.log('⚠️ Skipping SMS notification - incomplete click data')
				}
			}

			// Always send admin notification (for both scenarios)
			await this.sendAdminNotification(earning, clickData, conversionData)

			console.log(
				`✅ Notifications completed for earning ${earning.uid} - ${scenarioType}`,
			)
		} catch (error) {
			console.error('Error sending notifications:', error)
			// Don't throw error - notifications are not critical for the main process
		}
	}

	/**
	 * Send email notification to user
	 */
	async sendEmailNotification(earning) {
		try {
			await this.earningService.processEarningsAndSendMail(earning)
			console.log(`📧 Email sent for earning ${earning.uid}`)
		} catch (error) {
			console.error('Email notification error:', error)
		}
	}

	/**
	 * Send SMS notification to user
	 */
	async sendSMSNotification(userId, storeId) {
		try {
			const user = await this.userService.findUserWithId(userId)
			const store = await this.storeService.findStoreById(storeId)

			if (!user?.mobile || !store?.name) {
				console.log('⚠️ Missing user mobile or store name for SMS')
				return
			}

			const message = `Hi ${user.name}, we have successfully tracked your recent transaction at ${store.name} via us. Actual cashback amount will be updated soon. -IndianCashback`
			const templateId = 1307160996134040338n

			await sendSms(user.mobile, message, 'auto_track', templateId)
			console.log(`📱 SMS sent to ${user.mobile}`)
		} catch (error) {
			console.error('SMS notification error:', error)
		}
	}

	/**
	 * Send notification to admin bot
	 */
	async sendAdminNotification(earning, clickData, conversionData) {
		try {
			let user = null
			let store = null

			// Only fetch user and store if clickData exists (Scenario 2)
			if (clickData?.user) {
				user = await this.userService.findUserWithId(clickData.user)
			}
			if (clickData?.store) {
				store = await this.storeService.findStoreById(clickData.store)
			}

			// Determine scenario type for notification
			const scenarioType = clickData ? 'CLICK TRACKED' : 'STANDALONE EARNING'

			// Format timestamps with proper fallback handling
			const formatTimestamp = dateInput => {
				try {
					if (!dateInput) {
						return 'N/A'
					}
					const date = new Date(dateInput)
					if (Number.isNaN(date.getTime())) {
						return 'Invalid Date'
					}
					return date
						.toISOString()
						.replace('T', ' ')
						.replace(/\.\d{3}Z$/, ' UTC')
				} catch {
					return 'Invalid Date'
				}
			}

			const conversionTime = formatTimestamp(conversionData.datetime)
			const processingTime = formatTimestamp(new Date())

			// Fixed: Using plain text format instead of HTML to avoid bot API parsing errors
			let detailedInfo = `🎉 ${scenarioType}!

`

			// For standalone earnings, exclude user, store, and click-related info
			if (clickData) {
				// Scenario 2: Include all information
				detailedInfo += `👤 User : ${user?.name || 'N/A'}
🔗 Reference ID: ${clickData.referenceId || 'N/A'}
💰 Earning UID: ${earning.uid}
🏪 Store Name: ${store?.name || 'N/A'}
🛒 Sale Amount: ₹${conversionData.saleAmount || '0.00'}
✅ Approved Payout: ₹${conversionData.approvedPayout || '0.00'}
🤝 Partner: ${conversionData.partner}
📦 Order ID: ${conversionData.adId}
🕐 Conversion Time: ${conversionTime}
⚡ Processed Time: ${processingTime}`
			} else {
				// Scenario 3: Exclude user, store, and click ID
				detailedInfo += `💰 Earning UID: ${earning.uid}
🔗 Reference ID: ${earning.referenceId || 'N/A'}
🛒 Sale Amount: ₹${conversionData.saleAmount || '0.00'}
✅ Approved Payout: ₹${conversionData.approvedPayout || '0.00'}
🤝 Partner: ${conversionData.partner}
📦 Order ID: ${conversionData.adId}
🕐 Conversion Time: ${conversionTime}
⚡ Processed Time: ${processingTime}`
			}

			const result = await sendNotificationToAdmin(detailedInfo, '1576')
			if (result.success) {
				console.log(`✅ Admin notification sent for earning ${earning.uid}`)
			} else {
				console.warn(`⚠️ Failed to send admin notification: ${result.error}`)
			}
		} catch (error) {
			console.error('❌ Admin notification error:', error)
		}
	}

	/**
	 * Create admin logs for audit trail
	 */
	async createAdminLogs(earning, clickData) {
		try {
			const autoAdmin = await this.adminService.getAdminDetailsWithEmail(
				'<EMAIL>',
			)

			const logs = [
				{
					admin: autoAdmin._id,
					log: `${autoAdmin.name} created new User Earning id ${earning.uid}`,
				},
				{
					admin: autoAdmin._id,
					log: `${autoAdmin.name} updated User cashback click status to "tracked" ${clickData.uid}. earning uid:${earning.uid}`,
				},
			]

			await this.adminLogService.createMultipleLogs(logs)
			console.log(`📝 Admin logs created for earning ${earning.uid}`)
		} catch (error) {
			console.error('Admin log creation error:', error)
		}
	}

	/**
	 * Send error notification to admin
	 */
	async sendErrorNotification(error, context) {
		try {
			// Fixed: Using plain text format instead of HTML to avoid bot API parsing errors
			const errorInfo = `❌ CLICK TRACKING ERROR

Context: ${context}
Error: ${error.message}
Time: ${new Date().toISOString()}
Stack: ${error.stack}`

			const result = await sendNotificationToAdmin(errorInfo, '1576')
			if (result.success) {
				console.log(`✅ Error notification sent to admin for: ${context}`)
			} else {
				console.warn(`⚠️ Failed to send error notification: ${result.error}`)
			}
		} catch (notificationError) {
			console.error('❌ Error notification failed:', notificationError)
		}
	}
}
