/**
 * Enhanced payout validation utility with comprehensive diagnostic logging
 * Provides detailed context for debugging payout validation failures
 */
export class PayoutValidator {
	constructor() {
		this.validationRules = {
			minAmount: 0.01, // Minimum payout amount in INR
			maxAmount: 1000000, // Maximum reasonable payout amount in INR
			requiredFields: ['approvedPayout', 'adId', 'partner'],
			numericFields: ['approvedPayout', 'saleAmount'],
		}
	}

	/**
	 * Validate standalone earning payout with comprehensive diagnostics
	 */
	validateStandalonePayout(conversionData) {
		const validationContext = {
			timestamp: new Date().toISOString(),
			orderId: conversionData.adId || 'MISSING',
			partner: conversionData.partner || 'UNKNOWN',
			validationRules: this.validationRules,
		}

		const diagnostics = {
			rawData: {
				approvedPayout: conversionData.approvedPayout,
				saleAmount: conversionData.saleAmount,
				currency: conversionData.currency || 'INR',
				originalPayout: conversionData.originalPayout,
				originalCurrency: conversionData.originalCurrency,
			},
			parsedData: {},
			validationResults: {},
			errors: [],
			warnings: [],
		}

		// Parse and validate payout amount
		const payoutValidation = this.validatePayoutAmount(
			conversionData,
			diagnostics,
		)

		// Validate required fields
		const fieldsValidation = this.validateRequiredFields(
			conversionData,
			diagnostics,
		)

		// Validate data consistency
		const consistencyValidation = this.validateDataConsistency(
			conversionData,
			diagnostics,
		)

		const isValid =
			payoutValidation.valid &&
			fieldsValidation.valid &&
			consistencyValidation.valid

		return {
			valid: isValid,
			diagnostics,
			validationContext,
			summary: this.generateValidationSummary(diagnostics, validationContext),
		}
	}

	/**
	 * Validate payout amount with detailed analysis
	 */
	validatePayoutAmount(conversionData, diagnostics) {
		const payout = conversionData.approvedPayout

		// Record raw payout data
		diagnostics.rawData.payoutType = typeof payout
		diagnostics.rawData.payoutString = String(payout)

		// Parse payout amount
		let parsedPayout
		try {
			parsedPayout = Number.parseFloat(payout)
			diagnostics.parsedData.approvedPayout = parsedPayout
			diagnostics.parsedData.isValidNumber = !Number.isNaN(parsedPayout)
		} catch (error) {
			diagnostics.errors.push({
				field: 'approvedPayout',
				issue: 'parsing_failed',
				details: `Failed to parse payout: ${error.message}`,
				value: payout,
			})
			return { valid: false }
		}

		// Validate payout exists and is numeric
		if (!payout && payout !== 0) {
			diagnostics.errors.push({
				field: 'approvedPayout',
				issue: 'missing_value',
				details: 'Payout amount is null, undefined, or empty',
				value: payout,
				expectedType: 'number > 0',
			})
			return { valid: false }
		}

		if (Number.isNaN(parsedPayout)) {
			diagnostics.errors.push({
				field: 'approvedPayout',
				issue: 'invalid_number',
				details: 'Payout amount is not a valid number',
				value: payout,
				parsedValue: parsedPayout,
				expectedType: 'valid number > 0',
			})
			return { valid: false }
		}

		// Validate payout is positive
		if (parsedPayout <= 0) {
			diagnostics.errors.push({
				field: 'approvedPayout',
				issue: 'non_positive_amount',
				details: 'Payout amount must be greater than 0',
				value: payout,
				parsedValue: parsedPayout,
				minimumRequired: this.validationRules.minAmount,
				violationType: parsedPayout === 0 ? 'zero_amount' : 'negative_amount',
			})
			return { valid: false }
		}

		// Validate payout is within reasonable range
		if (parsedPayout < this.validationRules.minAmount) {
			diagnostics.warnings.push({
				field: 'approvedPayout',
				issue: 'below_minimum',
				details: 'Payout amount is below minimum threshold',
				value: parsedPayout,
				minimumRequired: this.validationRules.minAmount,
			})
		}

		if (parsedPayout > this.validationRules.maxAmount) {
			diagnostics.warnings.push({
				field: 'approvedPayout',
				issue: 'above_maximum',
				details: 'Payout amount is unusually high',
				value: parsedPayout,
				maximumExpected: this.validationRules.maxAmount,
			})
		}

		diagnostics.validationResults.payoutAmount = {
			valid: true,
			parsedValue: parsedPayout,
			withinRange:
				parsedPayout >= this.validationRules.minAmount &&
				parsedPayout <= this.validationRules.maxAmount,
		}

		return { valid: true }
	}

	/**
	 * Validate required fields for standalone earnings
	 */
	validateRequiredFields(conversionData, diagnostics) {
		let allFieldsValid = true

		for (const field of this.validationRules.requiredFields) {
			const value = conversionData[field]
			const isEmpty =
				!value || (typeof value === 'string' && value.trim() === '')

			if (isEmpty) {
				diagnostics.errors.push({
					field,
					issue: 'missing_required_field',
					details: `Required field '${field}' is missing or empty`,
					value,
					required: true,
				})
				allFieldsValid = false
			} else {
				diagnostics.validationResults[field] = {
					valid: true,
					value,
					type: typeof value,
				}
			}
		}

		return { valid: allFieldsValid }
	}

	/**
	 * Validate data consistency and relationships
	 */
	validateDataConsistency(conversionData, diagnostics) {
		const warnings = []

		// Check sale amount vs payout relationship
		if (conversionData.saleAmount && conversionData.approvedPayout) {
			const saleAmount = Number.parseFloat(conversionData.saleAmount)
			const payout = Number.parseFloat(conversionData.approvedPayout)

			if (!Number.isNaN(saleAmount) && !Number.isNaN(payout)) {
				const payoutPercentage = (payout / saleAmount) * 100

				diagnostics.parsedData.saleAmount = saleAmount
				diagnostics.parsedData.payoutPercentage = payoutPercentage

				if (payout > saleAmount) {
					warnings.push({
						field: 'payout_vs_sale',
						issue: 'payout_exceeds_sale',
						details: 'Payout amount exceeds sale amount',
						saleAmount,
						payoutAmount: payout,
						payoutPercentage,
					})
				}

				if (payoutPercentage > 50) {
					warnings.push({
						field: 'payout_vs_sale',
						issue: 'high_payout_percentage',
						details: 'Payout percentage is unusually high',
						payoutPercentage,
						saleAmount,
						payoutAmount: payout,
					})
				}
			}
		}

		// Check currency conversion consistency
		if (conversionData.originalPayout && conversionData.originalCurrency) {
			diagnostics.parsedData.currencyConversion = {
				original: {
					amount: conversionData.originalPayout,
					currency: conversionData.originalCurrency,
				},
				converted: {
					amount: conversionData.approvedPayout,
					currency: conversionData.currency || 'INR',
				},
			}
		}

		diagnostics.warnings.push(...warnings)
		return { valid: true } // Consistency issues are warnings, not validation failures
	}

	/**
	 * Generate human-readable validation summary
	 */
	generateValidationSummary(diagnostics, validationContext) {
		const errorCount = diagnostics.errors.length
		const warningCount = diagnostics.warnings.length

		let summary = `Payout validation for order ${validationContext.orderId} (${validationContext.partner}): `

		if (errorCount === 0) {
			summary += '✅ PASSED'
			if (warningCount > 0) {
				summary += ` with ${warningCount} warning(s)`
			}
		} else {
			summary += `❌ FAILED with ${errorCount} error(s)`
			if (warningCount > 0) {
				summary += ` and ${warningCount} warning(s)`
			}
		}

		// Add key diagnostic info
		if (diagnostics.parsedData.approvedPayout !== undefined) {
			summary += ` | Payout: ₹${diagnostics.parsedData.approvedPayout}`
		}

		if (diagnostics.parsedData.saleAmount !== undefined) {
			summary += ` | Sale: ₹${diagnostics.parsedData.saleAmount}`
		}

		return summary
	}

	/**
	 * Create enhanced error for logging systems
	 */
	createEnhancedError(validationResult, conversionData) {
		const { diagnostics, validationContext, summary } = validationResult

		const enhancedError = new Error(
			'Invalid payout amount for standalone earning',
		)
		enhancedError.name = 'PayoutValidationError'
		enhancedError.validationSummary = summary
		enhancedError.diagnostics = diagnostics
		enhancedError.validationContext = validationContext
		enhancedError.conversionData = {
			orderId: conversionData.adId,
			partner: conversionData.partner,
			approvedPayout: conversionData.approvedPayout,
			saleAmount: conversionData.saleAmount,
			currency: conversionData.currency,
			originalPayout: conversionData.originalPayout,
			originalCurrency: conversionData.originalCurrency,
			affiliateInfo1: conversionData.affiliateInfo1,
			affiliateInfo2: conversionData.affiliateInfo2,
		}

		return enhancedError
	}
}
