/**
 * Handles currency conversion to INR
 */
export class CurrencyConverter {
	constructor() {
		this.cache = new Map()
		this.cacheExpiry = 60 * 60 * 1000 // 1 hour cache
	}

	/**
	 * Convert amount to INR
	 */
	async convertToINR(amount, fromCurrency) {
		if (!fromCurrency || fromCurrency === 'INR') {
			return Number.parseFloat(amount).toFixed(2)
		}

		try {
			const exchangeRate = await this.getExchangeRate(fromCurrency)
			const convertedAmount = Number.parseFloat(amount) * exchangeRate

			console.log(
				`💱 Converted ${amount} ${fromCurrency} to ₹${convertedAmount.toFixed(
					2,
				)}`,
			)
			return convertedAmount.toFixed(2)
		} catch (error) {
			console.error(`Currency conversion error for ${fromCurrency}:`, error)
			// Return original amount on error
			return Number.parseFloat(amount).toFixed(2)
		}
	}

	/**
	 * Get exchange rate with caching
	 */
	async getExchangeRate(fromCurrency) {
		const cacheKey = `${fromCurrency}_INR`
		const cached = this.cache.get(cacheKey)

		// Return cached rate if still valid
		if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
			return cached.rate
		}

		try {
			const url = `https://v6.exchangerate-api.com/v6/${process.env.EXCHANGE_RATE_API_KEY}/pair/${fromCurrency}/INR`
			const response = await fetch(url)
			const data = await response.json()

			if (data.result === 'success') {
				const rate = data.conversion_rate

				// Cache the rate
				this.cache.set(cacheKey, {
					rate,
					timestamp: Date.now(),
				})

				return rate
			}

			throw new Error(`API returned: ${data.result}`)
		} catch (error) {
			console.error('Exchange rate API error:', error)
			// Return fallback rates for common currencies
			return this.getFallbackRate(fromCurrency)
		}
	}

	/**
	 * Fallback exchange rates (approximate)
	 */
	getFallbackRate(fromCurrency) {
		const fallbackRates = {
			USD: 83.0,
			EUR: 90.0,
			GBP: 105.0,
			AUD: 55.0,
			CAD: 61.0,
			SGD: 62.0,
		}

		return fallbackRates[fromCurrency] || 1
	}

	/**
	 * Convert both sale amount and payout
	 */
	async convertConversionAmounts(conversionData) {
		// Validate input
		if (!conversionData) {
			throw new Error('Conversion data is required')
		}

		// Use safe defaults for missing properties
		const currency = conversionData.currency || 'INR'
		const saleAmount = conversionData.saleAmount || 0
		const approvedPayout = conversionData.approvedPayout || 0

		if (!currency || currency === 'INR') {
			return {
				...conversionData,
				currency: 'INR',
				saleAmount: Number.parseFloat(saleAmount).toFixed(2),
				approvedPayout: Number.parseFloat(approvedPayout).toFixed(2),
			}
		}

		const convertedSaleAmount = await this.convertToINR(saleAmount, currency)
		const convertedPayout = await this.convertToINR(approvedPayout, currency)

		return {
			...conversionData,
			currency: 'INR',
			saleAmount: convertedSaleAmount,
			approvedPayout: convertedPayout,
			originalCurrency: currency,
			originalSaleAmount: saleAmount,
			originalPayout: approvedPayout,
		}
	}
}
