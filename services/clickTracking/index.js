/**
 * Main entry point for the simplified click tracking system
 */

import { ClickTrackingOrchestrator } from './ClickTrackingOrchestrator.js'

// Import existing services
import { ClickService } from '../cashbackClick.js'
import { EarningService } from '../earning.js'
import { StoreService } from '../store.js'
import { UserService } from '../user.js'
import { AdminService } from '../admin.js'
import { AdminLogService } from '../adminLog.js'
import { AffiliationService } from '../affiliation.js'

/**
 * Initialize the click tracking system
 */
export function initializeClickTracking() {
	// Initialize all required services
	const services = {
		clickService: new ClickService(),
		earningService: new EarningService(),
		storeService: new StoreService(),
		userService: new UserService(),
		adminService: new AdminService(),
		adminLogService: new AdminLogService(),
		affiliationService: new AffiliationService(),
	}

	// Create and return orchestrator
	return new ClickTrackingOrchestrator(services)
}

/**
 * Main function to process all conversions (replaces existing cron functions)
 */
export async function processAllConversions() {
	const orchestrator = initializeClickTracking()
	await orchestrator.processAllConversions()
	return orchestrator.getMetrics()
}

/**
 * Process conversions for specific partner
 */
export async function processPartnerConversions(partner) {
	const orchestrator = initializeClickTracking()
	await orchestrator.processPartnerConversions(partner)
	return orchestrator.getMetrics()
}

/**
 * Individual partner processing functions
 */
export async function processImpactConversions() {
	console.log('Running Impact conversion processing...')
	return await processPartnerConversions('impact')
}

export async function processAffalliancesConversions() {
	console.log('Running Affalliances conversion processing...')
	return await processPartnerConversions('affalliances')
}

export async function processAdmitadConversions() {
	console.log('Running Admitad conversion processing...')
	return await processPartnerConversions('admitad')
}

// Export all modules for advanced usage
export { ClickTrackingOrchestrator } from './ClickTrackingOrchestrator.js'
export { AffiliateDataFetcher } from './AffiliateDataFetcher.js'
export { ClickMatcher } from './ClickMatcher.js'
export { DuplicateDetector } from './DuplicateDetector.js'
export { CurrencyConverter } from './CurrencyConverter.js'
export { StoreValidator } from './StoreValidator.js'
export { EarningCreator } from './EarningCreator.js'
export { NotificationManager } from './NotificationManager.js'
