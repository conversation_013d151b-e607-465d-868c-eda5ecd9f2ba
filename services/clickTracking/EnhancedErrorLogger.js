/**
 * Enhanced error logging utility for click tracking operations
 * Provides structured diagnostic logging following project patterns
 */
export class EnhancedErrorLogger {
	constructor(notificationManager) {
		this.notificationManager = notificationManager
	}

	/**
	 * Log payout validation error with comprehensive diagnostics
	 */
	async logPayoutValidationError(
		validationResult,
		conversionData,
		trackingRecords,
	) {
		const { diagnostics, validationContext, summary } = validationResult

		// 1. Console logging with project's emoji pattern
		console.error(`❌ ${summary}`)
		console.error('🔍 Diagnostic Details:', {
			orderId: conversionData.adId || 'N/A',
			partner: conversionData.partner || 'UNKNOWN',
			rawPayout: conversionData.approvedPayout,
			parsedPayout: diagnostics.parsedData.approvedPayout,
			errors: diagnostics.errors.length,
			warnings: diagnostics.warnings.length,
			timestamp: validationContext.timestamp,
		})

		// Log detailed error breakdown
		if (diagnostics.errors.length > 0) {
			console.error('📋 Validation Errors:')
			diagnostics.errors.forEach((error, index) => {
				console.error(`  ${index + 1}. ${error.field}: ${error.issue}`)
				console.error(`     Details: ${error.details}`)
				console.error(`     Value: ${JSON.stringify(error.value)}`)
				if (error.expectedType) {
					console.error(`     Expected: ${error.expectedType}`)
				}
			})
		}

		// Log warnings if any
		if (diagnostics.warnings.length > 0) {
			console.warn('⚠️ Validation Warnings:')
			diagnostics.warnings.forEach((warning, index) => {
				console.warn(`  ${index + 1}. ${warning.field}: ${warning.issue}`)
				console.warn(`     Details: ${warning.details}`)
			})
		}

		// 2. Add to tracking records with enhanced context
		const errorRecord = this.createEnhancedErrorRecord(
			validationResult,
			conversionData,
		)
		trackingRecords.errors.push(errorRecord)

		// 3. Send enhanced admin notification
		await this.sendEnhancedErrorNotification(validationResult, conversionData)

		return errorRecord
	}

	/**
	 * Create enhanced error record for tracking system
	 */
	createEnhancedErrorRecord(validationResult, conversionData) {
		const { diagnostics, validationContext, summary } = validationResult

		return {
			// Standard tracking record fields
			orderId: conversionData.adId || 'N/A',
			clickId:
				conversionData.affiliateInfo1 || conversionData.affiliateInfo2 || 'N/A',
			partner: conversionData.partner || 'UNKNOWN',
			errorMessage: 'Invalid payout amount for standalone earning',
			errorStack: 'PayoutValidationError',
			saleAmount: conversionData.saleAmount || 0,
			approvedPayout: conversionData.approvedPayout || 0,
			timestamp: validationContext.timestamp,

			// Enhanced diagnostic fields
			errorType: 'PAYOUT_VALIDATION_FAILURE',
			validationSummary: summary,
			diagnostics: {
				rawData: diagnostics.rawData,
				parsedData: diagnostics.parsedData,
				errorCount: diagnostics.errors.length,
				warningCount: diagnostics.warnings.length,
				primaryErrors: diagnostics.errors.map(e => ({
					field: e.field,
					issue: e.issue,
					details: e.details,
				})),
				dataQualityIssues: diagnostics.warnings.map(w => ({
					field: w.field,
					issue: w.issue,
					details: w.details,
				})),
			},
			conversionContext: {
				currency: conversionData.currency || 'INR',
				originalCurrency: conversionData.originalCurrency,
				originalPayout: conversionData.originalPayout,
				originalSaleAmount: conversionData.originalSaleAmount,
				datetime: conversionData.datetime,
				clickDate: conversionData.clickDate,
				purchaseDate: conversionData.purchaseDate,
			},
			validationRules: validationContext.validationRules,
		}
	}

	/**
	 * Send enhanced error notification to admin
	 */
	async sendEnhancedErrorNotification(validationResult, conversionData) {
		if (!this.notificationManager) {
			console.warn(
				'⚠️ No notification manager available for enhanced error notification',
			)
			return
		}

		try {
			const { diagnostics, validationContext, summary } = validationResult

			// Create detailed error notification following project's plain text pattern
			const errorInfo = `❌ PAYOUT VALIDATION FAILURE

${summary}

📋 Error Details:
${diagnostics.errors
	.map(
		(error, i) =>
			`${i + 1}. ${error.field}: ${error.issue}
   Details: ${error.details}
   Value: ${JSON.stringify(error.value)}`,
	)
	.join('\n')}

📊 Conversion Data:
Order ID: ${conversionData.adId || 'N/A'}
Partner: ${conversionData.partner || 'UNKNOWN'}
Raw Payout: ${conversionData.approvedPayout}
Sale Amount: ${conversionData.saleAmount || 'N/A'}
Currency: ${conversionData.currency || 'INR'}
Click ID: ${
				conversionData.affiliateInfo1 || conversionData.affiliateInfo2 || 'N/A'
			}

${
	diagnostics.warnings.length > 0
		? `⚠️ Warnings:
${diagnostics.warnings
	.map((w, i) => `${i + 1}. ${w.field}: ${w.issue}`)
	.join('\n')}`
		: ''
}

🕐 Timestamp: ${validationContext.timestamp}`

			await this.notificationManager.sendErrorNotification(
				new Error('Payout validation failed'),
				`Payout validation for ${conversionData.adId || 'unknown order'}`,
			)

			// Also send the detailed diagnostic info
			const result = await this.notificationManager.sendErrorNotification(
				{ message: errorInfo, stack: 'PayoutValidationDiagnostics' },
				'Detailed payout validation diagnostics',
			)

			if (result?.success) {
				console.log(
					`✅ Enhanced error notification sent for order ${conversionData.adId}`,
				)
			}
		} catch (notificationError) {
			console.error(
				'❌ Failed to send enhanced error notification:',
				notificationError,
			)
		}
	}

	/**
	 * Log general validation warning with context
	 */
	logValidationWarning(message, conversionData, additionalContext = {}) {
		console.warn(`⚠️ ${message}`)
		console.warn('📋 Context:', {
			orderId: conversionData.adId || 'N/A',
			partner: conversionData.partner || 'UNKNOWN',
			approvedPayout: conversionData.approvedPayout,
			saleAmount: conversionData.saleAmount,
			timestamp: new Date().toISOString(),
			...additionalContext,
		})
	}

	/**
	 * Log successful validation with key metrics
	 */
	logValidationSuccess(validationResult, _conversionData) {
		const { diagnostics, summary } = validationResult

		console.log(`✅ ${summary}`)

		if (diagnostics.warnings.length > 0) {
			console.warn(`⚠️ ${diagnostics.warnings.length} warning(s) detected:`)
			diagnostics.warnings.forEach((warning, index) => {
				console.warn(`  ${index + 1}. ${warning.field}: ${warning.issue}`)
			})
		}

		// Log key metrics for monitoring
		if (diagnostics.parsedData.payoutPercentage !== undefined) {
			const percentage = diagnostics.parsedData.payoutPercentage.toFixed(2)
			console.log(`📊 Payout percentage: ${percentage}% of sale amount`)
		}
	}

	/**
	 * Create structured error for external error tracking systems
	 */
	createStructuredError(validationResult, conversionData) {
		const enhancedError = new Error('Payout validation failed')
		enhancedError.name = 'PayoutValidationError'
		enhancedError.code = 'PAYOUT_VALIDATION_FAILURE'
		enhancedError.severity = 'ERROR'
		enhancedError.category = 'STANDALONE_EARNING_VALIDATION'

		// Attach diagnostic data
		enhancedError.diagnostics = validationResult.diagnostics
		enhancedError.validationContext = validationResult.validationContext
		enhancedError.conversionData = {
			orderId: conversionData.adId,
			partner: conversionData.partner,
			approvedPayout: conversionData.approvedPayout,
			saleAmount: conversionData.saleAmount,
			currency: conversionData.currency,
		}

		return enhancedError
	}
}
