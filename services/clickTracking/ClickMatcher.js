/**
 * Matches affiliate conversions to existing click records
 */
export class ClickMatcher {
  constructor(clickService) {
    this.clickService = clickService;
  }

  /**
   * Extract click ID from conversion data
   */
  extractClickId(affiliateInfo1, affiliateInfo2) {
    return affiliateInfo1 || affiliateInfo2;
  }

  /**
   * Find matching click record for conversion
   */
  async findMatchingClick(conversionData) {
    const clickId = this.extractClickId(conversionData.affiliateInfo1, conversionData.affiliateInfo2);
    
    if (!clickId) {
      console.log('❌ No click ID found in conversion data');
      return null;
    }

    try {
      const clickData = await this.clickService.findClickByRefId(clickId);
      
      if (!clickData) {
        console.log(`❌ No click found for ID: ${clickId}`);
        return null;
      }

      console.log(`✅ Found matching click: ${clickData.referenceId} (Status: ${clickData.status})`);
      return clickData;
    } catch (error) {
      console.error('Error finding click:', error);
      return null;
    }
  }

  /**
   * Validate click data for processing
   */
  validateClick(clickData) {
    if (!clickData) {
      return { valid: false, reason: 'Click data is null' };
    }

    if (!clickData.user) {
      return { valid: false, reason: 'Click has no associated user' };
    }

    if (!clickData.store) {
      return { valid: false, reason: 'Click has no associated store' };
    }

    if (!clickData.affiliation) {
      return { valid: false, reason: 'Click has no associated affiliation' };
    }

    return { valid: true };
  }
}