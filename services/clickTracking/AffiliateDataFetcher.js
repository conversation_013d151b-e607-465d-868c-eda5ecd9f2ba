import { configDotenv } from 'dotenv'
import { Agent } from 'node:https'

configDotenv()

/**
 * Fetches conversion data from different affiliate networks with improved error handling
 */
export class AffiliateDataFetcher {
	constructor() {
		this.dateFormatter = date => {
			const d = new Date(date)
			return [
				d.getFullYear(),
				String(d.getMonth() + 1).padStart(2, '0'),
				String(d.getDate()).padStart(2, '0'),
			].join('-')
		}
	}

	/**
	 * Fetch conversions from Impact API with improved error handling
	 * Fetches data for the last 1 day only
	 */
	async fetchImpactConversions(daysBack = 1) {
		try {
			console.log(`Fetching Impact conversions for the last ${daysBack} day`)

			const endDate = new Date()
			endDate.setDate(endDate.getDate() + 1) // Tomorrow (to include today)
			const startDate = new Date()
			startDate.setDate(startDate.getDate() - daysBack) // 1 day ago

			const url = `https://api.impact.com/Mediapartners/IRT7X4JxBMoe3498865QR2fcBPxdtvJQD1/Actions?ActionDateStart=${this.dateFormatter(
				startDate,
			)}T00:00:00Z&ActionDateEnd=${this.dateFormatter(endDate)}T00:00:00Z`

			console.log(
				`Fetching Impact data from ${this.dateFormatter(
					startDate,
				)} to ${this.dateFormatter(endDate)}`,
			)

			const conversions = await this.fetchImpactPeriodData(
				url,
				startDate,
				endDate,
				1,
			)

			console.log(
				`Impact API returned ${conversions.length} conversions from last ${daysBack} day`,
			)
			return conversions
		} catch (error) {
			console.error('Impact fetch error:', error.message)
			return []
		}
	}

	/**
	 * Fetch Impact data for a single period
	 */
	async fetchImpactPeriodData(url, startDate, endDate, periodNumber) {
		const headers = {
			Accept: 'application/json',
			'Content-Type': 'application/x-www-form-urlencoded',
			Cookie:
				'epersist=268449290.40515.0000; session=X1qieBAfKvTUklA3xvU8DQ|1660042687|fDQ_Rdxkj09cCTCn2ZV32fJRjMNKfos5uGodjBHHM1ukrZlwiWg-X3Uxtlbu2TEZqCTEzu0Xha-dN3rlMnDTh9dZuUjr8_LdYXCp3VXKHMU|T7iYzjTNqdvjNv3aWsYir6jriR0',
			Authorization: `Basic ${process.env.CALL_IMPACT_KEY}`,
			u: 'IRT7X4JxBMoe3498865QR2fcBPxdtvJQD1:ana_CPWCYd4XytFeNTz-ERZJ4zTjcntv',
		}

		try {
			console.log(
				`Fetching Impact period ${periodNumber}: ${this.dateFormatter(
					startDate,
				)} to ${this.dateFormatter(endDate)}`,
			)

			// Check if API key is available
			if (!process.env.CALL_IMPACT_KEY) {
				console.warn('CALL_IMPACT_KEY not found in environment variables')
				return []
			}

			const agent = new Agent({ rejectUnauthorized: false })
			const response = await fetch(url, {
				method: 'GET',
				headers,
				agent,
			})

			if (!response.ok) {
				const errorText = await response.text()
				console.error(
					`Impact API Error ${response.status} for period ${periodNumber}:`,
					errorText,
				)
				return []
			}

			const result = await response.json()
			console.log(
				`Impact period ${periodNumber} returned ${result.Actions?.length || 0
				} actions`,
			)

			return (
				result.Actions?.filter(
					conversion => conversion.SubId1 && conversion.SubId1 !== 1000,
				).map(conversion => ({
					saleAmount: conversion.Amount,
					approvedPayout: conversion.Payout,
					affiliateInfo1: conversion.SubId1,
					affiliateInfo2: conversion.SubId1,
					adId: conversion.Id,
					datetime: new Date(conversion.EventDate)
						.toISOString()
						.slice(0, 19)
						.replace('T', ' '),
					partner: 'Impact',
					advertiserInfo: conversion.AdId,
					currency: conversion.Currency || 'INR',
					notes: '',
					otherInfo: '',
				})) || []
			)
		} catch (error) {
			console.error(`Impact period ${periodNumber} fetch error:`, error.message)
			return []
		}
	}

	/**
	 * Fetch conversions from Analytics (Trackier) API with improved error handling
	 * Fetches data for the last 1 day only
	 */
	async fetchAnalyticsConversions(daysBack = 1) {
		try {
			console.log(
				`Fetching Analytics conversions for the last ${daysBack} day`,
			)

			const endDate = new Date()
			endDate.setDate(endDate.getDate() + 1) // Tomorrow (to include today)
			const startDate = new Date()
			startDate.setDate(startDate.getDate() - daysBack) // 1 day ago

			const url = `https://api.trackier.com/v2/publishers/conversions?apikey=${process.env.CALL_ANALYTICS_KEY
				}&start=${this.dateFormatter(startDate)}&end=${this.dateFormatter(
					endDate,
				)}&page=1`

			console.log(
				`Fetching Analytics data from ${this.dateFormatter(
					startDate,
				)} to ${this.dateFormatter(endDate)}`,
			)

			const conversions = await this.fetchAnalyticsPeriodData(
				url,
				startDate,
				endDate,
				1,
			)
			console.log(
				`Analytics API returned ${conversions.length} conversions from last ${daysBack} day`,
			)
			return conversions
		} catch (error) {
			console.error('Analytics fetch error:', error.message)
			return []
		}
	}

	/**
	 * Fetch Analytics data for a single period
	 */
	async fetchAnalyticsPeriodData(url, startDate, endDate, periodNumber) {
		try {
			console.log(
				`Fetching Analytics period ${periodNumber}: ${this.dateFormatter(
					startDate,
				)} to ${this.dateFormatter(endDate)}`,
			)

			// Check if API key is available
			if (!process.env.CALL_ANALYTICS_KEY) {
				console.warn('CALL_ANALYTICS_KEY not found in environment variables')
				return []
			}

			const agent = new Agent({ rejectUnauthorized: false })
			const response = await fetch(url, {
				method: 'GET',
				agent,
			})

			if (!response.ok) {
				const errorText = await response.text()
				console.error(
					`Analytics API Error ${response.status} for period ${periodNumber}:`,
					errorText,
				)
				return []
			}

			const result = await response.json()
			console.log(
				`Analytics period ${periodNumber} returned ${result.conversions?.length || 0
				} conversions`,
			)

			return (
				result.conversions
					?.filter(record => record.p1)
					.map(record => {
						let advertiserInfo = ''
						if (record.sub1) {
							advertiserInfo += record.sub1
						}
						if (record.sub2) {
							advertiserInfo += `, ${record.sub2}`
						}

						return {
							saleAmount: record.sale || 0,
							approvedPayout: record.payout,
							affiliateInfo1: record.p1,
							affiliateInfo2: '',
							adId: record.id,
							datetime: new Date(record.created)
								.toISOString()
								.slice(0, 19)
								.replace('T', ' '),
							partner: 'analytics',
							advertiserInfo,
							currency: 'INR',
							clickDate: record.created,
							purchaseDate: record.created,
							notes: '',
							otherInfo: '',
						}
					}) || []
			)
		} catch (error) {
			console.error(
				`Analytics period ${periodNumber} fetch error:`,
				error.message,
			)
			return []
		}
	}

	/**
	 * Fetch conversions from Affalliances (Trackier) API with improved error handling
	 * Fetches data for the last 1 day only
	 */
	async fetchAffalliancesConversions(daysBack = 1) {
		try {
			console.log(
				`Fetching Affalliances conversions for the last ${daysBack} day`,
			)

			const endDate = new Date()
			endDate.setDate(endDate.getDate() + 1) // Tomorrow (to include today)
			const startDate = new Date()
			startDate.setDate(startDate.getDate() - daysBack) // 1 day ago

			const url = `https://api.trackier.com/v2/publishers/conversions?apikey=${process.env.CALL_AFFALLIANCES_KEY
				}&start=${this.dateFormatter(startDate)}&end=${this.dateFormatter(
					endDate,
				)}&page=1`

			console.log(
				`Fetching Affalliances data from ${this.dateFormatter(
					startDate,
				)} to ${this.dateFormatter(endDate)}`,
			)

			const conversions = await this.fetchAffalliancesPeriodData(
				url,
				startDate,
				endDate,
				1,
			)

			console.log(
				`Affalliances API returned ${conversions.length} conversions from last ${daysBack} day`,
			)
			return conversions
		} catch (error) {
			console.error('Affalliances fetch error:', error.message)
			return []
		}
	}

	/**
	 * Fetch Affalliances data for a single period
	 */
	async fetchAffalliancesPeriodData(url, startDate, endDate, periodNumber) {
		try {
			console.log(
				`Fetching Affalliances period ${periodNumber}: ${this.dateFormatter(
					startDate,
				)} to ${this.dateFormatter(endDate)}`,
			)

			// Check if API key is available
			if (!process.env.CALL_AFFALLIANCES_KEY) {
				console.warn('CALL_AFFALLIANCES_KEY not found in environment variables')
				return []
			}

			const agent = new Agent({ rejectUnauthorized: false })
			const response = await fetch(url, {
				method: 'GET',
				agent,
			})

			if (!response.ok) {
				const errorText = await response.text()
				console.error(
					`Affalliances API Error ${response.status} for period ${periodNumber}:`,
					errorText,
				)
				return []
			}

			const result = await response.json()
			console.log(
				`Affalliances period ${periodNumber} returned ${result.conversions?.length || 0
				} conversions`,
			)

			return (
				result.conversions
					?.filter(record => record.p1)
					.map(record => {
						let advertiserInfo = ''
						if (record.sub1) {
							advertiserInfo += record.sub1
						}
						if (record.sub2) {
							advertiserInfo += `, ${record.sub2}`
						}

						return {
							saleAmount: record.sale || 0,
							approvedPayout: record.payout,
							affiliateInfo1: record.p1,
							affiliateInfo2: '',
							adId: record.id,
							datetime: new Date(record.created)
								.toISOString()
								.slice(0, 19)
								.replace('T', ' '),
							partner: 'affalliances',
							advertiserInfo,
							currency: 'INR',
							clickDate: record.created,
							purchaseDate: record.created,
							notes: '',
							otherInfo: '',
						}
					}) || []
			)
		} catch (error) {
			console.error(
				`Affalliances period ${periodNumber} fetch error:`,
				error.message,
			)
			return []
		}
	}

	/**
	 * Fetch conversions from Admitad API with improved error handling
	 * Fetches data for the last 1 day only
	 */
	async fetchAdmitadConversions(affiliationService, daysBack = 1) {
		try {
			console.log(`Fetching Admitad conversions for the last ${daysBack} day`)

			const affiliationTokenData =
				await affiliationService.findAffiliationTokenByName('Admitad')
			if (!affiliationTokenData?.accessToken) {
				console.warn('Admitad access token not found')
				return []
			}

			const endDate = new Date()
			const startDate = new Date()
			startDate.setDate(startDate.getDate() - daysBack) // 1 day ago

			console.log(
				`Fetching Admitad data from ${this.dateFormatter(
					startDate,
				)} to ${this.dateFormatter(endDate)}`,
			)

			const conversions = await this.fetchAdmitadPeriodData(
				affiliationTokenData.accessToken,
				startDate,
				endDate,
				1,
			)

			console.log(
				`Admitad API returned ${conversions.length} conversions from last ${daysBack} day`,
			)
			return conversions
		} catch (error) {
			console.error('Admitad fetch error:', error.message)
			return []
		}
	}

	/**
	 * Fetch Admitad data for a single period
	 */
	async fetchAdmitadPeriodData(accessToken, startDate, endDate, periodNumber) {
		try {
			console.log(
				`Fetching Admitad period ${periodNumber}: ${this.dateFormatter(
					startDate,
				)} to ${this.dateFormatter(endDate)}`,
			)

			const url = `https://api.admitad.com/statistics/actions/?date_start=${this.dateFormatter(
				startDate,
			)}&date_end=${this.dateFormatter(endDate)}&limit=500`

			const headers = {
				Authorization: `Bearer ${accessToken}`,
				'Content-Type': 'application/json',
			}

			const agent = new Agent({ rejectUnauthorized: false })
			const response = await fetch(url, {
				method: 'GET',
				headers,
				agent,
			})

			if (!response.ok) {
				const errorText = await response.text()
				console.error(
					`Admitad API Error ${response.status} for period ${periodNumber}:`,
					errorText,
				)
				return []
			}

			const result = await response.json()
			console.log(
				`Admitad period ${periodNumber} returned ${result.results?.length || 0
				} actions`,
			)

			return (
				result.results
					?.filter(conversion => conversion.subid || conversion.subid1)
					.map(conversion => {
						let affiliateInfo1 = conversion.subid
						if (!affiliateInfo1) {
							affiliateInfo1 = conversion.subid1
						}

						return {
							saleAmount: conversion.cart || 0,
							approvedPayout: conversion.payment,
							affiliateInfo1,
							affiliateInfo2: conversion.subid2 || '',
							adId: conversion.action_id,
							datetime: new Date(conversion.action_date)
								.toISOString()
								.slice(0, 19)
								.replace('T', ' '),
							partner: 'Admitad',
							advertiserInfo: conversion.website || '',
							currency: conversion.currency || 'INR',
							clickDate: conversion.click_date,
							purchaseDate: conversion.action_date,
							notes: '',
							otherInfo: '',
						}
					}) || []
			)
		} catch (error) {
			console.error(
				`Admitad period ${periodNumber} fetch error:`,
				error.message,
			)
			return []
		}
	}

	/**
	 * Fetch all conversions from all partners with improved error handling
	 * Now fetches only the last 10 days from each partner
	 */
	async fetchAllConversions(affiliationService) {
		try {
			console.log(
				'Starting to fetch conversions from all partners (last 10 days)...',
			)

			const [
				// impactConversions,
				// affalliancesConversions,
				// admitadConversions,
				analyticsConversions,
			] =
				await Promise.allSettled([
					// this.fetchImpactConversions(10),
					// this.fetchAffalliancesConversions(10),
					// this.fetchAdmitadConversions(affiliationService, 10),
					this.fetchAnalyticsConversions(10),
				])

			// Extract successful results
			// const impact =
			// 	impactConversions.status === 'fulfilled' ? impactConversions.value : []
			// const affalliances =
			// 	affalliancesConversions.status === 'fulfilled'
			// 		? affalliancesConversions.value
			// 		: []
			// const admitad =
			// 	admitadConversions.status === 'fulfilled'
			// 		? admitadConversions.value
			// 		: []
			const analytics =
				analyticsConversions.status === 'fulfilled'
					? analyticsConversions.value
					: []

			// Log any rejected promises
			// if (impactConversions.status === 'rejected') {
			// 	console.error('Impact fetch failed:', impactConversions.reason)
			// }
			// if (affalliancesConversions.status === 'rejected') {
			// 	console.error(
			// 		'Affalliances fetch failed:',
			// 		affalliancesConversions.reason,
			// 	)
			// }
			// if (admitadConversions.status === 'rejected') {
			// 	console.error('Admitad fetch failed:', admitadConversions.reason)
			// }
			// if (analyticsConversions.status === 'rejected') {
			// 	console.error('Analytics fetch failed:', analyticsConversions.reason)
			// }
			const allConversions = [
				// ...impact,
				// ...affalliances,
				// ...admitad,
				...analytics,
			]

			// console.log(
			// 	`Fetched ${allConversions.length} conversions from last 1 day (Impact: ${impact.length}, Affalliances: ${affalliances.length}, Admitad: ${admitad.length} Analytics: ${analytics.length})`,
			// )

			return allConversions
		} catch (error) {
			console.error('Error fetching all conversions:', error.message)
			return []
		}
	}

	/**
	 * Utility delay function
	 */
	delay(ms) {
		return new Promise(resolve => setTimeout(resolve, ms))
	}
}
