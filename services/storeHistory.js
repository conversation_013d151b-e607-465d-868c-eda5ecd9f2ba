import mongoose from "mongoose";
import { HttpException } from "../exceptions/httpException.js";
import { StoreCategoryHistory } from "../models/stores/history.js";

export class StoreCategoryHistoryService {
  createStoreCategoryHistory = async (body) => {
    // Extract the uid field from body._doc
    const plainBody = body._doc ? body._doc : body;

    // Extract only the fields we need
    const { uid, _id, ...docWithoutUid } = plainBody;

    let storeCategoryHistory;
    try {
      storeCategoryHistory = await StoreCategoryHistory.create({
        ...docWithoutUid,
        storeCategoryUid: uid,
        storeCategoryId: _id,
      });
    } catch (e) {
      console.log(e, "error");
    }
    if (!storeCategoryHistory) {
      throw new HttpException(
        404,
        "failed to create new store category history"
      );
    }
    return storeCategoryHistory;
  };
  getAllCategoryHistory = async (query) => {
    const pageSize = Number(query.pageSize) || 5;
    const page = Number(query.page) || 1;
    const sort = query.sort ? query.sort : { _id: -1 };

    const keyword = query.search
      ? {
          $or: [
            {
              name: {
                $regex: query.search,
                $options: "i",
              },
            },
          ],
        }
      : {};
    if (query.admin) {
      keyword.createdBy = query.admin;
    }

    if (query.platform) {
      keyword.device = query.platform;
    }

    // Add filter for store if present
    if (query.store) {
      keyword.store = new mongoose.Types.ObjectId(query.store);
    }

    const count = await StoreCategoryHistory.countDocuments({ ...keyword });
    const allCategoriesHistory = await StoreCategoryHistory.find(
      { ...keyword },
      {
        updatedAt: 1,
        sectionLink: 1,
        givingType: 1,
        gettingType: 1,
        oldUserOfferAmount: 1,
        newUserOfferAmount: 1,
        oldUserOfferPercent: 1,
        newUserOfferPercent: 1,
        name: 1,
        notes: 1,
        device: 1,
        uid: 1,
        gettingNewUserRate: 1,
        gettingOldUserRate: 1,
      }
    )
      .populate("affiliation", "name")
      .populate("store", "name")
      .populate("createdBy", "name")
      .sort(sort)
      .limit(pageSize)
      .skip((page - 1) * pageSize);

    return {
      search: query.search,
      allCategoriesHistory,
      page,
      pageSize,
      pages: Math.ceil(count / pageSize),
    };
  };
}
