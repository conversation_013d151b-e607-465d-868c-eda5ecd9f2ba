import { HttpException } from '../exceptions/httpException.js'
import { AdminLog } from '../models/admin/adminLog.js'

export class AdminLogService {
	createLog = async body => {
		const log = await AdminLog.create(body)
		if (!log) {
			throw new HttpException(404, 'log not created!')
		}
		return log
	}

	createMultipleLogs = async logs => {
		await AdminLog.insertMany(logs)
		return true
	}

	getAllLogs = async query => {
		const pageSize = Number(query.pageSize) || 10
		const page = Number(query.page) || 1

		const keyword = query.search
			? {
					$or: [
						{
							log: {
								$regex: query.search,
								$options: 'i',
							},
						},
					],
			  }
			: {}

		if (query.admin) {
			keyword.admin = query.admin
		}

		const allLogs = await AdminLog.find({ ...keyword })
			.populate('admin', 'name role')
			.sort({ createdAt: -1 })
			.limit(pageSize)
			.skip((page - 1) * pageSize)

		const count = await AdminLog.countDocuments({ ...keyword })
		const pages = Math.ceil(count / 20)

		return {
			allLogs,
			page,
			pages,
			pageSize,
			search: query.search,
		}
	}
}
