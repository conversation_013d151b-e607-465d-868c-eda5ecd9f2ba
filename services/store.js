import { HttpException } from "../exceptions/httpException.js";
import { Store } from "../models/stores/store.js";
import { MeiliSearchService } from "../config/meilisearch.js";
import { StoreSchema } from "../models/meilisearch/store.js";
import { StoreCategory } from "../models/categories/storeCategory.js";
import mongoose from "mongoose";

export class StoreService {
  meilisearch = new MeiliSearchService();

  createStore = async (body) => {
    const store = await Store.create(body);

    if (!store) {
      throw new HttpException(404, "filed to create new store");
    }
    const newStore = await Store.findById(store._id)
      .populate("giftCard", "name")
      .populate("categories.category", "name")
      .populate("categories.subCategories", "name");

    const categories = [];
    const subcategories = [];
    newStore.categories.map((item) => {
      categories.push({ name: item?.category?.name });
      item?.subCategories?.map((subCat) => {
        if (!subcategories.includes({ name: subCat?.name })) {
          subcategories.push({ name: subCat?.name });
        }
      });
    });
    const storecategories = await StoreCategory.find(
      { store: store._id },
      { name: 1, _id: 0 }
    );
    const storeDocument = new StoreSchema({
      id: newStore._id,
      uid: newStore.uid,
      url: newStore.logo.secureUrl,
      active: newStore.active,
      name: newStore.name,
      description: newStore.description,
      detailedDescription: newStore.detailedDescription,
      offerWarning: newStore.offerWarning,
      giftCard: newStore?.giftCard?.name,
      categories,
      bgColor: newStore.bgColor,
      storecategories,
      subcategories,
    });

    console.log(storeDocument, "store document");
    const searchResponse = await this.meilisearch.addDocuments(storeDocument);
    console.log(searchResponse, "search resposne");
    return store;
  };

  getAllStores = async (query) => {
    const pageSize = Number(query.pageSize) || 10;
    const page = Number(query.page) || 1;
    const sort = { _id: -1 };

    const keyword = query.search
      ? {
          $or: [
            {
              name: {
                $regex: query.search,
                $options: "i",
              },
            },
          ],
        }
      : {};
    if (query.admin) {
      keyword.createdBy = query.admin;
    }
    if (query.store) {
      keyword._id = query.store;
    }

    if (query.affiliation && query.affiliation !== "") {
      keyword.affiliation = new mongoose.Types.ObjectId(query.affiliation);
    }

    if (query.campaignType && query.campaignType !== "") {
      keyword.campaignType = query.campaignType;
    }

    const storeType = Number(query.storeType);
    if (storeType === 1) {
      keyword.active = true;
    } else if (storeType === 2) {
      keyword.active = false;
    } else if (storeType === 3) {
      keyword.name = /instant/i;
      keyword.active = true;
    } else if (storeType === 4) {
      keyword.name = /instant/i;
      keyword.active = false;
    }

    if (query.sort === 3) {
      sort.priority = -1; // lowest priority
    } else if (query.sort === 4) {
      sort.priority = 1; // highest priority
    } else if (query.sort === 5) {
      sort.name = 1; // name
    } else if (query.sort === 6) {
      sort._id = -1; // last created
    }

    const pipeline = [
      { $match: keyword },
      {
        $lookup: {
          from: "offers",
          localField: "_id",
          foreignField: "store",
          as: "offers",
        },
      },
      {
        $lookup: {
          from: "affiliations", // Assuming 'affiliations' is the collection name for affiliations
          localField: "affiliation", // The field in the store collection that references affiliation
          foreignField: "_id",
          as: "affiliation", // The field to populate in the response
        },
      },
      {
        $addFields: {
          activeOffersCount: {
            $size: {
              $filter: {
                input: "$offers",
                as: "offer",
                cond: { $eq: ["$$offer.active", true] },
              },
            },
          },
        },
      },
    ];

    if (query.offer === "1") {
      pipeline.push({ $match: { activeOffersCount: 1 } });
    }
    if (query.offer === "0") {
      pipeline.push({ $match: { activeOffersCount: 0 } });
    }

    // Add stages to calculate counts before pagination
    pipeline.push({
      $facet: {
        stores: [
          { $sort: sort },
          { $skip: (page - 1) * pageSize },
          { $limit: pageSize },
        ],
        instantStores: [
          { $match: { name: /instant/i, active: true } },
          { $count: "count" },
        ],
        instantActiveStores: [
          { $match: { name: /instant/i, active: false } },
          { $count: "count" },
        ],
        activeStores: [{ $match: { active: true } }, { $count: "count" }],
        inactiveStores: [{ $match: { active: false } }, { $count: "count" }],
        allStoresCount: [{ $count: "count" }],
      },
    });

    const result = await Store.aggregate(pipeline);
    const stores = result[0].stores;
    const instantStores = result[0].instantStores[0]
      ? result[0].instantStores[0].count
      : 0;
    const instantActiveStores = result[0].instantActiveStores[0]
      ? result[0].instantActiveStores[0].count
      : 0;
    const activeStores = result[0].activeStores[0]
      ? result[0].activeStores[0].count
      : 0;
    const inactiveStores = result[0].inactiveStores[0]
      ? result[0].inactiveStores[0].count
      : 0;
    const allStoresCount = result[0].allStoresCount[0]
      ? result[0].allStoresCount[0].count
      : 0;

    const countPipeline = [
      { $match: keyword },
      {
        $lookup: {
          from: "offers",
          localField: "_id",
          foreignField: "store",
          as: "offers",
        },
      },
      {
        $addFields: {
          activeOffersCount: {
            $size: {
              $filter: {
                input: "$offers",
                as: "offer",
                cond: { $eq: ["$$offer.active", true] },
              },
            },
          },
        },
      },
    ];

    if (query.offer === "1") {
      countPipeline.push({ $match: { activeOffersCount: 1 } });
    }
    if (query.offer === "0") {
      countPipeline.push({ $match: { activeOffersCount: 0 } });
    }

    countPipeline.push({ $count: "total" });

    const countResult = await Store.aggregate(countPipeline);
    const count = countResult[0] ? countResult[0].total : 0;

    return {
      search: query.search,
      allStores: stores,
      page,
      pageSize,
      pages: Math.ceil(count / pageSize),
      instantActiveStores,
      activeStores,
      inactiveStores,
      instantStores,
      allStoresCount,
    };
  };
  getAllStoresList = async () => {
    const allStores = await Store.find(
      {
        $or: [{ isDeleted: { $exists: false } }, { isDeleted: false }],
      },
      "name active uid affiliation affiliateLink isInstant"
    ).sort({
      _id: -1,
    });
    return allStores;
  };

  insertNewOffer = async (storeId, offerId) => {
    await Store.updateOne({ _id: storeId }, { $push: { offerId: offerId } });
    return true;
  };

  getStoreDetails = async (storeId) => {
    const store = await Store.findById(storeId)
      .populate("affiliation", "name")
      .populate("relatedStores", "name")
      .populate("categories.category", "name")
      .populate("categories.subCategories", "name")
      .populate("createdBy", "name");

    if (!store) {
      throw new HttpException(404, "resource not found");
    }
    return store;
  };

  findStoreById = async (storeId) => {
    const store = await Store.findById(storeId)
      .populate("affiliation", "name")
      .populate("relatedStores", "name")
      .populate("categories.category", "name")
      .populate("categories.subCategories", "name")
      .populate("createdBy", "name");

    if (!store) {
      return null;
    }
    return store;
  };

  updateStore = async (storeId, body) => {
    const store = await Store.findById(storeId);
    if (!store) {
      throw new HttpException(404, "resource not found");
    }
    await Store.updateOne({ _id: storeId }, { $set: body });

    const newStore = await Store.findById(store._id)
      .populate("giftCard", "name")
      .populate("categories.category", "name")
      .populate("categories.subCategories", "name");

    const categories = [];
    const subcategories = [];
    newStore.categories.map((item) => {
      categories.push({ name: item?.category?.name });
      item?.subCategories?.map((subCat) => {
        if (!subcategories.includes({ name: subCat?.name })) {
          subcategories.push({ name: subCat?.name });
        }
      });
    });
    const storecategories = await StoreCategory.find(
      { store: store._id },
      { name: 1, _id: 0 }
    );
    const storeDocument = new StoreSchema({
      id: newStore._id,
      uid: newStore.uid,
      url: newStore.logo.secureUrl,
      active: newStore.active,
      name: newStore.name,
      description: newStore.description,
      detailedDescription: newStore.detailedDescription,
      offerWarning: newStore.offerWarning,
      giftCard: newStore?.giftCard?.name,
      categories,
      bgColor: newStore.bgColor,
      storecategories,
      subcategories,
    });
    await this.meilisearch.updateDocuments(storeDocument);
    return store;
  };

  updateStoreActiveStatus = async (storeId, adminId) => {
    const store = await Store.findById(storeId);
    if (!store) {
      throw new HttpException(404, "resource not found");
    }
    store.active = !store.active;
    store.updatedBy = adminId;
    await store.save();
    return store;
  };

  updateStoreDeleteStatus = async (storeId, adminId) => {
    const store = await Store.findById(storeId)
      .populate("categories.category", "name")
      .populate("categories.subCategories", "name");

    if (!store) {
      throw new HttpException(404, "resource not found");
    }
    const currentStatus = store?.isDeleted ? true : false;

    store.isDeleted = !store.isDeleted;
    store.updatedBy = adminId;
    await store.save();

    const categories = [];
    const subcategories = [];
    store.categories.map((item) => {
      categories.push({ name: item?.category?.name });
      item?.subCategories?.map((subCat) => {
        if (!subcategories.includes({ name: subCat?.name })) {
          subcategories.push({ name: subCat?.name });
        }
      });
    });
    const storecategories = await StoreCategory.find(
      { store: store._id },
      { name: 1, _id: 0 }
    );

    const storeDocument = new StoreSchema({
      id: store._id,
      uid: store.uid,
      url: store.logo.secureUrl,
      active: store.active,
      name: store.name,
      description: store.description,
      detailedDescription: store.detailedDescription,
      offerWarning: store.offerWarning,
      giftCard: store?.giftCard?.name,
      categories,
      bgColor: store.bgColor,
      storecategories,
      subcategories,
    });

    if (currentStatus) {
      await this.meilisearch.addDocuments(storeDocument);
    } else {
      await this.meilisearch.removeStore(store.id);
    }

    return store;
  };

  updateStorePriority = async (storeId, priority, adminId) => {
    const store = await Store.findById(storeId);
    if (!store) {
      throw new HttpException(404, "resource not found");
    }
    store.priority = priority;
    store.updatedBy = adminId;
    await store.save();
    return store;
  };

  updateAutoCheckStatus = async (storeId, adminId) => {
    const store = await Store.findById(storeId);
    if (!store) {
      throw new HttpException(404, "resource not found");
    }
    store.autoCheck = !store.autoCheck;
    store.updatedBy = adminId;
    await store.save();
    return store;
  };

  updateStoreDeepLinkStatus = async (storeId, adminId) => {
    const store = await Store.findById(storeId);
    if (!store) {
      throw new HttpException(404, "resource not found");
    }
    store.deepLinkEnable = !store.deepLinkEnable;
    store.updatedBy = adminId;
    await store.save();
    return store;
  };

  updateStatus = async (storeId, adminId) => {
    const store = await Store.findById(storeId);
    if (!store) {
      throw new HttpException(404, "resource not found");
    }
    store.active = !store.active;
    store.updatedBy = adminId;
    await store.save();
    return store;
  };

  updateIsSpecialLimit = async (storeId, isSpecial, adminId) => {
    const store = await Store.findById(storeId);
    if (!store) {
      throw new HttpException(404, "resource not found");
    }
    store.isSpecial = isSpecial;
    store.updatedBy = adminId;
    await store.save();
    return store;
  };

  updateStoreTrending = async (storeId, adminId, status) => {
    const store = await Store.findById(storeId);
    if (!store) {
      throw new HttpException(404, "resource not found");
    }
    store.trending = status;
    store.updatedBy = adminId;
    await store.save();
    return store;
  };
}
