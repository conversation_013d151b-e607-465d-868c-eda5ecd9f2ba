import { IcbGiftCard } from "../models/giftCards/icb-giftCard.js";

export class IcbGiftCardOrderService {
  getAllIcbGiftCardOrders = async (query) => {
    const pageSize = Number(query.pageSize) || 5;
    const page = Number(query.page) || 1;
    const sort = query.sort ? query.sort : { _id: -1 };

    const keyword = {};
    if (query.admin) {
      keyword.createdBy = query.admin;
    }
    const count = await IcbGiftCard.countDocuments({ ...keyword });
    const allIcbGiftCardOrders = await IcbGiftCard.find({ ...keyword })
      .populate("giftCard", "name active")
      .populate("buyer", "name active")
      .populate("updatedBy", "name")
      .sort(sort)
      .limit(pageSize)
      .skip((page - 1) * pageSize);

    return {
      search: query.search,
      allIcbGiftCardOrders,
      page,
      pageSize,
      pages: Math.ceil(count / pageSize),
    };
  };
}
