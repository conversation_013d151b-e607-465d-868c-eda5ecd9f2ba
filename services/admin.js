import bcrypt from 'bcryptjs'
import { HttpException } from '../exceptions/httpException.js'
import { Admin } from '../models/admin/admin.js'

export class AdminService {
    createAdmin = async body => {
        const admin = await Admin.create(body)
        if (!admin) {
            throw new HttpException(404, 'admin not created!')
        }
        return admin
    }

    getAdminDetailsWithId = async adminId => {
        const admin = await Admin.findById(adminId)
        if (!admin) {
            throw new HttpException(404, 'resource not found!')
        }
        return admin
    }

    updateAdminDetails = async (body, adminId) => {
        const admin = await Admin.findById(adminId)
        if (!admin) {
            throw new HttpException(404, 'resource not found!')
        }
        await Admin.updateOne({ _id: adminId }, { $set: body })
        return admin
    }

    updateAdminPassword = async (body, adminId) => {
        console.log("🚀 ~ AdminService ~ updateAdminPassword= ~ adminId:", adminId)
        console.log("🚀 ~ AdminService ~ updateAdminPassword= ~ body:", body)
        const admin = await Admin.findById(adminId)
        if (!admin) {
            throw new HttpException(404, 'resource not found!')
        }
        const password = await bcrypt.hash(body.password, 10)
        await Admin.updateOne({ _id: adminId }, { $set: { password } })
        return admin
    }

    resetAdminPassword = async body => {
        const admin = await Admin.findOne({ email: body.email }).select('+password')
        if (!admin) {
            throw new HttpException(404, 'resource not found!')
        }

        const password = await bcrypt.hash(body.newPassword, 10)
        await Admin.updateOne(
            { _id: admin._id },
            {
                $set: { password },
            },
        )
        return admin
    }

    updateAdminRole = async (adminId, roleString) => {
        let role = `${roleString}`
        role = role.toLocaleLowerCase()
        const admin = await Admin.findById(adminId)
        if (!admin) {
            throw new HttpException(404, 'resource not found!')
        }
        await Admin.updateOne({ _id: adminId }, { $set: { role } })
        return admin
    }

    updateAdminActiveStatus = async adminId => {
        const admin = await Admin.findById(adminId)
        if (!admin) {
            throw new HttpException(404, 'resource not found!')
        }
        admin.block = !admin.block
        await admin.save()
        return admin
    }

    getAllAdminList = async () => {
        const allAdmins = await Admin.find().sort({ _id: -1 })
        return allAdmins
    }

    deleteAdmin = async adminId => {
        const admin = await Admin.findById(adminId)
        if (!admin) {
            throw new HttpException(404, 'resource not found!')
        }
        await Admin.deleteOne({ _id: adminId })
        return true
    }

    getAllAdmins = async query => {
        const pageSize = Number(query.pageSize) || 10
        const page = Number(query.page) || 1

        const keyword = query.search
            ? {
                $or: [
                    {
                        name: {
                            $regex: query.search,
                            $options: 'i',
                        },
                    },
                ],
            }
            : {}

        const count = await Admin.countDocuments({ ...keyword })
        const allAdmins = await Admin.find({ ...keyword })
            .sort({ createdAt: -1 })
            .limit(pageSize)
            .skip((page - 1) * pageSize)

        return {
            allAdmins,
            page,
            pages: Math.ceil(count / pageSize),
            pageSize,
            search: query.search,
        }
    }

    getAdminDetailsWithEmail = async email => {
        const admin = await Admin.findOne({ email: email })
        if (!admin) {
            throw new HttpException(404, 'resource not found!')
        }
        return admin
    }
}
