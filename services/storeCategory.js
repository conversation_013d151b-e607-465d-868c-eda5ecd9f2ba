import { date } from "yup";
import { HttpException } from "../exceptions/httpException.js";
import { StoreCategory } from "../models/categories/storeCategory.js";

export class StoreCategoryService {
  createStoreCategory = async (body) => {
    try {
      // Create a new instance of the StoreCategory model
      const storeCategory = new StoreCategory(body);

      // Save the document to the database
      const savedCategory = await storeCategory.save();

      // Return the saved document
      return savedCategory;
    } catch (error) {
      // Handle errors such as validation or other exceptions
      throw new HttpException(400, "Failed to create new store category");
    }
  };

  getAllStoreCategoryList = async () => {
    const allStoreCategories = await StoreCategory.find(
      { active: true },
      "name affiliation store active uid oldUserOfferAmount newUserOfferAmount oldUserOfferPercent newUserOfferPercent"
    ).sort({
      _id: -1,
    });
    return allStoreCategories;
  };
  getStoreCategoryDetails = async (id) => {
    const storeCategory = await StoreCategory.findById(id)
      .populate("affiliation", "name")
      .populate("store", "name");
    if (!storeCategory) {
      throw new HttpException(404, "resource not found!");
    }
    return storeCategory;
  };

  getAllStoreCategories = async (query) => {
    const pageSize = Number(query.pageSize) || 5;
    const page = Number(query.page) || 1;
    const sort = query.name ? {} : { _id: -1 };

    const keyword = query.search
      ? {
          $or: [
            {
              name: {
                $regex: query.search,
                $options: "i",
              },
            },
          ],
        }
      : {};
    if (query.admin) {
      keyword.createdBy = query.admin;
    }
    if (query.store) {
      keyword.store = query.store;
    }
    if (query.app) {
      keyword.device = query.app;
    }
    if (query.name) {
      if (query.name == 1) {
        sort.name = -1;
      } else {
        sort.name = 1;
      }
    }

    keyword.active = true;
    const count = await StoreCategory.countDocuments({ ...keyword });
    const allStoreCategories = await StoreCategory.find({ ...keyword })
      .populate("affiliation", "name")
      .populate("store", "name")
      .populate("createdBy", "name")
      .populate("updatedBy", "name")
      .sort({ ...sort })
      .limit(pageSize)
      .skip((page - 1) * pageSize);

    return {
      search: query.search,
      allStoreCategories,
      page,
      pageSize,
      pages: Math.ceil(count / pageSize),
    };
  };
  updateStoreCategory = async (categoryId, body) => {
    const storeCategory = await StoreCategory.findById(categoryId);
    if (!storeCategory) {
      throw new HttpException(404, "resource not found");
    }
    await StoreCategory.updateOne({ _id: categoryId }, { $set: body });
    return storeCategory;
  };

  updateStoreCategoryActiveStatus = async (categoryId, adminId) => {
    const storeCategory = await StoreCategory.findById(categoryId);
    if (!storeCategory) {
      throw new HttpException(404, "resource not found");
    }
    storeCategory.active = !storeCategory.active;
    storeCategory.updatedBy = adminId;
    const data = await storeCategory.save();

    return data;
  };
  permanentDeleteStoreCategory = async (categoryId) => {
    const storeCategory = await StoreCategory.findById(categoryId);
    if (!storeCategory) {
      throw new HttpException(404, "resource not found");
    }
    await StoreCategory.deleteOne({ _id: categoryId });
    return true;
  };
}
