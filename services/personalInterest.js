import { HttpException } from "../exceptions/httpException.js";
import { PersonalInterest } from "../models/personalInterest.js";

export class PersonalInterestService {
  createPersonalInterest = async (data, adminId) => {
    const payload = {
      ...data,
      createdBy: adminId,
    };
    const personalInterest = await PersonalInterest.create(payload);
    if (!personalInterest) {
      throw new HttpException(404, "filed to create new personal interest");
    }
    return personalInterest;
  };

  updatePersonalInterest = async (id, data, adminId) => {
    const payload = {
      ...data,
      updatedBy: adminId,
    };

    const personalInterest = await PersonalInterest.findById(id);
    if (!personalInterest) {
      throw new HttpException(404, "resource not found");
    }
    await PersonalInterest.findOneAndUpdate({ _id: id }, { $set: payload });
    return personalInterest;
  };

  getAllPersonalInterests = async (query) => {
    const pageSize = Number(query.pageSize) || 5;
    const page = Number(query.page) || 1;
    const sort = { _id: -1 };

    const keyword = {};
    if (query.admin) {
      keyword.createdBy = query.admin;
    }
    const count = await PersonalInterest.countDocuments({ ...keyword });
    const allPersonalInterests = await PersonalInterest.find({ ...keyword })
      .populate("createdBy", "name")
      .sort(sort)
      .limit(pageSize)
      .skip((page - 1) * pageSize);

    return {
      search: query.search,
      allPersonalInterests,
      page,
      pageSize,
      pages: Math.ceil(count / pageSize),
    };
  };
}
