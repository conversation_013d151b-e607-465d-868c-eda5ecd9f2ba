import { Agent } from 'http';
import { AffiliationService } from './affiliation.js';
import { AdminService } from './admin.js';



const affiliationService = new AffiliationService()
const adminService = new AdminService()


export const admitKeyChange = async () => {
    console.log("-------Admit key change function called-------");
    const url = 'https://api.admitad.com/token/';
    const body = `grant_type=client_credentials&client_id=${process.env.ADMITAD_CLIENT_ID}&scope=private_data%20private_data_phone%20private_data_email%20%20websites%20statistics%20banners%20public_data%20advcampaigns%20banners_for_website%20coupons%20statistics%20advcampaigns_for_website%20lost_orders%20manage_broker_application%20broker_application%20deeplink_generator%20coupons_for_website`;

    const headers = new Headers({
        'Authorization': `Basic ${process.env.ADMITAD_BASE_64_AUTH}`,
        'Content-Type': 'application/x-www-form-urlencoded',
        'Cookie': 'gdpr_country=0; path_language=en; user_default_language=ae;',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/68.0.3440.106 Safari/537.36',
        'Accept-Language': 'en-US,en;q=0.5'
    });

    try {

        const agent = new Agent({ rejectUnauthorized: false });
        const response = await fetch(url, {
            method: 'POST',
            headers: headers,
            body: body,
            agent: agent
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();

        console.log(result.access_token);

        // Simulating the update_admit_token function
        await updateAdmitToken(result.access_token);
    } catch (error) {
        console.error('Error fetching data:', error);
    }
}

// Helper function to  update_admit_token 
async function updateAdmitToken(accessToken) {

    const admin = await adminService.getAdminDetailsWithEmail("<EMAIL>")

    const affiliationTokenData = {
        name: 'Admitad',
        accessToken: accessToken,
        createdBy: admin._id,
        updatedBy: admin._id
    };


    const affiliationToken = await affiliationService.upsertAffiliationToken(affiliationTokenData)



    // Implement the logic for update_admit_token here
    console.log('Access token updated:', affiliationToken);
}

