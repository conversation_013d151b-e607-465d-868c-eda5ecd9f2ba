import { ClickService } from '../services/cashbackClick.js'
import { StoreService } from '../services/store.js'
import { EarningService } from '../services/earning.js'
// biome-ignore lint/style/useNodejsImportProtocol: <explanation>
import { Agent } from 'https'
import { AdminService } from './admin.js'
import { AdminLogService } from './adminLog.js'
import { AffiliationService } from './affiliation.js'
import { UserService } from './user.js'
import { sendSms } from '../utils/sendSms.js'

const clickService = new ClickService()
const storeService = new StoreService()
const earningServie = new EarningService()
const adminService = new AdminService()
const adminLogService = new AdminLogService()
const affiliationService = new AffiliationService()
const userService = new UserService()

// export const callClickoTrackier = async () => {
//   const endDate = new Date();
//   endDate.setDate(endDate.getDate() + 1);
//   const startDate = new Date();
//   startDate.setDate(startDate.getDate() - 20);

//   const formattedEndDate = endDate.toISOString().split("T")[0];
//   const formattedStartDate = startDate.toISOString().split("T")[0];

//   const urlGenerated = `https://api.trackier.com/v2/publishers/conversions?apikey=${process.env.CALL_CLICK_TRACKIER_KEY}&start=${formattedStartDate}&end=${formattedEndDate}&page=1`;

//   try {
//     const response = await fetch(urlGenerated, {
//       method: "GET",
//       headers: {
//         Accept: "application/json",
//       },
//     });

//     if (!response.ok) {
//       throw new Error(`HTTP error! status: ${response.status}`);
//     }

//     const result = await response.json();

//     for (const record of result.conversions) {
//       if (record.p1 && record.p1.trim() !== "") {
//         const adId = record.id;
//         const saleAmount = record.sale || 0;
//         const datetime = new Date(record.created)
//           .toISOString()
//           .replace("T", " ")
//           .split(".")[0];
//         const approvedPayout = record.payout;
//         const affiliateInfo1 = record.p1;
//         const affiliateInfo2 = "";
//         let advertiserInfo = "";

//         if (record.sub1) advertiserInfo += record.sub1;
//         if (record.sub2) advertiserInfo += `, ${record.sub2}`;

//         await checkIfTracked(
//           saleAmount,
//           approvedPayout,
//           affiliateInfo1,
//           affiliateInfo2,
//           adId,
//           datetime,
//           "",
//           "clickonik",
//           advertiserInfo
//         );
//       }
//     }
//   } catch (error) {
//     console.error("Error:", error);
//   }
// };

// export const callRovers = async () => {
//   const endDate = new Date();
//   endDate.setDate(endDate.getDate() + 1);
//   const startDate = new Date();
//   startDate.setDate(startDate.getDate() - 20);

//   const formatDate = (date) => {
//     const d = new Date(date);
//     const month = "" + (d.getMonth() + 1);
//     const day = "" + d.getDate();
//     const year = d.getFullYear();

//     return [year, month.padStart(2, "0"), day.padStart(2, "0")].join("-");
//   };

//   const formattedEndDate = formatDate(endDate);
//   const formattedStartDate = formatDate(startDate);

//   const urlGenerated = `https://api.trackier.com/v2/publishers/conversions?apikey=${process.env.CALL_ROVERS_KEY}&start=${formattedStartDate}&end=${formattedEndDate}&page=1`;

//   try {
//     const agent = new Agent({ rejectUnauthorized: false });
//     const response = await fetch(urlGenerated, {
//       method: "GET",
//       agent: agent,
//     });

//     if (!response.ok) {
//       throw new Error(`HTTP error! status: ${response.status}`);
//     }

//     const result = await response.json();
//     for (const record of result.conversions) {
//       if (!record.p1) {
//         continue;
//       }

//       const adId = record.id;
//       let saleAmount = record.sale;
//       const datetime = new Date(record.created)
//         .toISOString()
//         .slice(0, 19)
//         .replace("T", " ");
//       const approvedPayout = record.payout;
//       const affiliateInfo1 = record.p1;
//       const affiliateInfo2 = "";

//       let advertiserInfo = "";
//       if (record.sub1) advertiserInfo += record.sub1;
//       if (record.sub2) advertiserInfo += `, ${record.sub2}`;

//       if (!saleAmount) saleAmount = 0;

//       await checkIfTracked(
//         saleAmount,
//         approvedPayout,
//         affiliateInfo1,
//         affiliateInfo2,
//         adId,
//         datetime,
//         "",
//         "rovers",
//         advertiserInfo
//       );
//     }
//   } catch (error) {
//     console.error("Error fetching data:", error);
//   }
// };

// export const callShooglooTrackier = async () => {
//   const endDate = new Date();
//   endDate.setDate(endDate.getDate() + 1);
//   const startDate = new Date();
//   startDate.setDate(startDate.getDate() - 20);

//   const formatDate = (date) => {
//     const d = new Date(date);
//     const month = "" + (d.getMonth() + 1);
//     const day = "" + d.getDate();
//     const year = d.getFullYear();

//     return [year, month.padStart(2, "0"), day.padStart(2, "0")].join("-");
//   };

//   const formattedEndDate = formatDate(endDate);
//   const formattedStartDate = formatDate(startDate);

//   const urlGenerated = `https://api.trackier.com/v2/publishers/conversions?apikey=${process.env.CALL_SHOOGLOO_KEY}&start=${formattedStartDate}&end=${formattedEndDate}&page=1`;

//   try {
//     const agent = new Agent({ rejectUnauthorized: false });
//     const response = await fetch(urlGenerated, {
//       method: "GET",
//       agent: agent,
//     });

//     if (!response.ok) {
//       throw new Error(`HTTP error! status: ${response.status}`);
//     }

//     const result = await response.json();
//     for (const record of result.conversions) {
//       if (!record.p1) {
//         continue;
//       }

//       const adId = record.id;
//       let saleAmount = record.sale;
//       const datetime = new Date(record.created)
//         .toISOString()
//         .slice(0, 19)
//         .replace("T", " ");
//       const approvedPayout = record.payout;
//       const affiliateInfo1 = record.p1;
//       const affiliateInfo2 = "";

//       let advertiserInfo = "";
//       if (record.sub1) advertiserInfo += record.sub1;
//       if (record.sub2) advertiserInfo += `, ${record.sub2}`;

//       if (!saleAmount) saleAmount = 0;

//       await checkIfTracked(
//         saleAmount,
//         approvedPayout,
//         affiliateInfo1,
//         affiliateInfo2,
//         adId,
//         datetime,
//         "",
//         "shoogloo",
//         advertiserInfo
//       );
//     }
//   } catch (error) {
//     console.error("Error fetching data:", error);
//   }
// };

// export const callVcTrackier = async () => {
//   const endDate = new Date();
//   endDate.setDate(endDate.getDate() + 1);
//   const startDate = new Date();
//   startDate.setDate(startDate.getDate() - 20);

//   const formatDate = (date) => {
//     const d = new Date(date);
//     const month = "" + (d.getMonth() + 1);
//     const day = "" + d.getDate();
//     const year = d.getFullYear();

//     return [year, month.padStart(2, "0"), day.padStart(2, "0")].join("-");
//   };

//   const formattedEndDate = formatDate(endDate);
//   const formattedStartDate = formatDate(startDate);

//   const urlGenerated = `https://api.trackier.com/v2/publishers/conversions?apikey=${process.env.CALL_VC_KEY}&start=${formattedStartDate}&end=${formattedEndDate}&page=1`;

//   try {
//     const agent = new Agent({ rejectUnauthorized: false });
//     const response = await fetch(urlGenerated, {
//       method: "GET",
//       agent: agent,
//     });

//     if (!response.ok) {
//       throw new Error(`HTTP error! status: ${response.status}`);
//     }

//     const result = await response.json();
//     for (const record of result.conversions) {
//       if (!record.p1) {
//         continue;
//       }

//       const adId = record.id;
//       let saleAmount = record.sale;
//       const datetime = new Date(record.created)
//         .toISOString()
//         .slice(0, 19)
//         .replace("T", " ");
//       const approvedPayout = record.payout;
//       const affiliateInfo1 = record.p1;
//       const affiliateInfo2 = "";

//       let advertiserInfo = "";
//       if (record.sub1) advertiserInfo += record.sub1;
//       if (record.sub2) advertiserInfo += `, ${record.sub2}`;

//       if (!saleAmount) saleAmount = 0;

//       await checkIfTracked(
//         saleAmount,
//         approvedPayout,
//         affiliateInfo1,
//         affiliateInfo2,
//         adId,
//         datetime,
//         "",
//         "vc_trackier",
//         advertiserInfo
//       );
//     }
//   } catch (error) {
//     console.error("Error fetching data:", error);
//   }
// };

export const callImpact = async () => {
	const endDate = new Date()
	endDate.setDate(endDate.getDate() + 1)
	const startDate = new Date()
	startDate.setDate(startDate.getDate() - 20)

	const formatDate = date => {
		const d = new Date(date)
		const month = `${d.getMonth() + 1}`
		const day = `${d.getDate()}`
		const year = d.getFullYear()

		return [year, month.padStart(2, '0'), day.padStart(2, '0')].join('-')
	}

	const formattedEndDate = formatDate(endDate)
	const formattedStartDate = formatDate(startDate)

	const url = `https://api.impact.com/Mediapartners/IRT7X4JxBMoe3498865QR2fcBPxdtvJQD1/Actions?ActionDateStart=${formattedStartDate}T00:00:00Z&ActionDateEnd=${formattedEndDate}T00:00:00Z`

	const headers = new Headers({
		Accept: 'application/json',
		'Content-Type': 'application/x-www-form-urlencoded',
		Cookie:
			'epersist=268449290.40515.0000; session=X1qieBAfKvTUklA3xvU8DQ|1660042687|fDQ_Rdxkj09cCTCn2ZV32fJRjMNKfos5uGodjBHHM1ukrZlwiWg-X3Uxtlbu2TEZqCTEzu0Xha-dN3rlMnDTh9dZuUjr8_LdYXCp3VXKHMU|T7iYzjTNqdvjNv3aWsYir6jriR0',
		Authorization: `Basic ${process.env.CALL_IMPACT_KEY}`,
		u: 'IRT7X4JxBMoe3498865QR2fcBPxdtvJQD1:ana_CPWCYd4XytFeNTz-ERZJ4zTjcntv',
	})

	try {
		const agent = new Agent({ rejectUnauthorized: false })
		const response = await fetch(url, {
			method: 'GET',
			headers: headers,
			// @ts-ignore
			agent: agent,
		})

		if (!response.ok) {
			throw new Error(`HTTP error! status: ${response.status}`)
		}

		const result = await response.json()

		for (const conversion of result.Actions) {
			if (!conversion.SubId1 || conversion.SubId1 === 1000) {
				continue
			}

			const saleAmount = conversion.Amount
			const datetime = new Date(conversion.EventDate)
				.toISOString()
				.slice(0, 19)
				.replace('T', ' ')
			const approvedPayout = conversion.Payout
			const affiliateInfo1 = conversion.SubId1
			const affiliateInfo2 = conversion.SubId1
			const adId = conversion.Id

			const notes = ''
			const advertiserInfo = conversion.AdId

			await checkIfTracked({
				saleAmount,
				approvedPayout,
				affiliateInfo1,
				affiliateInfo2,
				adId,
				datetime,
				notes,
				partner: 'Impact',
				advertiserInfo,
				currency: conversion.Currency,
			})
		}
	} catch (error) {
		console.error('Error fetching data:', error)
	}
}

// export const callClickoAffise = async () => {
//   const endDate = new Date();
//   endDate.setDate(endDate.getDate() + 1);
//   const startDate = new Date();
//   startDate.setDate(startDate.getDate() - 10);

//   const formatDate = (date) => {
//     const d = new Date(date);
//     const month = `${d.getMonth() + 1}`;
//     const day = `${d.getDate()}`;
//     const year = d.getFullYear();

//     return [year, month.padStart(2, "0"), day.padStart(2, "0")].join("-");
//   };

//   const formattedEndDate = formatDate(endDate);
//   const formattedStartDate = formatDate(startDate);

//   const urlGenerated = `https://api.trackier.com/affiliate/conversions?start=${formattedStartDate}&end=${formattedEndDate}&fields[]=sale&fields[]=p1&fields[]=txnId&fields[]=payout&fields[]=_id&fields[]=created`;
//   const headers = new Headers({
//     Host: "api.trackier.com",
//     "X-Api-Key": process.env.CALL_CLICK_AFFISE_KEY,
//     "Cache-Control": "no-cache",
//   });

//   try {
//     const agent = new Agent({ rejectUnauthorized: false });
//     const response = await fetch(urlGenerated, {
//       method: "GET",
//       headers: headers,
//       agent: agent,
//     });

//     if (!response.ok) {
//       throw new Error(`HTTP error! status: ${response.status}`);
//     }

//     const result = await response.json();

//     for (const record of result.records) {
//       if (!record.p1) {
//         continue;
//       }

//       const adId = record._id;
//       let saleAmount = record.sale;
//       const datetime = new Date(record.created.date)
//         .toISOString()
//         .slice(0, 19)
//         .replace("T", " ");
//       const approvedPayout = record.payout;
//       const affiliateInfo1 = record.p1;
//       const affiliateInfo2 = "";
//       const advertiserInfo = record.txnId;

//       if (!saleAmount) saleAmount = 0;

//       await checkIfTracked(
//         saleAmount,
//         approvedPayout,
//         affiliateInfo1,
//         affiliateInfo2,
//         adId,
//         datetime,
//         "",
//         "clickonik",
//         advertiserInfo
//       );
//     }
//   } catch (error) {
//     console.error("Error fetching data:", error);
//   }
// };

// export const callAffle = async () => {
//   const endDate = new Date();
//   endDate.setDate(endDate.getDate() + 1);
//   const startDate = new Date();
//   startDate.setDate(startDate.getDate() - 20);

//   const formatDate = (date) => {
//     const d = new Date(date);
//     const month = "" + (d.getMonth() + 1);
//     const day = "" + d.getDate();
//     const year = d.getFullYear();

//     return [year, month.padStart(2, "0"), day.padStart(2, "0")].join("-");
//   };

//   const formattedEndDate = formatDate(endDate);
//   const formattedStartDate = formatDate(startDate);

//   const urlGenerated = `https://api.trackier.com/v2/publishers/conversions?apikey=${process.env.CALL_AFFLE_KEY}&start=${formattedStartDate}&end=${formattedEndDate}&page=1`;

//   try {
//     const agent = new Agent({ rejectUnauthorized: false });
//     const response = await fetch(urlGenerated, {
//       method: "GET",
//       agent: agent,
//     });

//     if (!response.ok) {
//       throw new Error(`HTTP error! status: ${response.status}`);
//     }

//     const result = await response.json();
//     for (const record of result.conversions) {
//       if (!record.p1) {
//         continue;
//       }

//       const adId = record.id;
//       let saleAmount = record.sale;
//       const datetime = new Date(record.created)
//         .toISOString()
//         .slice(0, 19)
//         .replace("T", " ");
//       const approvedPayout = record.payout;
//       const affiliateInfo1 = record.p1;
//       const affiliateInfo2 = "";

//       let advertiserInfo = "";
//       if (record.sub1) advertiserInfo += record.sub1;
//       if (record.sub2) advertiserInfo += `, ${record.sub2}`;

//       if (!saleAmount) saleAmount = 0;

//       await checkIfTracked(
//         saleAmount,
//         approvedPayout,
//         affiliateInfo1,
//         affiliateInfo2,
//         adId,
//         datetime,
//         "",
//         "affle",
//         advertiserInfo
//       );
//     }
//   } catch (error) {
//     console.error("Error fetching data:", error);
//   }
// };
//
//

export const callAffalliances = async () => {
	const endDate = new Date()
	endDate.setDate(endDate.getDate() + 1)
	const startDate = new Date()
	startDate.setDate(startDate.getDate() - 20)

	const formatDate = date => {
		const d = new Date(date)
		const month = `${d.getMonth() + 1}`
		const day = `${d.getDate()}`
		const year = d.getFullYear()

		return [year, month.padStart(2, '0'), day.padStart(2, '0')].join('-')
	}

	const formattedEndDate = formatDate(endDate)
	const formattedStartDate = formatDate(startDate)

	const urlGenerated = `https://api.trackier.com/v2/publishers/conversions?apikey=${process.env.CALL_AFFALIANCES_KEY}&start=${formattedStartDate}&end=${formattedEndDate}&page=1`

	try {
		const agent = new Agent({ rejectUnauthorized: false })
		const response = await fetch(urlGenerated, {
			method: 'GET',
			// @ts-ignore
			agent: agent,
		})

		if (!response.ok) {
			throw new Error(`HTTP error! status: ${response.status}`)
		}

		const result = await response.json()
		for (const record of result.conversions) {
			if (!record.p1) {
				continue
			}

			const adId = record.id
			let saleAmount = record.sale
			const datetime = new Date(record.created)
				.toISOString()
				.slice(0, 19)
				.replace('T', ' ')
			const approvedPayout = record.payout
			const affiliateInfo1 = record.p1
			const affiliateInfo2 = ''

			let advertiserInfo = ''
			if (record.sub1) advertiserInfo += record.sub1
			if (record.sub2) advertiserInfo += `, ${record.sub2}`

			if (!saleAmount) saleAmount = 0

			//await checkIfTracked(
			//  {
			//    saleAmount,
			//    approvedPayout,
			//    affiliateInfo1,
			//    affiliateInfo2,
			//    adId,
			//    datetime,
			//    "",
			//    "affle",
			//    advertiserInfo
			//  }
			//);

			checkIfTracked({
				saleAmount,
				approvedPayout,
				affiliateInfo1,
				affiliateInfo2,
				adId,
				datetime,
				notes: '',
				partner: 'affalliances',
				advertiserInfo,
				otherInfo: '',
				clickDate: record.created,
				purchaseDate: record.created,
				currency: 'INR',
			})
		}
	} catch (error) {
		console.error('Error fetching data:', error)
	}
}

export const callAdmit = async () => {
	const endDate = new Date()
	const startDate = new Date()
	startDate.setDate(endDate.getDate() - 10)

	const formatDate = date => {
		const d = new Date(date)
		const month = `${d.getMonth() + 1}`
		const day = `${d.getDate()}`
		const year = d.getFullYear()

		return [day.padStart(2, '0'), month.padStart(2, '0'), year].join('.')
	}

	if (new Date().getHours() < 3) {
		endDate.setHours(endDate.getHours() - 1)
	}

	const formattedEndDate = formatDate(endDate)
	const formattedStartDate = formatDate(startDate)

	// Simulating the get_admit_token_val function
	const tokenVal = await getAdmitTokenVal()

	const urlGenerated = `https://api.admitad.com/statistics/actions/?date_start=${formattedStartDate}&date_end=${formattedEndDate}&limit=500&order_by=date`

	const headers = new Headers({
		Authorization: `Bearer ${tokenVal}`,
		'Content-Type': 'application/x-www-form-urlencoded',
		Cookie: 'gdpr_country=0; path_language=en; user_default_language=ae;',
		'User-Agent':
			'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/68.0.3440.106 Safari/537.36',
		'Accept-Language': 'en-US,en;q=0.5',
	})

	try {
		const agent = new Agent({ rejectUnauthorized: false })
		const response = await fetch(urlGenerated, {
			method: 'GET',
			headers: headers,
			// @ts-ignore
			agent: agent,
		})

		if (!response.ok) {
			throw new Error(`HTTP error! status: ${response.status}`)
		}

		const result = await response.json()

		for (const conversion of result.results) {
			if (!conversion.subid1 && !conversion.subid) {
				continue
			}

			let saleAmount = conversion.cart

			/**
			 * @type {any}
			 */
			let datetime = new Date(conversion.action_date)
				.toISOString()
				.slice(0, 19)
				.replace('T', ' ')
			const approvedPayout = conversion.payment
			let affiliateInfo1 = conversion.subid
			const affiliateInfo2 = conversion.subid2
			const actionId = conversion.action_id
			const adId = conversion.order_id || conversion.id
			const advertiserInfo = adId

			if (!saleAmount) {
				saleAmount = 0
			}
			if (!affiliateInfo1) {
				affiliateInfo1 = conversion.subid1
			}

			// check if it's tracked
			const admitTracked = (await earningServie.getEarningByOrderId(actionId))
				? true
				: false
			if (admitTracked) {
				continue
			}

			// if (conversion.currency === "USD") {
			//   approvedPayout *= 71.5;
			//   saleAmount *= 71.5;
			// }

			datetime = new Date(datetime)
			datetime.setHours(datetime.getHours() + 2)
			datetime.setMinutes(datetime.getMinutes() + 30)
			datetime = datetime.toISOString().slice(0, 19).replace('T', ' ')

			checkIfTracked({
				saleAmount,
				approvedPayout,
				affiliateInfo1,
				affiliateInfo2,
				adId,
				datetime,
				notes: '',
				partner: 'admit',
				advertiserInfo,
				otherInfo: '',
				clickDate: conversion.click_date,
				purchaseDate: conversion.action_date,
				currency: conversion.currency,
			})
		}
	} catch (error) {
		console.error('Error fetching data:', error)
	}
}

// Helper function to get_admit_token_val
async function getAdmitTokenVal() {
	const affilaitonTokenData =
		await affiliationService.findAffiliationTokenByName('Admitad')
	return affilaitonTokenData.accessToken
}

/**
 * Checks if a sale has been tracked.
 *
 * @async
 * @function checkIfTracked
 * @param {Object} params - The parameters for the function.
 * @param {number} params.saleAmount - The amount of the sale.
 * @param {number} params.approvedPayout - The approved payout for the sale.
 * @param {string} params.affiliateInfo1 - Information about the first affiliate.
 * @param {string} params.affiliateInfo2 - Information about the second affiliate.
 * @param {string} params.adId - The ID of the advertisement.
 * @param {string} params.datetime - The date and time of the sale.
 * @param {string} [params.notes=""] - Optional notes about the sale.
 * @param {string} [params.partner=""] - The partner associated with the sale.
 * @param {string} [params.advertiserInfo=""] - Information about the advertiser.
 * @param {string} [params.otherInfo=""] - Additional information.
 * @param {Date} [params.clickDate] - The date the ad was clicked.
 * @param {Date} [params.purchaseDate] - The date the purchase was made (optional).
 * @param {string} [params.currency=""] - The currency of the sale.
 * @returns {Promise<void>} - A promise that resolves when the check is complete.
 */
async function checkIfTracked({
	saleAmount,
	approvedPayout,
	affiliateInfo1,
	affiliateInfo2,
	adId,
	datetime,
	notes = '',
	partner = '',
	advertiserInfo = '',
	otherInfo = '',
	clickDate,
	purchaseDate,
	currency = '',
}) {
	const clickId = getClickId(affiliateInfo1, affiliateInfo2)

	if (!clickId) {
		console.log('no click id returning...')
		return
	}

	const clickData = await clickService.findClickByRefId(clickId)

	if (!clickData) {
		console.log(`Couldn't find a click with this Id ${clickId}`)
		return
	}

	const confirmDate = getConfirmDate(datetime)
	const clickDataItem = clickData
	const storeId = clickDataItem?.store
	// console.log(storeId, "store id")

	const storeData = await storeService.findStoreById(storeId)

	if (!storeData.autoCheck) {
		console.log('store data auto check disabled, returning...')
		return
	}

	const partnerId = clickDataItem.affiliation

	const orderTracked = (await earningServie.getEarningByOrderIdAndStore(
		adId,
		storeId,
	))
		? true
		: false

	await handleApproval(clickDataItem, orderTracked, partner, advertiserInfo, {
		saleAmount,
		approvedPayout,
		adId,
		datetime,
		confirmDate,
		partnerId,
		notes,
		otherInfo,
		clickDate,
		purchaseDate,
		currency,
	})
}

function getClickId(affiliateInfo1, affiliateInfo2) {
	const clickId = affiliateInfo1 ? affiliateInfo1 : affiliateInfo2
	return clickId
}

function getConfirmDate(datetime) {
	const confirmDate = new Date(datetime)
	confirmDate.setMonth(confirmDate.getMonth() + 3)
	return confirmDate
}

async function handleApproval(
	clickDataItem,
	orderTracked,
	partner,
	advertiserInfo,
	context,
) {
	// add click data to context
	context.clickDataItem = clickDataItem

	if (clickDataItem.status === 'tracked') {
		console.log(
			`\nAlready approved click id (${context.clickDataItem._id}), checking for order id`,
		)
		if (orderTracked) {
			console.log(`\nAlready approved !! ${context.adId}`)
		} else {
			console.log(`\nNormal approving... ${context.adId}`)
			await handleNormalApproval(context)
		}
	} else {
		console.log(`\nClick id not tracked. ${context.clickDataItem._id}`)
		if (orderTracked) {
			console.log(`\nAlready approved !! ${context.adId}`)
		} else {
			console.log(`\nApproving... ${context.adId}`)
			await handleNormalApproval(context)
		}
	}
}

async function handleNormalApproval(context) {
	try {
		const admin = await adminService.getAdminDetailsWithEmail(
			'<EMAIL>',
		)

		if (context.currency && context.currency !== 'INR') {
			try {
				// Convert saleAmount to INR
				const convertedSaleAmount = await convertCurrency(
					context.saleAmount,
					context.currency,
					'INR',
					process.env.EXCHANGE_RATE_API_KEY,
				)

				// Convert approvedPayout to INR
				const convertedApprovedPayout = await convertCurrency(
					context.approvedPayout,
					context.currency,
					'INR',
					process.env.EXCHANGE_RATE_API_KEY,
				)

				console.log(`Converted Sale Amount: ₹${convertedSaleAmount}`)
				console.log(`Converted Approved Payout: ₹${convertedApprovedPayout}`)

				// Update the context with converted values
				context.saleAmount = convertedSaleAmount
				context.approvedPayout = convertedApprovedPayout
			} catch (currencyError) {
				console.error(
					'Error handling currency conversion:',
					currencyError.message,
				)
			}
		}

		const payload = await createEarningObject(context)

		const earning = await earningServie.createEarnings(payload)
		if (!earning) {
			console.log('Failed to create new user earning!')
			return
		}

		await clickService.updateClickStatus(earning.click, 'tracked')

		// Get user and store information for detailed tracking message
		const user = context.clickDataItem.user
		const store = context.clickDataItem.store
		const detailedInfo = `
			<div style="max-width: 500px; margin: 0 auto; padding: 15px;">
				<h1 style="text-align: left; margin-bottom: 20px; font-size: 20px;">🎉 Click tracked!!!</h1>

				<div style="margin-bottom: 15px;">
				<p>👤 User Email: ${user?.email || 'N/A'}</p>
				<p>🔗 Click ID: ${context.clickDataItem.referenceId}</p>
				<p>💰 Earning ID: ${earning.uid}</p>
				<p>🏪 Store Name: ${store?.name || 'N/A'}</p>
				<p>🛒 Sale Amount: ₹${context.saleAmount || '0.00'}</p>
				<p>✅ Approved Payout: ₹${context.approvedPayout || '0.00'}</p>
				</div>
			</div>
			`

		// Send detailed info to bot API
		await sendDetailedInfoToBot(detailedInfo)

		const logs = [
			{
				admin: admin._id,
				log: `${admin.name} created new User Earning id ${earning.uid}  `,
			},
			{
				admin: admin._id,
				log: `${admin.name} updated User cashback click status to "tracked" ${context.clickDataItem.uid} . earning uid:${earning.uid}   `,
			},
		]
		await adminLogService.createMultipleLogs(logs)
		await earningServie.processEarningsAndSendMail(earning)
		await sendTrackedSmsToUser(
			context.clickDataItem.user,
			context.clickDataItem.store,
		)
	} catch (error) {
		console.error('Error in handleNormalApproval:', error)
	}
}

export async function createEarningObject(
	context,
	amount = 0,
	advertiserInfo = '',
) {
	const admin = await adminService.getAdminDetailsWithEmail(
		'<EMAIL>',
	)

	return {
		referenceId: context.clickDataItem.referenceId,
		trackingTime: context.datetime,
		orderUniqueId: context.adId,
		cashbackAmount: 0,
		orderCount: 1,
		amountGot: context.approvedPayout,
		saleAmount: context.saleAmount,
		confirmDate: context.confirmDate,
		offer: context.clickDataItem.offer,
		user: context?.clickDataItem?.user,
		click: context.clickDataItem._id,
		store: context.clickDataItem.store,
		affiliation: context.clickDataItem.affiliation,
		notes: context.notes,
		otherInfo: context.otherInfo,
		autoUpdated: true,
		//Change it to auto admin
		createdBy: admin._id,
		clickDate: context.clickDate,
		purchaseDate: context.purchaseDate,
	}
}

/**
 * Sends detailed tracking information to the bot API endpoint
 *
 * @async
 * @function sendDetailedInfoToBot
 * @param {string} detailedInfo - The HTML formatted detailed tracking information
 * @returns {Promise<void>} - A promise that resolves when the message is sent
 */
async function sendDetailedInfoToBot(detailedInfo) {
	try {
		const threadId = '1576'
		const response = await fetch(
			'https://api-main.indiancashback.com/bot/send-message',
			{
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					threadId,
					msg: detailedInfo,
				}),
			},
		)

		if (!response.ok) {
			throw new Error(`HTTP error! status: ${response.status}`)
		}

		const result = await response.json()
		console.log('✅ Successfully sent detailed info to bot API:', {
			status: response.status,
			response: result,
		})
	} catch (error) {
		console.error('❌ Error sending detailed info to bot API:', {
			error: error.message,
			stack: error.stack,
		})
		// Don't throw the error to prevent breaking the main flow
		// The tracking should continue even if bot notification fails
	}
}

async function sendTrackedSmsToUser(userId, storeId) {
	const user = await userService.findUserWithId(userId)
	const store = await storeService.findStoreById(storeId)

	const sms = `Hi user, we have successfully tracked your recent transaction at ${store?.name} via us. Actual cashback amount will be updated soon. -IndianCashback`

	const to = user.mobile

	// @ts-ignore
	const templateId = 1307160996134040338n

	await sendSms(to, sms, 'auto_track', templateId)
}

/**
 * Converts an amount from one currency to another using the Exchange Rate API.
 *
 * @async
 * @function convertCurrency
 * @param {number|string} amount - The amount to be converted. Can be a number or a string representing a number.
 * @param {string} fromCurrency - The currency code of the source currency (e.g., "EUR").
 * @param {string} toCurrency - The currency code of the target currency (e.g., "INR").
 * @param {string} apiKey - The API key for accessing the Exchange Rate API.
 * @returns {Promise<string>} - A promise that resolves to the converted amount as a string rounded to 2 decimal places.
 * @throws {Error} - Throws an error if the API call fails or if the conversion cannot be performed.
 *
 * @example
 * // Example usage:
 * const convertedAmount = await convertCurrency(100, "USD", "INR", "your-api-key");
 * console.log(`Converted Amount: ₹${convertedAmount}`);
 */
async function convertCurrency(amount, fromCurrency, toCurrency, apiKey) {
	try {
		// Construct the API URL
		const url = `https://v6.exchangerate-api.com/v6/${apiKey}/pair/${fromCurrency}/${toCurrency}`

		// Fetch the exchange rate
		const response = await fetch(url)
		const data = await response.json()

		// Check if the API call was successful
		if (data.result === 'success') {
			const exchangeRate = data.conversion_rate
			const convertedAmount =
				Number.parseFloat(amount.toString()) * exchangeRate
			return convertedAmount.toFixed(2) // Return the converted amount rounded to 2 decimal places
		}
	} catch (error) {
		console.error('Error converting currency:', error.message)
		throw error
	}
}

//  Approving... TESTCLICK
// {
//   saleAmount: "5.87",
//   approvedPayout: "1.17",                                                                            adId: "TESTCLICK",
//   datetime: "2025-03-08 14:51:27",
//   confirmDate: 2025-06-08T09:21:27.000Z,
//   partnerId: new ObjectId('66f206eb28628113b48568d9'),
//   notes: "",
//   otherInfo: "",
//   clickDate: undefined,
//   purchaseDate: undefined,
//   currency: "EUR",
//   clickDataItem: {
//     _id: new ObjectId('67cfe8a6b2513dbb012e1149'),
//     uid: 5221,
//     referenceId: "CBCLK8WJKIWX8GL",
//     store: new ObjectId('66f206ec28628113b4856c32'),
//     userIp: "**********",
//     title: "NameCheap",
//     url: "https://s3-ap-southeast-1.amazonaws.com/icb-images/storeLogos/namecheap-cashback-and-coupon-offers-31085161.png",
//     userCity: "Mumbai",
//     type: "express",
//     status: "clicked",
// device: "Generic Smartphone 0.0.0",
//     user: new ObjectId('66f206e728628113b48508d9'),
//     affiliation: new ObjectId('66f206eb28628113b48568d9'),
//     createdAt: 2025-03-11T07:39:18.324Z,
//     updatedAt: 2025-03-11T07:39:18.324Z,
//     __v: 0,
//   },
// } context in normal approval
// ┌───────────────┬───────────┐
// │               │ Values    │
// ├───────────────┼───────────┤
// │ orderUniqueId │ TESTCLICK │
// └───────────────┴───────────┘
// ┌───────────────┬───────────┐
// │               │ Values    │
// ├───────────────┼───────────┤
// │           uid │ 156044    │
// │ orderUniqueId │ TESTCLICK │
// └───────────────┴───────────┘
