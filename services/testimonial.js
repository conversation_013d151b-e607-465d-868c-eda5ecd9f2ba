import { HttpException } from '../exceptions/httpException.js'
import { Testimonial } from '../models/testimonial.js'

export class TestimonialService {
  createTestimonial = async (payload) => {
    const testimonial = await Testimonial.create(payload)
    if (!testimonial) {
      throw new HttpException(404, "filed to create new testimonial")
    }
    return testimonial
  }

  getTestimonialDetails = async (id) => {
    const testimonial = await Testimonial.findById(id)
    if (!testimonial) {
      throw new HttpException(404, "resource not found")
    }
    return testimonial
  }

  updateTestimonial = async (id, payload) => {
    const testimonial = await Testimonial.findById(id)
    if (!testimonial) {
      throw new HttpException(404, "resource not found")
    }
    await Testimonial.findOneAndUpdate({ _id: id }, { $set: payload })
    return testimonial
  }

  updateTestimonialActiveStatus = async (id) => {
    const testimonial = await Testimonial.findById(id)
    if (!testimonial) {
      throw new HttpException(404, "resource not found")
    }
    await Testimonial.findOneAndUpdate({ _id: id }, { $set: { active: !testimonial.active } })
    return testimonial
  }

  getAllTestimonials = async (query) => {
    const pageSize = Number(query.pageSize) || 5
    const page = Number(query.page) || 1
    const sort = { _id: -1 }

    const keyword = {}
    if (query.admin) {
      keyword.createdBy = query.admin
    }
    const count = await Testimonial.countDocuments({ ...keyword })
    const allTestimonials = await Testimonial.find({ ...keyword })
      .sort(sort)
      .limit(pageSize)
      .skip((page - 1) * pageSize)

    return {
      search: query.search,
      allTestimonials,
      page,
      pageSize,
      pages: Math.ceil(count / pageSize),
    }
  }

}