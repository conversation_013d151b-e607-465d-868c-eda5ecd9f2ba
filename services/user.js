import { Types } from "mongoose";
import { HttpException } from "../exceptions/httpException.js";
import { User } from "../models/user/user.js";
import { EarningService } from "./earning.js";
import { PaymentRequest } from "../models/payments/paymentRequest.js";
import { Earnings } from "../models/user/earnings.js";

export class UserService {
  earningService = new EarningService();

  loginUserWithCredentials = async (email, password) => {
    const user = await User.findOne({ email }).select("+password");
    if (!user) {
      throw new HttpException(404, "Credentials are incorrect!");
    }
    if (!user.active) {
      throw new HttpException(
        401,
        "Permission denied!, account blocked by organizations please contact help centre"
      );
    }
    const isPasswordMatched = await user.comparePassword(password);
    if (!isPasswordMatched) {
      throw new HttpException(403, "oops! password does not match");
    }
    const token = await user.getJwtToken();

    return {
      user: {
        name: user.name,
        email: user.email,
        createdAt: user.createdAt,
        count: user.count,
      },
      token,
    };
  };

  getAllUsers = async (query) => {
    const pageSize = Number(query.pageSize) || 5;
    const page = Number(query.page) || 1;
    const sort = query.sort ? query.sort : { _id: -1 };

    const keyword = query.search
      ? {
          $or: [
            {
              name: {
                $regex: query.search,
                $options: "i",
              },
            },
            {
              email: {
                $regex: query.search,
                $options: "i",
              },
            },
          ],
        }
      : {};
    if (query.startDate && query.endDate) {
      keyword.createdAt = {
        $gte: new Date(query.startDate),
        $lte: new Date(query.endDate),
      };
    }
    const count = await User.countDocuments({ ...keyword });
    const allUsers = await User.find({ ...keyword }).populate("referral", "uid email name")
      .sort(sort)
      .limit(pageSize)
      .skip((page - 1) * pageSize);

    return {
      search: query.search,
      allUsers,
      page,
      pageSize,
      pages: Math.ceil(count / pageSize),
    };
  };

  getAllUsersList = async () => {
    const allUsers = await User.find({}, "name uid email _id").sort({
      _id: -1,
    });
    //   .limit(2000);
    return allUsers;
  };

  updateUserActiveStatus = async (id) => {
    const user = await User.findById(id);
    if (!user) {
      throw new HttpException(404, "user not found!");
    }
    if (user.status !== "inactive") {
      user.status = user.status === "active" ? "blocked" : "active";
    } else {
      throw new HttpException(400, "inactive user");
    }
    await user.save();
    return user;
  };

  writeUserNotes = async (userId, notes, adminId) => {
    const user = await User.findById(userId);
    if (!user) {
      throw new HttpException(404, "user not found!");
    }
    user.notes = notes;
    user.updatedBy = adminId;
    await user.save();
    return user;
  };

  getUserStatistics = async (userId) => {
    const user = await User.findById(userId);

    if (!user) {
      throw new HttpException(404, "user not found!");
    }
    const totalCancelledEarnings =
      await this.earningService.getTotalAmountWithStatusAndUser({
        userId: user.id,
        status: "cancelled",
      });
    const totalConfirmedEarnings =
      await this.earningService.getTotalAmountWithStatusAndUser({
        userId: user.id,
        status: "confirmed",
      });

    const totalPendingEarnings =
      await this.earningService.getTotalAmountWithStatusAndUser({
        userId: user.id,
        status: "pending",
      });

    const totalCancelledRewardPoints =
      await this.earningService.getTotalAmountWithStatusAndUser({
        userId: user.id,
        status: "cancelled",
      });

    const totalConfirmedRewardPoints =
      await this.earningService.getTotalAmountWithStatusAndUser({
        userId: user.id,
        status: "confirmed",
        rewardPoint: true,
      });

    const totalPendingRewardPoints =
      await this.earningService.getTotalAmountWithStatusAndUser({
        userId: user.id,
        status: "pending",
        rewardPoint: true,
      });

    //getting total ReferralCommission
    const totalReferralCommission = await User.aggregate([
      {
        $match: {
          referral: user._id,
        },
      },
      {
        $lookup: {
          from: "earnings", // Name of the collection to join
          localField: "_id", // Field from the 'user' collection
          foreignField: "user", // Field from the 'earnings' collection
          as: "userEarnings", // Output array field
        },
      },
      {
        $unwind: "$userEarnings", // Deconstruct the array field
      },
      {
        $match: {
          "userEarnings.status": "confirmed", // Filter by status 'confirmed'
        },
      },
      {
        $group: {
          _id: null,
          totalCommission: { $sum: "$userEarnings.referralCommission" }, // Sum referralCommission
        },
      },
      {
        $project: {
          _id: 0, // Exclude _id field
          totalCommission: 1,
        },
      },
    ]).exec();

    //getting total ReferralCommission
    const totalReferralCommissionPending = await User.aggregate([
      {
        $match: {
          referral: user._id,
        },
      },
      {
        $lookup: {
          from: "earnings", // Name of the collection to join
          localField: "_id", // Field from the 'user' collection
          foreignField: "user", // Field from the 'earnings' collection
          as: "userEarnings", // Output array field
        },
      },
      {
        $unwind: "$userEarnings", // Deconstruct the array field
      },
      {
        $match: {
          "userEarnings.status": { $in: ["pending", "tracked_for_confirm"] }, // Filter by status 'confirmed':
        },
      },
      {
        $group: {
          _id: null,
          totalCommission: { $sum: "$userEarnings.referralCommission" }, // Sum referralCommission
        },
      },
      {
        $project: {
          _id: 0, // Exclude _id field
          totalCommission: 1,
        },
      },
    ]).exec();

    const totalWithdrawnAmount = await PaymentRequest.aggregate([
      {
        $match: {
          withdrawer: user._id,
        },
      },
      {
        $group: {
          _id: null,
          totalWithdrawnAmount: { $sum: "$withdrawAmount" }, // Sum referralCommission
        },
      },
    ]).exec();

    const userStaticstics = {
      withdrawableBalance: user?.balance,
      confirmedBalance: totalConfirmedEarnings,
      cancelledBalance: totalCancelledEarnings,
      pendingBalance: user?.pendingBalance,
      pendingRewards: totalPendingRewardPoints,
      confirmedRewards: totalConfirmedRewardPoints,
      cancelledRewards: totalCancelledRewardPoints,
      pendingReferralCommission:
        totalReferralCommissionPending[0]?.totalCommission,
      confirmedReferralCommission: totalReferralCommission[0]?.totalCommission,
      withDrawnBalance: totalWithdrawnAmount[0]?.totalWithdrawnAmount,
      name: user?.name,
      email: user?.email,
      avatar: user?.avatar,
    };
    return userStaticstics;
  };

  registerWithCredentials = async (payload) => {
    let user = await User.findOne({ email: payload.email });
    if (user) {
      throw new HttpException(409, "already have an account with this email");
    }
    user = await User.findOne({ mobile: payload.mobile });
    if (user) {
    }
    const newUser = await User.create(payload);
    if (!newUser) {
      throw new HttpException(404, "failed to create new user");
    }
    const token = await newUser.getJwtToken();

    return {
      user: {
        name: newUser.name,
        email: newUser.email,
        createdAt: newUser.createdAt,
        count: newUser.count,
      },
      token,
    };
  };
  updateUserPendingBalance = async (userId, amount, adminId) => {
    const user = await User.findById(userId);
    if (!user) {
      throw new HttpException(400, "user not found");
    }
    await User.updateOne(
      { _id: userId },
      {
        $set: {
          pendingBalance: user?.pendingBalance + Number(amount),
          updatedBy: adminId,
        },
      }
    );
    return true;
  };

  updateUserRewardPoints = async (userId, amount, adminId) => {
    const user = await User.findById(userId);
    if (!user) {
      throw new HttpException(400, "user not found");
    }
    await User.updateOne(
      { _id: userId },
      {
        $set: {
          pendingRewardPoints: user?.pendingRewardPoints + Number(amount),
          updatedBy: adminId,
        },
      }
    );
    return true;
  };

  updateAddUserBalance = async (
    userId,
    cashbackAmount,
    ReferralAmount,
    adminId
  ) => {
    const user = await User.findById(userId);
    if (!user) {
      throw new HttpException(400, "user not found");
    }

    // Update user's balance
    await User.updateOne(
      { _id: userId },
      {
        $set: {
          balance: Number(user.balance + cashbackAmount),
          pendingBalance: Number(user.pendingBalance - cashbackAmount),
          updatedBy: adminId,
        },
      }
    );

    if (user?.referral) {
      // Check if user's balance has reached 200 rupees
      const updatedUser = await User.findById(userId);
      if (updatedUser.balance >= 200) {
        // Find pending referral earnings
        const pendingEarnings = await Earnings.findOne({
          user: user.referral,
          referralUser: userId,
          earningsType: "referral",
          status: "pending",
        });

        // Update each earning and add cashback amount to referrer's balance
        if (pendingEarnings) {
          // Update the earning status to confirmed
          await Earnings.updateOne(
            { _id: pendingEarnings._id },
            {
              $set: {
                status: "confirmed",
                updatedBy: adminId,
              },
            }
          );

          // Add the cashback amount from the earning to the referrer's balance
          const referrer = await User.findById(user.referral);
          await User.updateOne(
            { _id: user.referral },
            {
              $set: {
                balance: Number(
                  referrer.balance + pendingEarnings.cashbackAmount
                ),
                updatedBy: adminId,
              },
            }
          );
        }
      }

      // Update referrer's balance
      const referrer = await User.findById(user?.referral);
      await User.updateOne(
        { _id: user.referral },
        {
          $set: {
            balance: Number(referrer.balance + ReferralAmount),
            pendingBalance: Number(referrer.pendingBalance - ReferralAmount),
            updatedBy: adminId,
          },
        }
      );
    }
    return true;
  };

  updateAddUserRewardPoints = async (
    userId,
    cashbackAmount,
    referralAmount,
    adminId
  ) => {
    const user = await User.findById(userId);
    if (!user) {
      throw new HttpException(400, "user not found");
    }

    // Update user's reward points
    await User.updateOne(
      { _id: userId },
      {
        $set: {
          rewardPoints: Number(user.rewardPoints + cashbackAmount),
          pendingRewardPoints: Number(
            user.pendingRewardPoints - cashbackAmount
          ),
          updatedBy: adminId,
        },
      }
    );

    if (user?.referral) {
      // Check if user's balance has reached 200 rupees
      const updatedUser = await User.findById(userId);
      if (updatedUser.balance >= 200) {
        // Find pending referral earnings
        const pendingEarnings = await Earnings.find({
          user: user.referral,
          referralUser: userId,
          earningsType: "referral",
          status: "pending",
        });

        // Update each earning and add cashback amount to referrer's balance
        for (const earning of pendingEarnings) {
          // Update the earning status to confirmed
          await Earnings.updateOne(
            { _id: earning._id },
            {
              $set: {
                status: "confirmed",
                updatedBy: adminId,
              },
            }
          );

          // Add the cashback amount from the earning to the referrer's balance
          const referrer = await User.findById(user.referral);
          await User.updateOne(
            { _id: user.referral },
            {
              $set: {
                balance: Number(referrer.balance + earning.cashbackAmount),
                updatedBy: adminId,
              },
            }
          );
        }
      }

      // Update referrer's balance
      const referrer = await User.findById(user?.referral);
      await User.updateOne(
        { _id: user.referral },
        {
          $set: {
            balance: Number(referrer.balance + referralAmount),
            pendingBalance: Number(referrer.pendingBalance - referralAmount),
            updatedBy: adminId,
          },
        }
      );
    }
    return true;
  };

  updateRemoveUserBalance = async (
    userId,
    cashbackAmount,
    referralAmount,
    adminId
  ) => {
    const user = await User.findById(userId);
    if (!user) {
      throw new HttpException(400, "user not found");
    }
    await User.updateOne(
      { _id: userId },
      {
        $set: {
          pendingBalance: Number(user.pendingBalance + cashbackAmount),
          balance: Number(user?.balance - cashbackAmount),
          updatedBy: adminId,
        },
      }
    );
    if (user?.referral) {
      const referrer = await User.findById(user?.referral);
      try {
        await User.updateOne(
          { _id: user.referral },
          {
            $set: {
              pendingBalance: Number(referrer.pendingBalance + referralAmount),
              balance: Number(referrer?.balance - referralAmount),
              updatedBy: adminId,
            },
          }
        );
      } catch (error) {}
    }
    return true;
  };

  rejectWithDrowalBalance = async (userId, balance, adminId) => {
    const userBalance = Number(balance);
    const user = await User.findById(userId);
    if (!user) {
      throw new HttpException(400, "user not found");
    }

    await User.updateOne(
      { _id: user._id },
      {
        $set: {
          balance: Number(user.balance) + userBalance,
          updatedBy: adminId,
        },
      }
    );

    return user;
  };

  findUserWithId = async (userId) => {
    const user = await User.findById(userId);
    if (!user) {
      return null;
    }
    return user;
  };
}
