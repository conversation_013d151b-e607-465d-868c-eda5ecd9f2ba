import { HttpException } from "../exceptions/httpException.js";
import { Admin } from "../models/admin/admin.js";
import AdminToken from "../models/admin/adminToken.js";

export class AuthenticationService {
  adminLogin = async (email, password) => {
    const admin = await Admin.findOne({ email }).select("+password");

    if (!admin) {
      throw new HttpException(404, "Credentials are incorrect!");
    }
    if (admin.block) {
      throw new HttpException(401, "Permission denied! blocked by super admin");
    }
    const isPasswordMatched = await admin.comparePassword(password);
    if (!isPasswordMatched) {
      throw new HttpException(403, "oops! password does not match");
    }
    const { accessToken, refreshToken } = await admin.getJwtToken();
    return {
      accessToken,
      refreshToken,
      admin: {
        name: admin?.name,
        _id: admin?._id,
        avatar: admin?.avatar,
        role: admin?.role,
      },
    };
  };

  refreshToken = async (token) => {
    const tokenData = await AdminToken.findOne({ token });
    if (!tokenData) {
      throw new HttpException(
        401,
        "jwt token not found! please login to access"
      );
    }
  };
}
