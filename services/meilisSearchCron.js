import { MeiliSearchService } from "../config/meilisearch";



export const removeInactiveOfferFromMeili = async () => {

    const meilisearch = new MeiliSearchService();

    try {
        // Filter to delete documents where the "active" field is false or "status" is "inactive"
        const deleteResponse = await meilisearch.removeExpiredOffers()
        console.log("Deleted inactive offers from MeiliSearch:", deleteResponse);
    } catch (error) {
        console.error("Error deleting inactive offers from MeiliSearch:", error);
    }
};

