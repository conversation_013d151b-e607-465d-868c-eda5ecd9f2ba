import { HttpException } from '../exceptions/httpException.js'
import { OnGoingSaleOffers } from '../models/offer/onGoingSales.js'

export class OnGoingSaleOfferService {

    createOnGoingSale = async (payload) => {
        const onGoingSale = await OnGoingSaleOffers.create(payload)
        if (!onGoingSale) {
            throw new HttpException(404, "filed to create new on going sale")
        }
        return onGoingSale
    }

    getAllOnGoingSales = async (query) => {
        const pageSize = Number(query.pageSize) || 5
        const page = Number(query.page) || 1
        const sort = { _id: -1 }

        const keyword = query.search
            ? {
                $or: [
                    {
                        saleName: {
                            $regex: query.search,
                            $options: 'i',
                        },
                    },
                ],
            }
            : {}
        if (query.admin) {
            keyword.createdBy = query.admin
        }
        const count = await OnGoingSaleOffers.countDocuments({ ...keyword })
        const allOngoingSales = await OnGoingSaleOffers.find({ ...keyword })
            .populate('createdBy', 'name uid')
            .populate('offers', 'cashbackTitle uid')
            .sort(sort)
            .limit(pageSize)
            .skip((page - 1) * pageSize)

        return {
            search: query.search,
            allOngoingSales,
            page,
            pageSize,
            pages: Math.ceil(count / pageSize),
        }
    }

    getOnGoingSaleDetails = async (id) => {
        const onGoingSale = await OnGoingSaleOffers.findById(id).populate('offers', "cashbackTitle ")
        if (!onGoingSale) {
            throw new HttpException(404, "resource not found")
        }
        return onGoingSale
    }

    updateOnGoingSale = async (id, body) => {
        const onGoingSale = await OnGoingSaleOffers.findById(id)
        if (!onGoingSale) {
            throw new HttpException(404, "resource not found")
        }
        await OnGoingSaleOffers.updateOne({ _id: id }, { $set: body })
        return onGoingSale
    }


    handleBlock = async (id) => {
        const onGoingSale = await OnGoingSaleOffers.findById(id)
        if (!onGoingSale) {
            throw new HttpException(404, "resource not found")
        }
        await OnGoingSaleOffers.updateOne({ _id: id }, { $set: { active: !onGoingSale.active } })
        return onGoingSale
    }



    deleteOnGoingSale = async (id) => {
        const onGoingSale = await OnGoingSaleOffers.findById(id)
        if (!onGoingSale) {
            throw new HttpException(404, "resource not found")
        }
        await OnGoingSaleOffers.deleteOne({ _id: id })
        return true
    }

}
