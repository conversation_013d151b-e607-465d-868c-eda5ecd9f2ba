import { ErrorTracker } from '../models/error.js'

export const createErrorLog = async (error, user, admin, request) => {
	const payload = {
		message: error?.message,
		statusCode: error?.status ? error?.status : 500,
		stackTrace: error?.errors,
		body: JSON.stringify(request?.body),
		params: JSON.stringify(request?.params),
		query: JSON.stringify(request?.query),
		requestUrl: request?.url,
		admin,
		user,
		requestHeaders: JSON.stringify(request?.headers),
	}
	await ErrorTracker.create(payload)
}
