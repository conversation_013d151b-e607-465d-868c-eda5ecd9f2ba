import mongoose from "mongoose";
import { MeiliSearchService } from "../config/meilisearch.js";
import { HttpException } from "../exceptions/httpException.js";
import { OfferSchema } from "../models/meilisearch/offer.js";
import { Offer } from "../models/offer/offer.js";
import { Store } from "../models/stores/store.js";

export class OfferService {
  meilisearch = new MeiliSearchService();
  createOffer = async (body) => {
    const store = await Store.findById(body.store);
    if (!store) {
      throw new HttpException(404, "resource not found!");
    }
    const offer = await Offer.create(body);
    if (!offer) {
      throw new HttpException(404, "failed to create new offer ");
    }
    const newOffer = await Offer.findById(offer._id)
      .populate("store", "name")
      .populate("categories.category", "name")
      .populate("categories.subCategories", "name")
      .populate(
        "storeCategory",
        "name gettingType oldUserOfferAmount newUserOfferAmount oldUserOfferPercent newUserOfferPercent"
      );

    const categories = [];
    const subcategories = [];
    newOffer.categories.map((item) => {
      categories.push({ name: item?.category?.name });
      item?.subCategories?.map((subCat) => {
        if (!subcategories.some((subCatItem) => subCatItem.name === subCat?.name)) {
          subcategories.push({ name: subCat?.name });
        }
      });
    });
    const newUserOffer =
      newOffer?.storeCategory?.gettingType === "amount"
        ? `₹${newOffer?.storeCategory?.newUserOfferAmount}`
        : `${newOffer?.storeCategory?.newUserOfferPercent}%`;

    const oldUserOffer =
      newOffer?.storeCategory?.gettingType === "amount"
        ? `₹${newOffer?.storeCategory?.oldUserOfferAmount}`
        : `${newOffer?.storeCategory?.oldUserOfferPercent}%`;

    const offerDocument = new OfferSchema({
      id: newOffer?._id,
      uid: newOffer?.uid,
      active: newOffer?.active,
      url: newOffer.productImage.secureUrl,
      title: newOffer?.title,
      offerType: newOffer?.offerType,
      description: newOffer?.description,
      couponCode: newOffer?.couponCode,
      offerPercent: newOffer?.offerPercent,
      offerAmount: newOffer?.offerAmount,
      offerWarning: newOffer?.store?.offerWarning,
      store: newOffer?.store?.name,
      dateExpiry: newOffer?.dateExpiry,
      categories,
      subcategories,
      storecategories: [{ name: newOffer?.storeCategory?.name }],
      newUserOffer,
      oldUserOffer,
    });
    await this.meilisearch.addDocuments(offerDocument);
    return offer;
  };

  getAllOffers = async (query) => {
    const pageSize = Number(query.pageSize) || 10;
    const page = Number(query.page) || 1;

    let keyword = query.search
      ? {
          $or: [
            {
              title: {
                $regex: query.search,
                $options: "i",
              },
            },
          ],
        }
      : {};

    if (query.couponCode) {
      keyword = {
        ...keyword,
        $or: [
          {
            couponCode: {
              $regex: query.couponCode,
              $options: "i",
            },
          },
        ],
      };
    }

    if (query.store && query.store !== "") {
      keyword.store = new mongoose.Types.ObjectId(query.store);
    }

    if (query.affiliation && query.affiliation !== "") {
      keyword.affiliation = new mongoose.Types.ObjectId(query.affiliation);
    }

    if (query.admin && query.admin !== "") {
      keyword.createdBy = new mongoose.Types.ObjectId(query.admin);
    }

    const currentDate = new Date();
    const startOfToday = new Date(currentDate);
    startOfToday.setHours(0, 0, 0, 0); // Start of today

    const endOfToday = new Date(currentDate);
    endOfToday.setHours(23, 59, 59, 999); // End of today

    console.log("keyword", keyword);

    // Perform calculations based on the filtered keyword
    const allOffersCount = await Offer.countDocuments(keyword); // Count based on filtered keywords
    const allActiveOffers = await Offer.countDocuments({
      ...keyword,
      active: true, // Ensure active is true
      dateExpiry: { $gte: currentDate }, // Only future expiry dates
    });
    const allExpiredOffers = await Offer.countDocuments({
      ...keyword,
      $or: [
        { active: false }, // Either active is false
        { dateExpiry: { $lt: currentDate } }, // Or the expiry date has passed
      ],
    });

    const offersExpiredOffersToday = await Offer.countDocuments({
      ...keyword,

      dateExpiry: { $gte: startOfToday, $lte: endOfToday }, // Only offers expiring today
    });

    // Handle offerType conditions
    if (query.offerType === "active") {
      keyword = {
        ...keyword,
        active: true, // Ensure the offer is active
        dateExpiry: { $gte: currentDate }, // Ensure the expiry date hasn't passed
      };
    } else if (query.offerType === "expired") {
      keyword = {
        ...keyword,
        $or: [
          { active: false }, // Either active is false
          { dateExpiry: { $lt: currentDate } }, // Or the expiry date has passed
        ],
      };
    }

    // Determine sort order based on query.sort parameter
    let sortOptions = { createdAt: -1 }; // Default sort by creation date (newest first)

    if (query.sort) {
      switch (query.sort) {
        case "title": {
          sortOptions = { title: 1 }; // Sort alphabetically by title
          break;
        }
        case "highPriority": {
          sortOptions = { priority: -1 }; // Sort by priority (highest first)
          break;
        }
        case "lowPriority": {
          sortOptions = { priority: 1 }; // Sort by priority (lowest first)
          break;
        }
        case "expiry": {
          sortOptions = { dateExpiry: 1 }; // Sort by expiry date (soonest first)
          break;
        }
        case "lastUpdated": {
          sortOptions = { updatedAt: -1 }; // Sort by last updated date (newest first)
          break;
        }
        default: {
          sortOptions = { createdAt: -1 }; // Default sort
          break;
        }
      }
    }

    // Apply pagination
    const count = await Offer.countDocuments(keyword);
    const allOffers = await Offer.find(keyword)
      .sort(sortOptions)
      .populate("createdBy", "name")
      .populate("updatedBy", "name")
      .populate("store", "uid name ")
      .populate("affiliation", "name")
      .limit(pageSize)
      .skip((page - 1) * pageSize);

    return {
      allOffers,
      allOffersCount,
      allExpiredOffers,
      offersExpiredOffersToday,
      allActiveOffers,
      page,
      pages: Math.ceil(count / pageSize),
      pageSize,
      search: query.search,
      sort: query.sort || "lastUpdated", // Include the sort parameter in the response
    };
  };

  getActiveOffersCountByStore = async (storeId) => {
    const activeOffersCount = await Offer.countDocuments({
      store: storeId,
      active: true,
    });
    return activeOffersCount;
  };

  getAllOffersList = async (query) => {
    const currentDate = new Date();
    const keywords = query.active
      ? { dateExpiry: { $gt: currentDate }, active: true }
      : {};

    const allOffers = await Offer.find(
      { ...keywords },
      "title uid active"
    ).sort({
      _id: -1,
    });
    return allOffers;
  };
  getAllTrendingOffers = async (query) => {
    const pageSize = Number(query.pageSize) || 10;
    const page = Number(query.page) || 1;

    // Get current date for filtering expired offers
    const currentDate = new Date();

    let keyword = query.search
      ? {
          $or: [
            {
              title: {
                // Changed from cashbackTitle to title to match the schema
                $regex: query.search,
                $options: "i",
              },
            },
          ],
        }
      : {};

    // Default filters: trending, active, and not expired
    keyword.trending = true;
    keyword.active = true;
    keyword.dateExpiry = { $gte: currentDate }; // Only include non-expired offers

    if (query.couponCode) {
      keyword = {
        ...keyword,
        $or: [
          {
            couponCode: {
              $regex: query.couponCode,
              $options: "i",
            },
          },
        ],
      };
    }
    // Determine sort order based on query.sort parameter
    let sortOptions = { createdAt: -1 }; // Default sort by creation date (newest first)

    if (query.sort) {
      switch (query.sort) {
        case "title": {
          sortOptions = { title: 1 }; // Sort alphabetically by title
          break;
        }
        case "highPriority": {
          sortOptions = { trendingPriority: -1 }; // Sort by trending priority (highest first)
          break;
        }
        case "lowPriority": {
          sortOptions = { trendingPriority: 1 }; // Sort by trending priority (lowest first)
          break;
        }
        case "expiry": {
          sortOptions = { dateExpiry: 1 }; // Sort by expiry date (soonest first)
          break;
        }
        case "lastUpdated": {
          sortOptions = { updatedAt: -1 }; // Sort by last updated date (newest first)
          break;
        }
        default: {
          sortOptions = { createdAt: -1 }; // Default sort
          break;
        }
      }
    }

    const count = await Offer.countDocuments({ ...keyword });
    const allTrendingOffers = await Offer.find({ ...keyword })
      .sort(sortOptions)
      .populate("createdBy", "name")
      .populate("updatedBy", "name")
      .populate("store")
      .populate("affiliation", "name")
      .limit(pageSize)
      .skip((page - 1) * pageSize);

    return {
      allTrendingOffers,
      page,
      pages: Math.ceil(count / pageSize),
      pageSize,
      search: query.search,
      sort: query.sort || "lastUpdated", // Include the sort parameter in the response
    };
  };

  getOfferDetails = async (offerId) => {
    const offer = await Offer.findById(offerId)
      .populate("store", "name")
      .populate("affiliation", "name")
      .populate("storeCategory", "name");

    if (!offer) {
      throw new HttpException(404, "resource not found");
    }
    return offer;
  };
  updateOfferPriority = async (offerId, priority, updatedBy) => {
    const offer = await Offer.findById(offerId);
    if (!offer) {
      throw new HttpException(404, "resource not found");
    }
    await Offer.updateOne(
      { _id: offerId },
      {
        $set: {
          priority,
          updatedBy,
          editedDate: new Date(),
        },
      }
    );
    return offer;
  };
  updateOffer = async (offerId, payload) => {
    const offer = await Offer.findById(offerId);
    if (!offer) {
      throw new HttpException(404, "resource not found");
    }

    await Offer.updateOne({ _id: offerId }, { $set: payload });
    const newOffer = await Offer.findById(offer._id)
      .populate("store", "name")
      .populate("categories.category", "name")
      .populate("categories.subCategories", "name")
      .populate(
        "storeCategory",
        "name gettingType oldUserOfferAmount newUserOfferAmount oldUserOfferPercent newUserOfferPercent"
      );

    const categories = [];
    const subcategories = [];
    newOffer.categories.map((item) => {
      categories.push({ name: item?.category?.name });
      item?.subCategories?.map((subCat) => {
        if (!subcategories.includes({ name: subCat?.name })) {
          subcategories.push({ name: subCat?.name });
        }
      });
    });
    const newUserOffer =
      newOffer?.storeCategory?.gettingType === "amount"
        ? `₹${newOffer?.storeCategory?.newUserOfferAmount}`
        : `${newOffer?.storeCategory?.newUserOfferPercent}%`;

    const oldUserOffer =
      newOffer?.storeCategory?.gettingType === "amount"
        ? `₹${newOffer?.storeCategory?.oldUserOfferAmount}`
        : `${newOffer?.storeCategory?.oldUserOfferPercent}%`;

    const offerDocument = new OfferSchema({
      id: newOffer?._id,
      uid: newOffer?.uid,
      active: newOffer?.active,
      title: newOffer?.title,
      url: newOffer.productImage.secureUrl,
      offerType: newOffer?.offerType,
      description: newOffer?.description,
      couponCode: newOffer?.couponCode,
      offerPercent: newOffer?.offerPercent,
      offerAmount: newOffer?.offerAmount,
      offerWarning: newOffer?.store?.offerWarning,
      store: newOffer?.store?.name,
      dateExpiry: newOffer?.dateExpiry,
      categories,
      subcategories,
      storecategories: [{ name: newOffer?.storeCategory?.name }],
      newUserOffer,
      oldUserOffer,
    });
    await this.meilisearch.updateDocuments(offerDocument);
    return newOffer;
  };

  updateOfferActiveStatus = async (offerId, updatedBy) => {
    const offer = await Offer.findById(offerId);
    if (!offer) {
      throw new HttpException(404, "resource not found");
    }
    // Update the offer's active status in the database
    await Offer.updateOne(
      { _id: offerId },
      {
        $set: {
          active: !offer.active,
          updatedBy,
          editedDate: new Date(),
        },
      }
    );

    const updatedOffer = await Offer.findById(offerId);

    try {
      if (updatedOffer.active) {
        // If active is set to true, add the complete offer document to MeiliSearch
        const categories = offer.categories.map((item) => ({
          name: item?.category?.name,
        }));
        const subcategories = offer.categories.flatMap((item) =>
          item?.subCategories.map((subCat) => ({ name: subCat?.name }))
        );

        const newUserOffer =
          offer.storeCategory?.gettingType === "amount"
            ? `₹${offer.storeCategory?.newUserOfferAmount}`
            : `${offer.storeCategory?.newUserOfferPercent}%`;

        const oldUserOffer =
          offer.storeCategory?.gettingType === "amount"
            ? `₹${offer.storeCategory?.oldUserOfferAmount}`
            : `${offer.storeCategory?.oldUserOfferPercent}%`;

        const offerDocument = new OfferSchema({
          id: offer._id.toString(), // Unique ID (use _id or uid)
          uid: offer?.uid, // Unique user ID (if applicable)
          active: offer.active, // Active status
          url: offer.productImage.secureUrl, // URL of the product image
          title: offer?.title, // Title of the offer
          offerType: offer?.offerType, // Offer type (if applicable)
          description: offer?.description, // Detailed description
          couponCode: offer?.couponCode, // Coupon code if applicable
          offerPercent: offer?.offerPercent, // Percentage offer (if applicable)
          offerAmount: offer?.offerAmount, // Amount-based offer (if applicable)
          offerWarning: offer?.store?.offerWarning, // Offer warning (if applicable)
          store: offer?.store?.name, // Store name
          dateExpiry: offer?.dateExpiry || Date.now(), // Expiry date (timestamp)
          categories, // Categories
          subcategories, // Subcategories
          storecategories: [
            {
              name: offer?.storeCategory?.name || "", // Store category
            },
          ],
          newUserOffer,
          oldUserOffer,
        });

        // Add the offer document to MeiliSearch
        const response = await this.meilisearch.addDocuments(offerDocument);
        console.log("Added offer to MeiliSearch:", response);
      } else {
        // If active is set to false, remove the offer from MeiliSearch
        const response = await this.meilisearch.removeSelectedOffer(
          "672ee084c6f386e5b962835c"
        ); // Use offer.uid if you store uid in MeiliSearch
        console.log("Deleted offer from MeiliSearch:", response);
      }
    } catch (error) {
      console.error("Error interacting with MeiliSearch:", error);
      throw new Error("Failed to update offer in MeiliSearch");
    }

    return updatedOffer;
  };
  updateOfferTrendingStatus = async (offerId, updatedBy) => {
    const offer = await Offer.findById(offerId);
    if (!offer) {
      throw new HttpException(404, "resource not found");
    }
    await Offer.updateOne(
      { _id: offerId },
      {
        $set: {
          trending: !offer.trending,
          updatedBy,
          editedDate: new Date(),
        },
      }
    );
    return offer;
  };

  updateOfferMissedStatus = async (offerId, updatedBy) => {
    const offer = await Offer.findById(offerId);
    if (!offer) {
      throw new HttpException(404, "resource not found");
    }
    await Offer.updateOne(
      { _id: offerId },
      {
        $set: {
          missedDeal: !offer.missedDeal,
          updatedBy,
          editedDate: new Date(),
        },
      }
    );
    return offer;
  };

  updateOfferTrendingPriority = async (offerId, priority, updatedBy) => {
    const offer = await Offer.findById(offerId);
    if (!offer) {
      throw new HttpException(404, "resource not found!");
    }
    await Offer.updateOne(
      { _id: offerId },
      {
        $set: {
          trendingPriority: priority,
          updatedBy,
          editedDate: new Date(),
        },
      }
    );
    return offer;
  };
  updateMissedDeals = async (offerId) => {
    const offer = await Offer.findById(offerId);
    if (!offer) {
      throw new HttpException(404, "resource not found!");
    }
    await Offer.updateOne(
      { _id: offerId },
      {
        $set: {
          missedDeal: true,
        },
      }
    );
    return offer;
  };
}
