import { HttpException } from '../exceptions/httpException.js'
import { Banner } from '../models/banners/banner.js'

export class BannerService {
    createBanner = async payload => {
        const banner = await Banner.create(payload)
        if (!banner) {
            throw new HttpException(404, 'failed to create new banner')
        }
        return banner
    }

    getAllBanners = async query => {
        const pageSize = Number(query.pageSize) || 5
        const page = Number(query.page) || 1
        const sort = query.sort ? query.sort : { _id: -1 }

        const keyword = { type: 'context' }

        const count = await Banner.countDocuments({ ...keyword })
        const allBanners = await Banner.find({ ...keyword })
            .populate('createdBy', 'name')
            .sort(sort)
            .limit(pageSize)
            .skip((page - 1) * pageSize)
        return {
            search: query.search,
            allBanners,
            page,
            pageSize,
            pages: Math.ceil(count / pageSize),
        }
    }

    getBannerDetails = async (id, payload) => {
        const banner = await Banner.findById(id)
        if (!banner) {
            throw new HttpException(404, 'resource not found')
        }
        return banner
    }

    updateBanner = async (id, payload) => {
        const banner = await Banner.findById(id)
        if (!banner) {
            throw new HttpException(404, 'resource not found')
        }
        await Banner.updateOne({ _id: id }, { $set: payload })
        return banner
    }

    updateBannerActiveStatus = async id => {
        const banner = await Banner.findById(id)
        if (!banner) {
            throw new HttpException(404, 'resource not found')
        }
        banner.isActive = !banner.isActive
        await banner.save()
        return banner
    }

    updateBannerPriority = async (bannerId, priority, updatedBy) => {
        const banner = await Banner.findById(bannerId);
        if (!banner) {
            throw new HttpException(404, "resource not found");
        }
        await Banner.updateOne(
            { _id: bannerId },
            {
                $set: {
                    priority,
                    updatedBy,
                    editedDate: new Date(),
                },
            }
        );
        return banner;
    };
    deleteBanner = async id => {
        const banner = await Banner.findById(id)
        if (!banner) {
            throw new HttpException(404, 'resource not found')
        }
        await Banner.deleteOne({ _id: id })
        return true
    }
}
