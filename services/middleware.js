import { HttpException } from "../exceptions/httpException.js";
import { Middleware } from "../models/middleware.js";
import {
  CREATE_AFFILIATION,
  DELETE_AFFILIATION,
  UPDATE_AFFILIATION,
  VIEW_ALL_AFFILIATIONS,
  ACCESS_ADMIN_LOGS,
  ADD_NEW_ADMIN,
  ADD_NEW_CATEGORY,
  ADD_NEW_OFFER,
  B<PERSON><PERSON>K_ADMIN,
  CHANGE_ADMIN_STATUS,
  CREATE_CASHBACK,
  CREATE_COMMISSIONS,
  CREATE_GIFT_CARD,
  CREATE_GIFT_CARD_OFFERS,
  CREATE_GIFT_CARD_ORDERS,
  CREATE_GIFT_CARD_SLIDERS,
  CREATE_MID<PERSON><PERSON>WARE,
  CREATE_MISSED_DEALS,
  CREATE_MISSING_CASHBACK,
  CREATE_STORE,
  CREATE_STORE_CASHBACK_RATE,
  CREATE_STORIES,
  CREATE_SUB_CATEGORY,
  CREATE_TRENDING_STORE,
  CREATE_EARNINGS,
  <PERSON><PERSON><PERSON>_CASHBACK,
  DELE<PERSON>_CATEGORY,
  <PERSON><PERSON><PERSON>_COMMISSIONS,
  DELETE_GIFT_CARD,
  DELETE_GIFT_CARD_OFFER,
  DELETE_GIFT_CARD_SLIDER,
  DELETE_MISSED_DEALS,
  DELETE_MISSING_CASHBACK,
  DELETE_OFFER,
  DELETE_PAYMENT_REQUEST,
  DELETE_REFERRALS,
  DELETE_STORE,
  DELETE_STORE_CASHBACK_RATES,
  DELETE_STORE_REVIEWS,
  DELETE_STORIES,
  DELETE_SUB_CATEGORY,
  DELETE_TRENDING_STORES,
  DELETE_EARNINGS,
  EDIT_CASHBACK,
  EDIT_OFFER,
  FILTER_STORE_CASHBACK_RATES_HISTORY,
  GET_ALL_COMMISSIONS,
  GET_ALL_MISSED_DEALS,
  GET_ALL_MISSING_CASHBACK,
  GET_ALL_PAYMENT_REQUESTS,
  GET_ALL_REFERRALS,
  GET_ALL_STORE_REVIEWS,
  GET_ALL_STORIES,
  GET_ALL_EARNINGS,
  GET_APPROVED_EARNINGS,
  GET_CANCEL_EARNINGS,
  GET_COMMISSION_DETAILS,
  GET_CONFIRM_EARNINGS,
  GET_MISSING_CASHBACK_DETAILS,
  GET_PAYMENT_REQUEST_DETAILS,
  GET_REFERRAL_DETAILS,
  GET_CLICKS,
  GET_CLICK_DETAILS,
  GET_EARNINGS_DETAILS,
  GET_WITHOUT_EARNINGS,
  REMOVE_SUBCATEGORY,
  RESTORE_CATEGORY,
  UPDATE_CATEGORY,
  UPDATE_COMMISSIONS,
  UPDATE_GIFT_CARD,
  UPDATE_GIFT_CARD_OFFER,
  UPDATE_GIFT_CARD_ORDERS,
  UPDATE_GIFT_CARD_SLIDER,
  UPDATE_MIDDLEWARE,
  UPDATE_MISSED_DEALS,
  UPDATE_MISSING_CASHBACK,
  UPDATE_PAYMENT_REQUEST,
  UPDATE_REFERRALS,
  UPDATE_STORE,
  UPDATE_STORE_CASHBACK_RATES,
  UPDATE_STORE_REVIEWS,
  UPDATE_STORIES,
  UPDATE_SUB_CATEGORY,
  UPDATE_TRENDING_STORE,
  UPDATE_USER_DETAILS,
  UPDATE_EARNINGS,
  VIEW_ALL_ACTIONS,
  VIEW_ALL_ADMINS,
  VIEW_ALL_CASHBACK,
  VIEW_ALL_GIFT_CARDS,
  VIEW_ALL_GIFT_CARD_OFFERS,
  VIEW_ALL_GIFT_CARD_ORDERS,
  VIEW_ALL_GIFT_CARD_SLIDER,
  VIEW_ALL_MIDDLEWARES,
  VIEW_ALL_STORE_CASHBACK_RATES,
  VIEW_ALL_TRENDING_STORES,
  VIEW_ALL_USERS,
  VIEW_CATEGORIES,
  VIEW_COMMON_STORE_CASHBACK_RATES,
  VIEW_GIFT_CARD_DETAILS,
  VIEW_GIFT_CARD_OFFER_DETAILS,
  VIEW_OFFER,
  VIEW_SINGLE_CASHBACK,
  VIEW_STORE,
  VIEW_ALL_ADMINS_FILTER,
  CREATE_BANNER,
  UPDATE_BANNER,
  GET_ALL_BANNERS,
  DELETE_BANNER,
  CREATE_ONGOING_SALES,
  DELETE_ONGOING_SALES,
  UPDATE_ONGOING_SALES,
  GET_ALL_ONGOING_SALES,
  CREATE_TRENDING_OFFER,
  DELETE_TRENDING_OFFER,
  UPDATE_TRENDING_OFFER,
  VIEW_ALL_TRENDING_OFFERS,
} from "../utils/constants.js";

export class MiddlewareService {
  createMiddleware = async (payload) => {
    const middleware = await Middleware.create(payload);
    if (!middleware) {
      throw new HttpException(404, "failed to create new middleware!");
    }
    return middleware;
  };
  addNewFeatureToMiddleware = async (level, feature) => {
    const middleware = await Middleware.findOne({ level });
    if (!middleware) {
      throw new HttpException(404, "resource not found!");
    }
    await Middleware.updateMany(
      { level: { $gte: level } },
      {
        $push: { features: feature },
      }
    );
    return { id: middleware._id, feature };
  };
  removeNewFeatureFromMiddleware = async (level, feature) => {
    const middleware = await Middleware.findOne({ level });
    if (!middleware) {
      throw new HttpException(404, "resource not found!");
    }
    await Middleware.updateMany(
      { level: { $gte: level } },
      {
        $pull: { features: feature },
      }
    );
    return { id: middleware._id, feature };
  };

  updateMiddlewareStatus = async (id) => {
    const middleware = await Middleware.findById(id);
    if (!middleware) {
      throw new HttpException(404, "resource not found!");
    }
    middleware.active = !middleware.active;
    await middleware.save();
    return middleware;
  };

  updateMiddlewareName = async (id, name) => {
    const middleware = await Middleware.findById(id);
    if (!middleware) {
      throw new HttpException(404, "resource not found!");
    }
    middleware.name = name;
    await middleware.save();
    return middleware;
  };
  getAllMiddlewareActions = async () => {
    return [
      VIEW_STORE,
      CREATE_STORE,
      UPDATE_STORE,
      DELETE_STORE,
      VIEW_OFFER,
      ADD_NEW_OFFER,
      EDIT_OFFER,
      DELETE_OFFER,
      VIEW_CATEGORIES,
      ADD_NEW_CATEGORY,
      UPDATE_CATEGORY,
      DELETE_CATEGORY,
      RESTORE_CATEGORY,
      REMOVE_SUBCATEGORY,
      CREATE_SUB_CATEGORY,
      DELETE_SUB_CATEGORY,
      UPDATE_SUB_CATEGORY,
      VIEW_ALL_ADMINS,
      VIEW_ALL_ADMINS_FILTER,
      ADD_NEW_ADMIN,
      CHANGE_ADMIN_STATUS,
      BLOCK_ADMIN,
      ACCESS_ADMIN_LOGS,
      VIEW_ALL_MIDDLEWARES,
      VIEW_ALL_ACTIONS,
      CREATE_MIDDLEWARE,
      UPDATE_MIDDLEWARE,
      CREATE_AFFILIATION,
      DELETE_AFFILIATION,
      UPDATE_AFFILIATION,
      VIEW_ALL_AFFILIATIONS,
      //   CREATE_CASHBACK,
      //   VIEW_ALL_CASHBACK,
      //   VIEW_SINGLE_CASHBACK,
      //   EDIT_CASHBACK,
      //   DELETE_CASHBACK,
      CREATE_GIFT_CARD,
      VIEW_ALL_GIFT_CARDS,
      VIEW_GIFT_CARD_DETAILS,
      UPDATE_GIFT_CARD,
      DELETE_GIFT_CARD,
      CREATE_GIFT_CARD_SLIDERS,
      VIEW_ALL_GIFT_CARD_SLIDER,
      UPDATE_GIFT_CARD_SLIDER,
      DELETE_GIFT_CARD_SLIDER,
      CREATE_GIFT_CARD_OFFERS,
      VIEW_ALL_GIFT_CARD_OFFERS,
      VIEW_GIFT_CARD_OFFER_DETAILS,
      UPDATE_GIFT_CARD_OFFER,
      DELETE_GIFT_CARD_OFFER,
      VIEW_ALL_GIFT_CARD_ORDERS,
      CREATE_GIFT_CARD_ORDERS,
      UPDATE_GIFT_CARD_ORDERS,
      CREATE_STORE_CASHBACK_RATE,
      VIEW_ALL_STORE_CASHBACK_RATES,
      VIEW_COMMON_STORE_CASHBACK_RATES,
      UPDATE_STORE_CASHBACK_RATES,
      DELETE_STORE_CASHBACK_RATES,
      FILTER_STORE_CASHBACK_RATES_HISTORY,
      GET_ALL_STORE_REVIEWS,
      UPDATE_STORE_REVIEWS,
      DELETE_STORE_REVIEWS,
      VIEW_ALL_USERS,
      UPDATE_USER_DETAILS,
      CREATE_EARNINGS,
      UPDATE_EARNINGS,
      DELETE_EARNINGS,
      GET_ALL_EARNINGS,
      GET_EARNINGS_DETAILS,
      GET_APPROVED_EARNINGS,
      GET_CONFIRM_EARNINGS,
      GET_CANCEL_EARNINGS,
      GET_WITHOUT_EARNINGS,
      GET_CLICK_DETAILS,
      GET_CLICKS,
      GET_ALL_MISSING_CASHBACK,
      CREATE_MISSING_CASHBACK,
      DELETE_MISSING_CASHBACK,
      UPDATE_MISSING_CASHBACK,
      GET_MISSING_CASHBACK_DETAILS,
      UPDATE_PAYMENT_REQUEST,
      DELETE_PAYMENT_REQUEST,
      GET_PAYMENT_REQUEST_DETAILS,
      GET_ALL_PAYMENT_REQUESTS,
      GET_ALL_REFERRALS,
      UPDATE_REFERRALS,
      DELETE_REFERRALS,
      GET_REFERRAL_DETAILS,
      CREATE_COMMISSIONS,
      GET_ALL_COMMISSIONS,
      UPDATE_COMMISSIONS,
      GET_COMMISSION_DETAILS,
      DELETE_COMMISSIONS,
      CREATE_STORIES,
      UPDATE_STORIES,
      DELETE_STORIES,
      GET_ALL_STORIES,
      CREATE_TRENDING_STORE,
      DELETE_TRENDING_STORES,
      UPDATE_TRENDING_STORE,
      VIEW_ALL_TRENDING_STORES,
      CREATE_MISSED_DEALS,
      DELETE_MISSED_DEALS,
      GET_ALL_MISSED_DEALS,
      UPDATE_MISSED_DEALS,
      CREATE_BANNER,
      UPDATE_BANNER,
      GET_ALL_BANNERS,
      DELETE_BANNER,
      CREATE_ONGOING_SALES,
      DELETE_ONGOING_SALES,
      UPDATE_ONGOING_SALES,
      GET_ALL_ONGOING_SALES,
      CREATE_TRENDING_OFFER,
      VIEW_ALL_TRENDING_OFFERS,
      DELETE_TRENDING_OFFER,
      UPDATE_TRENDING_OFFER,
    ];
  };

  getAllMiddlewares = async (query) => {
    const pageSize = Number(query.pageSize) || 10;
    const page = Number(query.page) || 1;
    const sort = query.sort ? query.sort : { _id: -1 };

    const keyword = query.search
      ? {
          $or: [
            {
              name: {
                $regex: query.search,
                $options: "i",
              },
            },
          ],
        }
      : {};
    if (query.admin) {
      keyword.createdBy = query.admin;
    }
    const count = await Middleware.countDocuments({ ...keyword });
    const allMiddlewares = await Middleware.find({ ...keyword })
      .populate("createdBy", "name  count")
      .sort(sort)
      .limit(pageSize)
      .skip((page - 1) * pageSize);

    return {
      search: query.search,
      allMiddlewares,
      page,
      pageSize,
      pages: Math.ceil(count / pageSize),
    };
  };
}
