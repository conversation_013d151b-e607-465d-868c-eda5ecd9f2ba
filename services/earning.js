import moment from 'moment'
import { HttpException } from '../exceptions/httpException.js'
import { Offer } from '../models/offer/offer.js'
import { Store } from '../models/stores/store.js'
import { Earnings } from '../models/user/earnings.js'
import { User } from '../models/user/user.js'
import { sendCashbackTrackedMail } from '../utils/mailgun.js'
import { Clicks } from '../models/clicks.js'
import { convertDatesToUTC, convertLocalToUTC } from '../utils/dateFormatter.js'
import mongoose from 'mongoose'

export class EarningService {
	createEarnings = async payload => {
		try {
			console.table({
				orderUniqueId: payload.orderUniqueId,
			})
			const earnings = new Earnings({ ...payload })
			await earnings.save()
			if (!earnings) {
				throw new HttpException(404, 'Failed to create new user earning!')
			}
			return earnings
		} catch (error) {
			console.error('Error creating earnings:', error)
			// For non-critical errors, we'll log and skip
			console.warn('Non-critical error occurred, skipping:', error.message)
			return null // Return null to indicate that earning creation was skipped
		}
	}
	getEarningDetails = async id => {
		const earnings = await Earnings.findById(id)
			.populate('affiliation', 'name uid')
			.populate('store', 'name uid')
			.populate('click')
			.populate('offer')
			.populate({
				path: 'user',
				populate: { path: 'referral', select: 'name uid email' },
				select: 'name uid email ',
			})
			.populate('createdBy', 'name uid')
		if (!earnings) {
			throw new HttpException(404, 'resource not found!')
		}
		return earnings
	}

	getAllProceedWithoutEarnings = async query => {
		const pageSize = Number(query.pageSize) || 10
		const page = Number(query.page) || 1

		const keyword = query.search
			? {
					$or: [
						{
							name: {
								$regex: query.search,
								$options: 'i',
							},
						},
					],
				}
			: {}
		const count = await Earnings.countDocuments({ ...keyword })
		const allProceedWithEarnings = await Earnings.find({ ...keyword })
			.populate('affiliation', 'name uid')
			.populate('store', 'name uid')
			.populate('click')
			.populate({
				path: 'user',
				populate: { path: 'referral', select: 'name uid email' },
				select: 'name uid email ',
			})
			.limit(pageSize)
			.skip((page - 1) * pageSize)

		return {
			allProceedWithEarnings,
			page,
			pages: Math.ceil(count / pageSize),
			pageSize,
			search: query.search,
		}
	}

	getAllEarnings = async query => {
		try {
			const pageSize = Number(query.pageSize) || 10
			const page = Number(query.page) || 1

			const keyword = {}

			// Initialize keyword.$or if it doesn't exist
			keyword.$or = keyword.$or || []
			keyword.$and = keyword.$and || []

			if (query.search?.trim()) {
				const searchTerm = query.search.trim()

				// Find stores with names matching the search term
				const matchingStores = await Store.find(
					{ name: { $regex: searchTerm, $options: 'i' } },
					{ _id: 1 },
				)

				const matchingUsers = await User.find(
					{
						$or: [
							{ name: { $regex: searchTerm, $options: 'i' } },
							{ email: { $regex: searchTerm, $options: 'i' } },
						],
					},
					{ _id: 1 }, // Project only the _id field
				)

				const matchingClicks = await Clicks.find(
					{
						referenceId: { $regex: searchTerm, $options: 'i' },
					},
					{ _id: 1 }, // Project only the _id field
				)

				// Extract store and user IDs
				const storeIds = matchingStores.map(store => store._id)
				//const storeIds = matchingStores.map((store) => new mongoose.Types.ObjectId(store._id));
				const userIds = matchingUsers.map(user => user._id)
				const clickIds = matchingClicks.map(click => click._id)
				//console.log(storeIds);

				// Add store IDs to the keyword
				if (storeIds.length) {
					keyword.$or.push({ store: { $in: storeIds } })
				}

				// Add user IDs to the keyword
				if (userIds.length) {
					keyword.$or.push({ user: { $in: userIds } })
				}

				// Add click IDs to the keyword
				if (clickIds.length) {
					keyword.$or.push({ click: { $in: clickIds } })
				}

				// Check if searchTerm is a valid number string and add it to the keyword
				if (!Number.isNaN(searchTerm) && searchTerm.trim() !== '') {
					keyword.$or.push({ uid: Number.parseInt(searchTerm) }) // Direct match for a valid number
				}
			}

			const userIdSearchTerm = query.userId?.trim()
			// Check if userIdSearchTerm is a valid number string and add it to the keyword
			if (!Number.isNaN(userIdSearchTerm) && userIdSearchTerm.trim() !== '') {
				const matchingUser = await User.findOne({
					uid: Number.parseInt(userIdSearchTerm),
				})
				if (matchingUser) {
					keyword.$and.push({ user: matchingUser._id }) // Direct match for a valid number
				}
			}

			const orderUniqueIdSearchTerm = query.orderId?.trim()
			// Check if orderUniqueIdSearchTerm is a valid number string and add it to the keyword
			if (orderUniqueIdSearchTerm.trim() !== '') {
				keyword.$and.push({ orderUniqueId: orderUniqueIdSearchTerm })
			}

			if (query.remarks && query.remarks.trim() !== '') {
				keyword.$and.push({ remarks: new RegExp(query.remarks, 'i') })
			}

			if (query.advId && query.advId.trim() !== '') {
				keyword.$and.push({ advertiserInfo: query.advId })
			}

			if (query.startDate && query.endDate) {
				const { startUTC, endUTC } = convertDatesToUTC(
					query.startDate,
					query.endDate,
				)

				// Add the filter condition for confirmDate
				keyword.$and = keyword.$and || []
				keyword.$and.push({
					createdAt: { $gte: startUTC, $lte: endUTC },
				})
			}

			if (query.status && query.status !== 'all') {
				keyword.$and = keyword.$and || []

				if (query.status === 'only_past_confirm_date') {
					// Add condition for past confirm dates
					keyword.$and.push({
						confirmDate: { $lte: convertLocalToUTC() },
						status: 'pending',
					})
				} else if (query.status === 'confirmed') {
					// Check for both "tracked_for_confirm" and "confirmed"
					keyword.$and.push({
						status: { $in: ['tracked_for_confirm', 'confirmed'] },
					})
				} else if (query.status === 'cancelled') {
					// Check for both "tracked_for_cancel" and "cancelled"
					keyword.$and.push({
						status: { $in: ['tracked_for_cancel', 'cancelled'] },
					})
				} else {
					// Add condition for any other status
					keyword.$and.push({ status: query.status })
				}
			}

			if (query.affiliation) {
				keyword.$and = keyword.$and || []
				//keyword.$and.push({ affiliation: query.affiliation });
				keyword.$and.push({
					affiliation: new mongoose.Types.ObjectId(query.affiliation),
				})
			}
			if (query.store) {
				keyword.$and = keyword.$and || []
				keyword.$and.push({
					store:
						query.store.trim().length === 24
							? new mongoose.Types.ObjectId(query.store)
							: query.store,
				})
			}

			if (keyword.$or.length === 0) {
				delete keyword.$or // Remove empty $or
			}

			if (keyword.$and.length === 0) {
				delete keyword.$and // Remove empty $and
			}

			const count = await Earnings.countDocuments({ ...keyword })
			const allEarnings = await Earnings.find({ ...keyword })
				.populate('affiliation', 'name uid')
				.populate('store', 'name uid')
				.populate('click')
				.populate({
					path: 'user',
					populate: { path: 'referral', select: 'name uid email' },
					select: 'name uid email ',
				})
				.populate('offer', 'title')
				.populate('createdBy', 'name')
				.sort({ _id: -1 })
				.limit(pageSize)
				.skip((page - 1) * pageSize)

			let totalAmountGotFromPartner = 0
			let totalAmountGotPending = 0
			let totalAmountGotCancelled = 0
			let totalAmountGotConfirmed = 0
			let totalAmountToGiveToUser = 0
			let totalAmountToGivePending = 0
			let totalAmountToGiveCancelled = 0
			let totalAmountToGiveConfirmed = 0
			let statByStore = []

			if (Object.keys(keyword).length > 0) {
				const totalAmountGotSumResult = await Earnings.aggregate([
					{ $match: keyword },
					{ $group: { _id: null, totalAmount: { $sum: '$amountGot' } } },
				])

				const totalAmountToGiveSumResult = await Earnings.aggregate([
					{ $match: keyword },
					{ $group: { _id: null, totalAmount: { $sum: '$cashbackAmount' } } },
				])

				const totalAmountGotSumPendingResult = await Earnings.aggregate([
					{ $match: keyword },
					{
						$group: {
							_id: null,
							totalAmount: {
								$sum: {
									$cond: [{ $eq: ['$status', 'pending'] }, '$amountGot', 0],
								},
							},
						},
					},
				])

				const totalAmountToGiveSumPendingResult = await Earnings.aggregate([
					{ $match: keyword },
					{
						$group: {
							_id: null,
							totalAmount: {
								$sum: {
									$cond: [
										{ $in: ['$status', ['pending']] },
										'$cashbackAmount',
										0,
									],
								},
							},
						},
					},
				])

				const totalAmountGotSumCancelledResult = await Earnings.aggregate([
					{ $match: keyword },
					{
						$group: {
							_id: null,
							totalAmount: {
								$sum: {
									$cond: [
										{ $in: ['$status', ['cancelled', 'tracked_for_cancel']] },
										'$amountGot',
										0,
									],
								},
							},
						},
					},
				])

				const totalAmountToGiveSumCancelledResult = await Earnings.aggregate([
					{ $match: keyword },
					{
						$group: {
							_id: null,
							totalAmount: {
								$sum: {
									$cond: [
										{ $in: ['$status', ['cancelled', 'tracked_for_cancel']] },
										'$cashbackAmount',
										0,
									],
								},
							},
						},
					},
				])

				const totalAmountGotSumConfirmedResult = await Earnings.aggregate([
					{ $match: keyword },
					{
						$group: {
							_id: null,
							totalAmount: {
								$sum: {
									$cond: [
										{ $in: ['$status', ['confirmed', 'tracked_for_confirm']] },
										'$amountGot',
										0,
									],
								},
							},
						},
					},
				])

				const totalAmountToGiveSumConfirmedResult = await Earnings.aggregate([
					{ $match: keyword },
					{
						$group: {
							_id: null,
							totalAmount: {
								$sum: {
									$cond: [
										{ $in: ['$status', ['confirmed', 'tracked_for_confirm']] },
										'$cashbackAmount',
										0,
									],
								},
							},
						},
					},
				])

				statByStore = await Earnings.aggregate([
					{ $match: keyword },
					{
						$group: {
							_id: '$store',
							totalAmountGot: { $sum: '$amountGot' },
							count: { $sum: 1 },
							totalAmountToGive: { $sum: '$cashbackAmount' },
							totalSaleAmount: { $sum: '$saleAmount' },
							totalOrderCount: {
								$sum: {
									$cond: {
										if: { $lte: ['$orderCount', 0] },
										then: 1,
										else: '$orderCount',
									},
								},
							},
						},
					},
					{
						$lookup: {
							from: 'stores',
							localField: '_id',
							foreignField: '_id',
							as: 'storeDetails',
						},
					},
					{ $unwind: '$storeDetails' },
					{
						$project: {
							storeName: '$storeDetails.name',
							totalAmountGot: 1,
							count: 1,
							totalAmountToGive: 1,
							totalSaleAmount: 1,
							totalOrderCount: 1,
							aov: {
								$cond: {
									if: { $gt: ['$totalOrderCount', 0] },
									then: { $divide: ['$totalSaleAmount', '$totalOrderCount'] },
									else: 0,
								},
							},
						},
					},
				])

				totalAmountGotFromPartner = (
					totalAmountGotSumResult[0]?.totalAmount || 0
				).toFixed(2)
				totalAmountGotPending = (
					totalAmountGotSumPendingResult[0]?.totalAmount || 0
				).toFixed(2)
				totalAmountGotCancelled = (
					totalAmountGotSumCancelledResult[0]?.totalAmount || 0
				).toFixed(2)
				totalAmountGotConfirmed = (
					totalAmountGotSumConfirmedResult[0]?.totalAmount || 0
				).toFixed(2)
				totalAmountToGiveToUser = (
					totalAmountToGiveSumResult[0]?.totalAmount || 0
				).toFixed(2)
				totalAmountToGivePending = (
					totalAmountToGiveSumPendingResult[0]?.totalAmount || 0
				).toFixed(2)
				totalAmountToGiveCancelled = (
					totalAmountToGiveSumCancelledResult[0]?.totalAmount || 0
				).toFixed(2)
				totalAmountToGiveConfirmed = (
					totalAmountToGiveSumConfirmedResult[0]?.totalAmount || 0
				).toFixed(2)
			}

			return {
				allEarnings,
				page,
				pages: Math.ceil(count / pageSize),
				pageSize,
				search: query.search,
				totalAmountGotFromPartner: totalAmountGotFromPartner,
				totalAmountGotPending: totalAmountGotPending,
				totalAmountGotCancelled: totalAmountGotCancelled,
				totalAmountGotConfirmed: totalAmountGotConfirmed,
				totalAmountToGiveToUser: totalAmountToGiveToUser,
				totalAmountToGivePending: totalAmountToGivePending,
				totalAmountToGiveCancelled: totalAmountToGiveCancelled,
				totalAmountToGiveConfirmed: totalAmountToGiveConfirmed,
				statByStore: statByStore,
			}
		} catch (error) {
			console.log('🚀 ~ EarningService ~ getAllEarnings= ~ error:', error)
			throw new HttpException(500, 'Internal server error')
		}
	}

	getAllAutoTrackedEarnings = async query => {
		const pageSize = Number(query.pageSize) || 10
		const page = Number(query.page) || 1

		const keyword = { autoUpdated: true } // Add the autoUpdated filter
		if (query.status) {
			keyword.status = query.status
		}
		if (query.store) {
			keyword.store = query.store
		}
		if (query.user) {
			keyword.user = query.user
		}
		if (query.affiliation) {
			keyword.affiliation = query.affiliation
		}

		const count = await Earnings.countDocuments({ ...keyword })
		const allEarnings = await Earnings.find({ ...keyword })
			.populate('affiliation', 'name uid')
			.populate('store', 'name uid')
			.populate('click')
			.populate({
				path: 'user',
				populate: { path: 'referral', select: 'name uid email' },
				select: 'name uid email ',
			})
			.populate('offer', 'title')
			.populate('createdBy', 'name')
			.sort({ _id: -1 })
			.limit(pageSize)
			.skip((page - 1) * pageSize)

		return {
			allEarnings,
			page,
			pages: Math.ceil(count / pageSize),
			pageSize,
			search: query.search,
		}
	}

	getAllPreApprovedEarnings = async query => {
		const page = query.page ? Number.parseInt(query.page) : 1 // Changed from ternary with 2
		const pageSize = query.pageSize ? Number.parseInt(query.pageSize) : 10
		const skip = (page - 1) * pageSize

		//using keyword filters for calculation - later change to pipe line
		const keyword = {}

		// Initialize keyword.$or if it doesn't exist
		keyword.$or = keyword.$or || []
		keyword.$and = keyword.$and || []

		if (query.search?.trim()) {
			const searchTerm = query.search.trim()

			// Find stores with names matching the search term
			const matchingStores = await Store.find(
				{ name: { $regex: searchTerm, $options: 'i' } },
				{ _id: 1 },
			)

			const matchingUsers = await User.find(
				{
					$or: [
						{ name: { $regex: searchTerm, $options: 'i' } },
						{ email: { $regex: searchTerm, $options: 'i' } },
					],
				},
				{ _id: 1 }, // Project only the _id field
			)

			const matchingClicks = await Clicks.find(
				{
					referenceId: { $regex: searchTerm, $options: 'i' },
				},
				{ _id: 1 }, // Project only the _id field
			)

			// Extract store and user IDs
			const storeIds = matchingStores.map(store => store._id)
			//const storeIds = matchingStores.map((store) => new mongoose.Types.ObjectId(store._id));
			const userIds = matchingUsers.map(user => user._id)
			const clickIds = matchingClicks.map(click => click._id)
			//console.log(storeIds);

			// Add store IDs to the keyword
			if (storeIds.length) {
				keyword.$or.push({ store: { $in: storeIds } })
			}

			// Add user IDs to the keyword
			if (userIds.length) {
				keyword.$or.push({ user: { $in: userIds } })
			}

			// Add click IDs to the keyword
			if (clickIds.length) {
				keyword.$or.push({ click: { $in: clickIds } })
			}

			// Check if searchTerm is a valid number string and add it to the keyword
			if (!Number.isNaN(searchTerm) && searchTerm.trim() !== '') {
				keyword.$or.push({ uid: Number.parseInt(searchTerm) }) // Direct match for a valid number
			}
		}

		const userIdSearchTerm = query.userId?.trim()
		// Check if userIdSearchTerm is a valid number string and add it to the keyword
		if (!Number.isNaN(userIdSearchTerm) && userIdSearchTerm.trim() !== '') {
			const matchingUser = await User.findOne({
				uid: Number.parseInt(userIdSearchTerm),
			})
			if (matchingUser) {
				keyword.$and.push({ user: matchingUser._id }) // Direct match for a valid number
			}
		}

		const orderUniqueIdSearchTerm = query.orderId?.trim()
		// Check if orderUniqueIdSearchTerm is a valid number string and add it to the keyword
		if (orderUniqueIdSearchTerm.trim() !== '') {
			keyword.$and.push({ orderUniqueId: orderUniqueIdSearchTerm })
		}

		if (query.remarks && query.remarks.trim() !== '') {
			keyword.$and.push({ remarks: new RegExp(query.remarks, 'i') })
		}

		if (query.advId && query.advId.trim() !== '') {
			keyword.$and.push({ advertiserInfo: query.advId })
		}

		if (query.startDate && query.endDate) {
			const { startUTC, endUTC } = convertDatesToUTC(
				query.startDate,
				query.endDate,
			)

			// Add the filter condition for confirmDate
			keyword.$and = keyword.$and || []
			keyword.$and.push({
				createdAt: { $gte: startUTC, $lte: endUTC },
			})
		}

		if (query.status && query.status !== 'all') {
			keyword.$and = keyword.$and || []

			if (query.status === 'only_past_confirm_date') {
				// Add condition for past confirm dates
				keyword.$and.push({
					confirmDate: { $lte: convertLocalToUTC() },
					status: 'pending',
				})
			} else if (query.status === 'confirmed') {
				// Check for both "tracked_for_confirm" and "confirmed"
				keyword.$and.push({
					status: { $in: ['tracked_for_confirm', 'confirmed'] },
				})
			} else if (query.status === 'cancelled') {
				// Check for both "tracked_for_cancel" and "cancelled"
				keyword.$and.push({
					status: { $in: ['tracked_for_cancel', 'cancelled'] },
				})
			} else {
				// Add condition for any other status
				keyword.$and.push({ status: query.status })
			}
		}

		if (query.affiliation) {
			keyword.$and = keyword.$and || []
			//keyword.$and.push({ affiliation: query.affiliation });
			keyword.$and.push({
				affiliation: new mongoose.Types.ObjectId(query.affiliation),
			})
		}
		if (query.store) {
			keyword.$and = keyword.$and || []
			keyword.$and.push({
				store:
					query.store.trim().length === 24
						? new mongoose.Types.ObjectId(query.store)
						: query.store,
			})
		}

		if (keyword.$or.length === 0) {
			delete keyword.$or // Remove empty $or
		}

		if (keyword.$and.length === 0) {
			delete keyword.$and // Remove empty $and
		}

		const status = query.status ? [query.status] : []

		let aggregatePipeline = []

		aggregatePipeline = [
			{
				$lookup: {
					from: 'stores',
					localField: 'store',
					foreignField: '_id',
					as: 'store',
				},
			},
			{
				$unwind: '$store',
			},
			{
				$lookup: {
					from: 'users',
					localField: 'user',
					foreignField: '_id',
					as: 'user',
				},
			},
			{
				$unwind: '$user',
			},

			// Store filter if exists
			...(query.store
				? [
						{
							$match: {
								'store._id':
									query.store.trim().length === 24
										? new mongoose.Types.ObjectId(query.store)
										: query.store,
							},
						},
					]
				: []),

			// Affiliation filter if exists
			...(query.affiliation
				? [
						{
							$match: {
								affiliation:
									query.affiliation.trim().length === 24
										? new mongoose.Types.ObjectId(query.affiliation)
										: query.affiliation,
							},
						},
					]
				: []),

			//remarks filter if exists
			...(query.remarks
				? [
						{
							$match: {
								remarks: new RegExp(query.remarks, 'i'),
							},
						},
					]
				: []),

			{
				$lookup: {
					from: 'clicks',
					localField: 'click',
					foreignField: '_id',
					as: 'click',
				},
			},
			{
				$unwind: {
					path: '$click',
					preserveNullAndEmptyArrays: true, // This preserves documents without clicks
				},
			}, // Then add search match if search term exists

			...(query.search?.trim()
				? [
						{
							$match: {
								$or: [
									// Search in store name
									{
										'store.name': {
											$regex: query.search.trim(),
											$options: 'i',
										},
									},

									// Search in click userIp (only if click exists)
									...(query.search.trim()
										? [
												{
													$and: [
														{ click: { $ne: null } }, // Only if click exists
														{
															'click.referenceId': {
																$regex: query.search.trim(),
																$options: 'i',
															},
														},
													],
												},
											]
										: []),

									// Search in user name and email
									{
										'user.name': { $regex: query.search.trim(), $options: 'i' },
									},
									{
										'user.email': {
											$regex: query.search.trim(),
											$options: 'i',
										},
									},
									// Search in numeric fields if search term is a number
									...(Number.isNaN(query.search.trim())
										? []
										: [
												//{ uid: parseInt(query.search.trim()) },
												{ 'store.uid': Number.parseInt(query.search.trim()) },
											]),
								],
							},
						},
					]
				: []),

			// Add date range match if start and end dates exist
			...(query.startDate && query.endDate
				? [
						{
							$match: {
								createdAt: {
									$gte: convertDatesToUTC(query.startDate, query.endDate)
										.startUTC,
									$lte: convertDatesToUTC(query.startDate, query.endDate)
										.endUTC,
								},
							},
						},
					]
				: []),

			// Add separate match for userId if it exists
			...(query.userId
				? [
						{
							$match: {
								'user.uid': Number.parseInt(query.userId),
							},
						},
					]
				: []),

			...(query.orderId
				? [
						{
							$match: {
								orderUniqueId: query.orderId.trim(),
							},
						},
					]
				: []),

			...(query.advId
				? [
						{
							$match: {
								advertiserInfo: query.advId.trim(),
							},
						},
					]
				: []),

			//
			{
				$group: {
					_id: {
						user: '$user._id',
						userId: '$user.uid',
						userName: '$user.name',
						store: '$store._id',
						storeId: '$store.uid',
						storeName: '$store.name',
					},
					earningIds: { $push: '$_id' },
					totalSaleAmount: { $sum: '$saleAmount' },
					totalAmountGot: { $sum: '$amountGot' },
					totalCashbackAmount: { $sum: '$cashbackAmount' },
					earningsCount: { $sum: 1 },
					lastCreatedAt: { $max: '$createdAt' }, // Get the most recent createdAt
				},
			},
			{
				$group: {
					_id: {
						_id: '$_id.user',
						name: '$_id.userName',
						uid: '$_id.userId',
					},
					stores: {
						$push: {
							store: '$_id.store',
							storeId: '$_id.storeId',
							storeName: '$_id.storeName',
							earningsDetails: {
								earningIds: '$earningIds',
								earningsCount: '$earningsCount',
								saleAmount: '$totalSaleAmount',
								amountGot: '$totalAmountGot',
								cashbackAmount: '$totalCashbackAmount',
							},
						},
					},
					lastCreatedAt: { $max: '$lastCreatedAt' }, // Keep track of the most recent date
				},
			},
			{
				$project: {
					_id: 0,
					user: '$_id',
					stores: 1,
					lastCreatedAt: 1,
				},
			},
			{
				$sort: { lastCreatedAt: -1 }, // Sort by most recent first
			},
			{
				$facet: {
					metadata: [{ $count: 'total' }],
					data: [{ $skip: skip }, { $limit: pageSize }],
				},
			},
		]
		if (query.status && query.status !== 'all') {
			aggregatePipeline.unshift({
				$match: {
					status: { $in: status },
				},
			})
		}

		const result = await Earnings.aggregate(aggregatePipeline)

		const allEarnings = result[0].data
		const total = result[0].metadata[0]?.total || 0
		const totalPages = Math.ceil(total / pageSize)

		let totalAmountGotConfirmed = 0
		let totalAmountToGiveConfirmed = 0
		let statByStore = []

		if (Object.keys(keyword).length > 0) {
			const totalAmountGotSumConfirmedResult = await Earnings.aggregate([
				{ $match: keyword },
				{
					$group: {
						_id: null,
						totalAmount: {
							$sum: {
								$cond: [
									{ $in: ['$status', ['confirmed', 'tracked_for_confirm']] },
									'$amountGot',
									0,
								],
							},
						},
					},
				},
			])

			const totalAmountToGiveSumConfirmedResult = await Earnings.aggregate([
				{ $match: keyword },
				{
					$group: {
						_id: null,
						totalAmount: {
							$sum: {
								$cond: [
									{ $in: ['$status', ['confirmed', 'tracked_for_confirm']] },
									'$cashbackAmount',
									0,
								],
							},
						},
					},
				},
			])

			statByStore = await Earnings.aggregate([
				{ $match: keyword },
				{
					$group: {
						_id: '$store',
						totalAmountGot: { $sum: '$amountGot' },
						count: { $sum: 1 },
						totalAmountToGive: { $sum: '$cashbackAmount' },
						totalSaleAmount: { $sum: '$saleAmount' },
						totalOrderCount: {
							$sum: {
								$cond: {
									if: { $lte: ['$orderCount', 0] },
									then: 1,
									else: '$orderCount',
								},
							},
						},
					},
				},
				{
					$lookup: {
						from: 'stores',
						localField: '_id',
						foreignField: '_id',
						as: 'storeDetails',
					},
				},
				{ $unwind: '$storeDetails' },
				{
					$project: {
						storeName: '$storeDetails.name',
						totalAmountGot: 1,
						count: 1,
						totalAmountToGive: 1,
						totalSaleAmount: 1,
						totalOrderCount: 1,
						aov: {
							$cond: {
								if: { $gt: ['$totalOrderCount', 0] },
								then: { $divide: ['$totalSaleAmount', '$totalOrderCount'] },
								else: 0,
							},
						},
					},
				},
				{
					$sort: { aov: -1 }, // Sorts by sales in descending order
				},
			])

			totalAmountGotConfirmed = (
				totalAmountGotSumConfirmedResult[0]?.totalAmount || 0
			).toFixed(2)

			totalAmountToGiveConfirmed = (
				totalAmountToGiveSumConfirmedResult[0]?.totalAmount || 0
			).toFixed(2)
		}

		return {
			allEarnings,
			page,
			pages: totalPages,
			pageSize,
			search: '',
			totalAmountGotConfirmed: totalAmountGotConfirmed,
			totalAmountToGiveConfirmed: totalAmountToGiveConfirmed,
			statByStore: statByStore,
		}
	}

	updateEarnings = async (id, body) => {
		const earnings = await Earnings.findById(id)
		if (!earnings) {
			throw new HttpException(404, 'resource not found!')
		}

		// Create a new object with modified values
		const updatedBody = { ...body }

		// Remove click field if it's empty string or doesn't exist
		if (!body.click || body.click === '') {
			updatedBody.click = undefined
		}

		await Earnings.updateOne({ _id: id }, { $set: updatedBody })
		return earnings
	}

	updateEarningStatus = async (id, status, adminId) => {
		const earnings = await Earnings.findById(id)
		if (!earnings) {
			throw new HttpException(404, 'resource not found!')
		}
		await Earnings.updateOne(
			{ _id: id },
			{
				$set: {
					updatedBy: adminId,
					status,
					autoUpdated: false,
				},
			},
		)

		return earnings
	}

	deleteEarnings = async earningId => {
		const earning = await Earnings.findById(earningId)
		if (!earning) {
			throw new HttpException(404, 'resource not fund!')
		}
		await Earnings.deleteOne({ _id: earningId })
		return true
	}

	confirmEarnings = async (id, adminId) => {
		const earnings = await Earnings.findById(id)
		if (!earnings) {
			throw new HttpException(404, 'resource not found!')
		}
		earnings.status = 'confirmed'
		earnings.updatedBy = adminId
		earnings.autoUpdated = false
		await earnings.save()
		return earnings
	}

	cancelEarnings = async (id, adminId) => {
		const earnings = await Earnings.findById(id)
		if (!earnings) {
			throw new HttpException(404, 'resource not found!')
		}
		earnings.status = 'cancelled'
		earnings.dateConfirmedCancelled = new Date()
		earnings.updatedBy = adminId
		earnings.autoUpdated = false
		await earnings.save()
		return earnings
	}

	addPendingEarnings = async id => {
		const earnings = await Earnings.findById(id)
		if (!earnings) {
			throw new HttpException(404, 'resource not found!')
		}
		earnings.active = false
		await earnings.save()
		return earnings
	}
	getAllEarningsToApprove = async query => {
		const pageSize = Number(query.pageSize) || 10
		const page = Number(query.page) || 1

		const count = await Earnings.countDocuments({
			toApprove: false,
			active: true,
		})
		const earnings = await Earnings.find({ toApprove: false, active: true })
			.sort({ createdAt: -1 })
			.populate('store', 'name uid')
			.populate('user', 'name uid email')
			.populate('affiliation', 'name uid')
			.populate('createdBy', 'name uid')

		function mergeDuplicates(arr) {
			const mergedMap = {}
			arr.for(obj => {
				const key = obj.userId + obj.storeId

				if (mergedMap[key]) {
					// Merge with existing object
					Object.assign(mergedMap[key].mergedData, obj)
					mergedMap[key].mergedCount++
					mergedMap[key].extraId.push(obj._id)
				} else {
					// Create a new merged object
					mergedMap[key] = {
						mergedData: { ...obj._doc },
						mergedCount: 1,
						extraId: [obj._id],
					}
				}
			})

			const mergedArray = Object.values(mergedMap)
			const pages = Math.ceil(count / pageSize)
			return { page, pages, pageSize, earnings: mergedArray }
		}
		const mergedArray = mergeDuplicates(earnings)

		const pages = Math.ceil(count / pageSize)
		return { page, pages, pageSize, earnings: mergedArray }
	}

	updateToApproveEarnings = async earningIds => {
		const earnings = await Earnings.find({ _id: earningIds }, 'count')
		await Earnings.updateMany(
			{ _id: earningIds },
			{ $set: { toApprove: true } },
		)
		return earnings
	}

	updateEarningsConfirmStatus = async id => {
		const earnings = await Earnings.findById(id)
		if (!earnings) {
			throw new HttpException(404, 'resource not found!')
		}
		earnings.checkedToConfirm = false
		earnings.autoUpdated = false
		earnings.confirmDate = null
		await earnings.save()
		return earnings
	}
	updateEarningsCancelStatus = async id => {
		const earnings = await Earnings.findById(id)
		if (!earnings) {
			throw new HttpException(404, 'resource not found!')
		}
		earnings.checkedToCancel = false
		earnings.autoUpdated = false
		earnings.dateConfirmedCancelled = null
		await earnings.save()
		return earnings
	}

	getEarningByOrderIdAndStore = async (orderId, storeId) => {
		const earnings = await Earnings.findOne({
			orderUniqueId: orderId,
			store: storeId,
		})
		if (!earnings) {
			return null
		}

		return earnings
	}

	getEarningByOrderId = async orderId => {
		const earnings = await Earnings.findOne({ orderUniqueId: orderId })
		if (!earnings) {
			return null
		}

		return earnings
	}

	getEarningByClickId = async clickId => {
		const earnings = await Earnings.findOne({ referenceId: clickId })
		if (!earnings) {
			return null
		}

		return earnings
	}

	getEarningById = async earningId => {
		const earnings = await Earnings.findById(earningId)
		if (!earnings) {
			return null
		}

		return earnings
	}

	processEarningsAndSendMail = async earning => {
		try {
			// Fetch trending offers with a limit of 3
			const trendingOffers = await Offer.find({
				trending: true,
				dateExpiry: { $gt: new Date() },
				active: true,
			}) // Filter expired offers
				.limit(3) // Limit to 3 results
				.populate('store')
				.sort({ priority: -1, dateExpiry: -1 })
				.exec()

			const formattedTrendingOffers = trendingOffers.map(offer => {
				return {
					title: offer?.title,
					storeName: offer?.store?.name,
					offerAmount: offer?.itemPrice - offer?.discount,
					endsIn: `${moment
						.duration(moment(offer?.dateExpiry).diff(moment()))
						.days()} ${
						moment.duration(moment(offer?.dateExpiry).diff(moment())).days() > 1
							? 'Days'
							: 'Day'
					} ${moment
						.duration(moment(offer?.dateExpiry).diff(moment()))
						.hours()} Hour`,
					offerCaption: offer?.offer,
					offerUid: offer?.uid,
					offerImage: offer?.productImage?.secureUrl,
				}
			})

			// Fetch trending stores with a limit of 3
			const trendingStores = await Store.find({
				trending: true,
				active: true,
			})
				.limit(3) // Limit to 3 results
				.sort({ priority: -1 })
				.exec()

			const formattedTrendingStores = trendingStores.map(store => {
				return {
					name: store?.name,
					storeImage: store?.logo?.secureUrl,
					storeOffer: store?.storeOffer,
				}
			})

			const user = await User.findById(earning?.user).select(
				'email name referralCode',
			)

			const store = await Store.findById(earning?.store).select('name logo')

			const mailPayload = {
				email: user?.email,
				name: user?.name,
				referralCode: user?.referralCode,
				referenceId: earning?.referenceId,
				date: moment(earning?.createdAt).format('D MMMM, YYYY'),
				cashback:
					earning?.cashbackAmount === 0
						? 'To be updated'
						: earning?.cashbackAmount,
				status: 'Tracked',
				confirmDate: moment(earning?.confirmDate).format('D MMMM, YYYY'),
				remarks: earning?.remarks ?? '',
				orderAmount: earning?.saleAmount,
				storeName: store?.name,
				storeLogo: store?.logo?.secureUrl,
				trendingOffers: formattedTrendingOffers,
				trendingStores: formattedTrendingStores,
				referralUrl: `https://${process.env.APP_URL}?r=${user?.referralCode}`,
			}

			//for now
			await sendCashbackTrackedMail(mailPayload)
		} catch (error) {
			console.error('Error processing earnings:', error)
		}
	}

	getTotalAmountWithStatusAndUser = async ({
		status,
		userId,
		rewardPoint = false,
	}) => {
		try {
			// Build match query dynamically
			const matchQuery = {
				status,
				user: new mongoose.Types.ObjectId(userId),
			}

			if (rewardPoint) {
				matchQuery.rewardPoint = true // Only add this condition if rewardPoint is true
			}

			const result = await Earnings.aggregate([
				{
					$match: matchQuery, // Dynamically filtered match
				},
				{
					$group: {
						_id: null, // No grouping key, since we only need the total
						totalAmount: { $sum: '$cashbackAmount' },
					},
				},
			])

			return result[0]?.totalAmount || 0
		} catch (error) {
			console.error(error)
			return 0
		}
	}

	//  getTotlaAmountWithStatusAndUser = async ({ status, userId, rewardPoint = false }) => {
	//    try {
	//      const result = await Earnings.aggregate([
	//        {
	//          $match: {
	//            status,
	//            user: new mongoose.Types.ObjectId(userId),
	//          } // Filter by the given status
	//        },
	//        {
	//          $group: {
	//            _id: null, // No grouping key, since we only need the total
	//            totalAmount: { $sum: "$cashbackAmount" }
	//          }
	//        }
	//      ]);
	//      console.log(result, "result");
	//
	//      const totalAmount = result[0]?.totalAmount || 0;
	//      return totalAmount;
	//
	//    } catch (error) {
	//      return 0;
	//    }
	//  };
	//
	//
	//
	//
	//
	//
	//
}
