import { HttpException } from "../exceptions/httpException.js";
import { Category } from "../models/categories/category.js";
import { SubCategory } from "../models/categories/subCategory.js";

export class CategoryService {
  createCategory = async (payload) => {
    const category = await Category.create(payload);
    if (!category) {
      throw new HttpException(404, "filed to create new category");
    }
    return category;
  };

  getCategoryDetails = async (id) => {
    const category = await Category.findById(id)
      .populate("createdBy", "name uid")
      .populate("subCategories", "name uid");

    if (!category) {
      throw new HttpException(404, "resource not found!");
    }
    return category;
  };

  getAllCategories = async (query) => {
    const pageSize = Number(query.pageSize) || 10;
    const page = Number(query.page) || 1;
    const sort = query.sort ? query.sort : { _id: -1 };

    const status = query.status ? query.status : "all";

    const keyword = query.search
      ? {
          $or: [
            {
              name: {
                $regex: query.search,
                $options: "i",
              },
            },
          ],
        }
      : {};

    const statusFilter = status === "top" ? { isTop: true } : {};

    const filter = { ...keyword, ...statusFilter };

    const count = await Category.countDocuments(filter);
    const allCategories = await Category.find(filter)
      .populate("createdBy", "name")
      .sort(sort)
      .limit(pageSize)
      .skip((page - 1) * pageSize);
    return {
      search: query.search,
      allCategories,
      page,
      pageSize,
      pages: Math.ceil(count / pageSize),
    };
  };

  getAllCategoriesList = async () => {
    const allCategories = await Category.find({}, "name  uid").sort({
      _id: -1,
    });
    const categoriesList = [];
    for (const category of allCategories) {
      const subCategories = await SubCategory.find(
        { category: category.id },
        "name  uid"
      );
      categoriesList.push({
        ...category._doc,
        subCategories,
      });
      // category.subCategories = subCategories
    }
    return categoriesList;
  };
  updateCategoryTrendingStatus = async (id) => {
    const category = await Category.findById(id);
    if (!category) {
      throw new HttpException(404, "resource not found!");
    }
    category.trending = !category.trending;
    await category.save();
    return category;
  };

  updateCategoryTopStatus = async (id) => {
    const category = await Category.findById(id);
    if (!category) {
      throw new HttpException(404, "resource not found!");
    }
    category.isTop = !category.isTop;
    await category.save();
    return category;
  };

  updateCategoryTrendingPriority = async (id, priority) => {
    const category = await Category.findById(id);
    if (!category) {
      throw new HttpException(404, "resource not found!");
    }
    category.trendingPriority = priority;
    await category.save();
    return category;
  };

  updateCategoryPriority = async (id, priority) => {
    const category = await Category.findById(id);
    if (!category) {
      throw new HttpException(404, "resource not found!");
    }
    category.priority = priority;
    await category.save();
    return category;
  };

  updateCategory = async (id, body) => {
    const category = await Category.findById(id);
    if (!category) {
      throw new HttpException(404, "resource not found!");
    }
    await Category.updateOne({ _id: id }, { $set: body });
    return category;
  };

  removeSubcategory = async (id, subCategoryIds) => {
    const category = await Category.findById(id);
    if (!category) {
      throw new HttpException(404, "resource not found!");
    }
    await Category.updateOne(
      { _id: id },
      { $pull: { subCategories: { $in: subCategoryIds } } }
    );
    return category;
  };

  addSubCategories = async (id, subCategoryId) => {
    const category = await Category.findById(id);
    if (!category) {
      throw new HttpException(404, "resource not found!");
    }
    await Category.updateOne(
      { _id: id },
      { $addToSet: { subCategories: subCategoryId } }
    );
    return category;
  };

  deleteCategory = async (id) => {
    const category = await Category.findById(id);
    if (!category) {
      throw new HttpException(404, "resource not found!");
    }

    await Category.findByIdAndDelete(id);

    return category;
  };
}
