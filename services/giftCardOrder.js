import { HttpException } from "../exceptions/httpException.js";
import { GiftCardOrder } from "../models/giftCards/orders.js";

export class GiftCardOrderService {
  createGiftCardOrder = async (payload) => {
    const giftCardOrder = await GiftCardOrder.create(payload);
    if (!giftCardOrder) {
      throw new HttpException(404, "filed to create new gift card order");
    }
    return giftCardOrder;
  };
  getGiftCardOrderDetails = async (id) => {
    const giftCardOrder = await GiftCardOrder.findById(id);
    if (!giftCardOrder) {
      throw new HttpException(404, "resource not found");
    }
    return giftCardOrder;
  };
  getAllGiftCardOrders = async (query) => {
    const pageSize = Number(query.pageSize) || 5;
    const page = Number(query.page) || 1;
    const sort = query.sort ? query.sort : { _id: -1 };

    const keyword = query.search
      ? {
          $or: [
            {
              name: {
                $regex: query.search,
                $options: "i",
              },
            },
          ],
        }
      : {};
    if (query.admin) {
      keyword.createdBy = query.admin;
    }
    if (query.status) {
      keyword.status = query.status;
    }
    const count = await GiftCardOrder.countDocuments({ ...keyword });
    const allGiftCardOrders = await GiftCardOrder.find({ ...keyword })
      .populate("updatedBy", "name active")
      .populate("user", "name uid email phone active")
      .populate("giftCard")
      // .populate("paymentSession")
      .sort(sort)
      .limit(pageSize)
      .skip((page - 1) * pageSize);

    return {
      search: query.search,
      allGiftCardOrders,
      page,
      pageSize,
      pages: Math.ceil(count / pageSize),
    };
  };
  updateGiftCardOrder = async (id, body) => {
    const giftCardOrder = await GiftCardOrder.findById(id);
    if (!giftCardOrder) {
      throw new HttpException(404, "resource not found");
    }
    await GiftCardOrder.updateOne({ _id: id }, { $set: body });
    return giftCardOrder;
  };
}
