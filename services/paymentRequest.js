import { HttpException } from "../exceptions/httpException.js";
import { BankAccountDetails } from "../models/payments/bankAccounts.js";
import { PaymentRequest } from "../models/payments/paymentRequest.js";
import { User } from "../models/user/user.js";
import { convertLocalToUTC } from "../utils/dateFormatter.js";

export class PaymentRequestService {
  getAllPaymentRequests = async (query) => {
    const pageSize = Number(query.pageSize) || 10;
    const page = Number(query.page) || 1;
    // const sort = query.amount === -1 ? { _id: -1 } : {};
    const sort = query.amount === -1 ? { _id: -1 } : { createdAt: -1 }; // Default sort by latest

    const keyword = {};

    if (query.startDate) {
      keyword.createdAt = {
        $gte: new Date(query.startDate),
        $lte: new Date(query.endDate ? query.endDate : new Date()),
      };
    }
    if (query.amount === 1) {
      sort.withdrawAmount = 1;
      sort.createdAt = undefined;
    }
    if (query.amount === 0) {
      sort.withdrawAmount = -1;
      sort.createdAt = undefined;
    }

    console.log(query.status, "query.status");
    // Add status filter
    if (query.status) {
      console.log("went here");
      keyword.status = query.status;
    }

    const count = await PaymentRequest.countDocuments({ ...keyword });
    const allPaymentRequests = await PaymentRequest.find({ ...keyword })
      .populate("withdrawer", "name email phone uid address")
      .sort({ ...sort })
      .limit(pageSize)
      .skip((page - 1) * pageSize);

    // Fetch bank details for each withdrawer
    const allPaymentRequestsWithBankDetails = await Promise.all(
      allPaymentRequests.map(async (request) => {
        const bankDetails = await BankAccountDetails.findOne({
          user: request.withdrawer._id,
          active: true, // Fetch only active bank details
        }).select("holderName bankName branchName accountNumber ifsc upi");

        return {
          ...request._doc,
          bankDetails: bankDetails || null, // Attach bank details or null if not found
        };
      })
    );

    return {
      search: query.search,
      allPaymentRequests: allPaymentRequestsWithBankDetails,
      page,
      pageSize,
      pages: Math.ceil(count / pageSize),
    };
  };
  getPaymentRequestDetails = async (id) => {
    const paymentRequest = await PaymentRequest.findById(id);
    if (!paymentRequest) {
      throw new HttpException(404, "resource not found ");
    }
    return paymentRequest;
  };

  updatePaymentRequest = async (id, body) => {
    const paymentRequest = await PaymentRequest.findById(id);
    if (!paymentRequest) {
      throw new HttpException(404, "resource not found ");
    }
    await PaymentRequest.updateOne({ _id: id }, { $set: body });

    return paymentRequest;
  };

  approvePaymentRequest = async (id, adminId) => {
    const paymentRequest = await PaymentRequest.findById(id).populate(
      "withdrawer",
      "name uid balance"
    );

    if (!paymentRequest) {
      throw new HttpException(404, "resource not found ");
    }
    if (paymentRequest.status === "approved") {
      throw new HttpException(400, "Payment request is already approved");
    }

    // If previously rejected, add back the withdraw amount to user's balance
    if (paymentRequest.status === "rejected") {
      const user = await User.findById(paymentRequest.withdrawer._id);
      user.balance -= paymentRequest.withdrawAmount;
      await user.save();
    }

    paymentRequest.status = "approved";
    paymentRequest.withdrawDate = convertLocalToUTC();
    paymentRequest.updatedBy = adminId;
    await paymentRequest.save();

    return paymentRequest;
  };

  rejectPaymentRequest = async (id, adminId) => {
    const paymentRequest = await PaymentRequest.findById(id).populate(
      "withdrawer",
      "name uid balance"
    );

    if (!paymentRequest) {
      throw new HttpException(404, "resource not found ");
    }

    if (paymentRequest.status === "rejected") {
      throw new HttpException(400, "Payment request is already rejected");
    }

    paymentRequest.status = "rejected";
    paymentRequest.updatedBy = adminId;
    paymentRequest.withdrawDate = convertLocalToUTC();
    await paymentRequest.save();

    return paymentRequest;
  };

  deletePaymentRequest = async (id) => {
    const paymentRequest = await PaymentRequest.findById(id);
    if (!paymentRequest) {
      throw new HttpException(404, "resource not found ");
    }
    paymentRequest.active = !paymentRequest.active;
    await paymentRequest.save();
    return paymentRequest;
  };
}
