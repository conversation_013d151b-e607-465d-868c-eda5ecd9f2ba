import { HttpException } from '../exceptions/httpException.js'
import { MobileStory } from '../models/banners/mobileStory.js'

export class MobileStoryService {
	createMobileStory = async payload => {
		const mobileStory = await MobileStory.create(payload)
		if (!mobileStory) {
			throw new HttpException(404, 'failed to create new mobile story')
		}
		return mobileStory
	}

	getAllMobileStories = async query => {
		const pageSize = Number(query.pageSize) || 5
		const page = Number(query.page) || 1
		const sort = query.sort ? query.sort : { _id: -1 }

		const keyword = {}
		if (query.store) {
			keyword.store = query.store
		}
		const count = await MobileStory.countDocuments({ ...keyword })
		const allMobileStories = await MobileStory.find({ ...keyword })
			.populate('store', 'name logo ')
			.populate('createdBy', 'name  ')
			.sort(sort)
			.limit(pageSize)
			.skip((page - 1) * pageSize)

		return {
			search: query.search,
			allMobileStories,
			page,
			pageSize,
			pages: Math.ceil(count / pageSize),
		}
	}

	getStoryDetails = async id => {
		const mobileStory = await MobileStory.findById(id).populate(
			'store',
			'name logo ',
		)
		if (!mobileStory) {
			throw new HttpException(404, 'resource not found')
		}
		return mobileStory
	}

	updateMobileStory = async (id, payload) => {
		const mobileStory = await MobileStory.findById(id)
		if (!mobileStory) {
			throw new HttpException(404, 'resource not found')
		}
		await MobileStory.updateOne({ _id: id }, { $set: payload })
		return mobileStory
	}

	updateMobileStoryActiveStatus = async id => {
		const mobileStory = await MobileStory.findById(id)
		if (!mobileStory) {
			throw new HttpException(404, 'resource not found')
		}
		mobileStory.isActive = !mobileStory.isActive
		await mobileStory.save()
		return mobileStory
	}

	deleteMobileStory = async id => {
		const mobileStory = await MobileStory.findById(id)
		if (!mobileStory) {
			throw new HttpException(404, 'resource not found')
		}
		await MobileStory.deleteOne({ _id: id })
		return true
	}
}
