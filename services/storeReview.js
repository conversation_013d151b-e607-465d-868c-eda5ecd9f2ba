import { HttpException } from "../exceptions/httpException.js";
import { StoreReviews } from "../models/stores/reviews.js";

export class StoreReviewService {
  createStoreReview = async (payload) => {
    const storeReview = await StoreReviews.create(payload);
    if (!storeReview) {
      throw new HttpException(404, "filed to create new store review");
    }
    return storeReview;
  };
  getAllStoreReviews = async (query) => {
    const pageSize = Number(query.pageSize) || 5;
    const page = Number(query.page) || 1;
    const sort = query.sort ? query.sort : { _id: -1 };

    const keyword = query.search
      ? {
          $or: [
            {
              review: {
                $regex: query.search,
                $options: "i",
              },
            },
          ],
        }
      : {};
    if (query.store) {
      keyword.store = query.store;
    }
    const count = await StoreReviews.countDocuments({ ...keyword });
    const allStoreReviews = await StoreReviews.find({ ...keyword })
      .populate("store", "name active uid")
      .populate("reviewer", "name active uid")
      .sort(sort)
      .limit(pageSize)
      .skip((page - 1) * pageSize);

    return {
      search: query.search,
      allStoreReviews,
      page,
      pageSize,
      pages: Math.ceil(count / pageSize),
    };
  };
  getStoreReviewDetails = async (reviewId) => {
    const storeReview = await StoreReviews.findById(reviewId);
    if (!storeReview) {
      throw new HttpException(404, "resource not found!");
    }
    return storeReview;
  };
  updateStoreReview = async (reviewId, body) => {
    const storeReview = await StoreReviews.findById(reviewId);
    if (!storeReview) {
      throw new HttpException(404, "resource not found!");
    }
    await StoreReviews.updateOne({ _id: reviewId }, { $set: body });
    return storeReview;
  };

  updateStoreReviewActiveStatus = async (reviewId) => {
    const storeReview = await StoreReviews.findById(reviewId);
    if (!storeReview) {
      throw new HttpException(404, "resource not found!");
    }
    storeReview.active = !storeReview.active;
    await storeReview.save();
    return storeReview;
  };
  permanentDeleteStoreReview = async (reviewId) => {
    const review = await StoreReviews.findById(reviewId);
    if (!review) {
      throw new HttpException(404, "resource not found!");
    }
    await StoreReviews.deleteOne(reviewId);
    return true;
  };
}
