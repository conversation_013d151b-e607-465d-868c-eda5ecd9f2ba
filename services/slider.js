import { HttpException } from '../exceptions/httpException.js'
import { Slider } from '../models/sliders.js'

export class SliderService {
	createSlider = async payload => {
		const slider = await Slider.create(payload)
		if (!slider) {
			throw new HttpException(404, 'failed to create new slider!')
		}
		return slider
	}
	updateSlider = async (sliderId, body) => {
		const slider = await Slider.findById(sliderId)
		if (!slider) {
			throw new HttpException(404, 'resource not found!')
		}
		await Slider.updateOne({ _id: sliderId }, { $set: body })

		return slider
	}
	deleteSlider = async sliderId => {
		const slider = await Slider.findById(sliderId)
		if (!slider) {
			throw new HttpException(404, 'resource not found!')
		}
		await Slider.deleteOne({ _id: sliderId })
		return true
	}
	getAllSliders = async query => {
		const pageSize = Number(query.pageSize) || 10
		const page = Number(query.page) || 1
		const sort = query.sort ? query.sort : { _id: -1 }

		const keyword = query.search
			? {
				$or: [
					{
						sliderOffer: {
							$regex: query.search,
							$options: 'i',
						},
					},
				],
			}
			: {}
		if (query.lowPriority === 'true') {
			sort.priority = 1
		}
		if (query.hiPriority === 'true') {
			sort.priority = -1
		}
		if (query.firstExpiry === 'true') {
			keyword.expiryDate = { $gte: new Date() }
			sort.expiryDate = 1
		}
		const count = await Slider.countDocuments({ ...keyword })
		const allSliders = await Slider.find({ ...keyword })
			.populate('createdBy', 'name count')
			.sort(sort)
			.limit(pageSize)
			.skip((page - 1) * pageSize)

		return {
			search: query.search,
			allSliders,
			page,
			pageSize,
			pages: Math.ceil(count / pageSize),
		}
	}
	getAllSlidersList = async () => {
		const allSliders = await Slider.find()
		return allSliders
	}
	updateSliderPriority = async (sliderId, priority) => {
		const slider = await Slider.findById(sliderId)
		if (!slider) {
			throw new HttpException(404, 'resource not found!')
		}
		slider.priority = priority
		await slider.save()
		return slider
	}
	getSliderDetails = async () => { }
}
