import { HttpException } from '../exceptions/httpException.js'
import { TermsAndPrivacy } from '../models/termsAndPrivacy.js'
import { TermsAndPrivacyLogs } from '../models/termsAndPrivacyLogs.js'

export class TermsAndPrivacyService {
  createTermsAndPrivacy = async (payload) => {

    console.log(
      '🚀 ~ TermsAndPrivacyService ~ createTermsAndPrivacyService= ~ payload:',
      payload,
    )
    const oldTermsAndPrivacy = await TermsAndPrivacy.findOne({ type: payload.type })
    if (oldTermsAndPrivacy) {
      throw new HttpException(403, `you already created a terms and privacy with ${payload.type} type`)
    }

    const termsAndPrivacy = await TermsAndPrivacy.create(payload)
    await TermsAndPrivacyLogs.create(payload)
    if (!termsAndPrivacy) {
      throw new HttpException(404, 'failed to create new  terms and Privacy ')
    }
    return termsAndPrivacy

  }

  getAllTermsAndPrivacies = async query => {
    const pageSize = Number(query.pageSize) || 5
    const page = Number(query.page) || 1
    const sort = query.sort ? query.sort : { _id: -1 }

    const keyword = query.search
      ? {
        $or: [
          {
            content: {
              $regex: query.search,
              $options: 'i',
            },
          },
        ],
      }
      : {}
    if (query.admin) {
      keyword.createdBy = query.admin
    }
    const count = await TermsAndPrivacy.countDocuments({ ...keyword })
    const allTermsAndPrivacies = await TermsAndPrivacy.find({ ...keyword })
      .populate('createdBy', 'name uid ')
      .sort({ _id: -1 })
      .limit(pageSize)
      .skip((page - 1) * pageSize)

    return {
      search: query.search,
      allTermsAndPrivacies,
      page,
      pageSize,
      pages: Math.ceil(count / pageSize),
    }
  }

  getTermsAndPrivacyDetails = async id => {
    const termsAndPrivacy = await TermsAndPrivacy.findById(
      id,
    ).populate('createdBy', 'name uid')

    if (!termsAndPrivacy) {
      throw new HttpException(404, 'terms and Privacy not found!')
    }

    return termsAndPrivacy
  }
  updateTermsAndPrivacyType = async (id, type) => {
    const termsAndPrivacy = await TermsAndPrivacy.findById(id)
    if (!termsAndPrivacy) {
      throw new HttpException(404, 'terms and Privacy not found!')
    }
    await TermsAndPrivacy.updateOne({ _id: id }, {
      $set: { type }
    })
    return termsAndPrivacy
  }

  updateTermsAndPrivacy = async (body, id, createdBy) => {
    const termsAndPrivacy = await TermsAndPrivacy.findById(id)
    if (!termsAndPrivacy) {
      throw new HttpException(404, 'terms and Privacy not found!')
    }
    const existTermsAndPrivacy = await TermsAndPrivacy.findOne({ type: body.type, _id: { $ne: id } })
    if (existTermsAndPrivacy) {
      throw new HttpException(403, 'cannot update existing terms and privacy type ')
    }
    await TermsAndPrivacy.updateOne({ _id: id }, {
      $set: body
    })
    await TermsAndPrivacyLogs.create({ ...body, createdBy })
    return termsAndPrivacy
  }

  updateTermsAndPrivacyActiveStatus = async id => {
    const termsAndPrivacy = await TermsAndPrivacy.findById(id)
    if (!termsAndPrivacy) {
      throw new HttpException(404, 'terms and conditions not found!')
    }
    await TermsAndPrivacy.updateOne({ _id: id }, { $set: { active: !termsAndPrivacy.active } })

  }

  getTermsAndPrivacyLogs = async (query) => {
    const pageSize = Number(query.pageSize) || 5
    const page = Number(query.page) || 1
    const sort = query.sort ? query.sort : { _id: -1 }

    const keyword = query.search
      ? {
        $or: [
          {
            content: {
              $regex: query.search,
              $options: 'i',
            },
          },
        ],
      }
      : {}
    if (query.admin) {
      keyword.createdBy = query.admin
    }
    const count = await TermsAndPrivacyLogs.countDocuments({ ...keyword })
    const allTermsAndPrivacyLogs = await TermsAndPrivacyLogs.find({ ...keyword })
      .populate('createdBy', 'name uid ')
      .sort({ _id: -1 })
      .limit(pageSize)
      .skip((page - 1) * pageSize)

    return {
      search: query.search,
      allTermsAndPrivacyLogs,
      page,
      pageSize,
      pages: Math.ceil(count / pageSize),
    }
  }

}
