import { HttpException } from '../exceptions/httpException.js'
import { GiftCardOffer } from '../models/giftCards/offers.js'

export class GiftCardOfferService {
	createGiftCardOffer = async payload => {
		const giftCardOffer = await GiftCardOffer.create(payload)
		if (!giftCardOffer) {
			throw new HttpException(404, 'filed to create new gift card offer')
		}
		return giftCardOffer
	}

	getAllGiftCardOffers = async query => {
		const pageSize = Number(query.pageSize) || 5
		const page = Number(query.page) || 1
		const sort = query.sort ? query.sort : { _id: -1 }

		const keyword = query.search
			? {
				$or: [
					{
						name: {
							$regex: query.search,
							$options: 'i',
						},
					},
				],
			}
			: {}
		if (query.giftCard) {
			keyword.giftCard = query.giftCard
		}
		const count = await GiftCardOffer.countDocuments({ ...keyword })
		const allGiftCardOffers = await GiftCardOffer.find({ ...keyword })
			.populate('createdBy', 'name active')
			.populate('giftCard', 'name active')
			.sort(sort)
			.limit(pageSize)
			.skip((page - 1) * pageSize)

		return {
			search: query.search,
			allGiftCardOffers,
			page,
			pageSize,
			pages: Math.ceil(count / pageSize),
		}
	}

	getGiftCardOfferDetails = async offerId => {
		const giftCardOffer = await GiftCardOffer.findById(offerId).populate('giftCard', 'name active')
		if (!giftCardOffer) {
			throw new HttpException(404, 'resource not found!')
		}
		return giftCardOffer
	}

	updateGiftCardOffer = async (offerId, payload) => {
		const giftCardOffer = await GiftCardOffer.findById(offerId)
		if (!giftCardOffer) {
			throw new HttpException(404, 'resource not found!')
		}
		await GiftCardOffer.updateOne({ _id: offerId }, { $set: payload })
		return giftCardOffer
	}

	deleteGiftCardOffer = async offerId => {
		const giftCardOffer = await GiftCardOffer.findById(offerId)
		if (!giftCardOffer) {
			throw new HttpException(404, 'resource not found!')
		}
		giftCardOffer.active = !giftCardOffer.active
		await giftCardOffer.save()
		return giftCardOffer
	}
}
