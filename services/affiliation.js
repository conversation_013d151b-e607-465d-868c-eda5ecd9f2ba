import { HttpException } from '../exceptions/httpException.js'
import { Affiliation } from '../models/affiliation.js'
import { AffiliationToken } from '../models/affiliationToken.js'

export class AffiliationService {
	createAffiliation = async body => {
		const affiliation = await Affiliation.create(body)
		if (!affiliation) {
			throw new HttpException(404, 'failed to create new Affiliation')
		}
		return affiliation
	}

	getAllAffiliations = async query => {
		const pageSize = Number(query.pageSize) || 10
		const page = Number(query.page) || 1

		const keyword = query.search
			? {
					$or: [
						{
							name: {
								$regex: query.search,
								$options: 'i',
							},
						},
					],
				}
			: {}

		if (query.admin) {
			keyword.admin = query.admin
		}

		const count = await Affiliation.countDocuments({ ...keyword })
		const allAffiliations = await Affiliation.find({ ...keyword })
			.populate('createdBy', 'name uid')
			.sort({ _id: -1 })
			.limit(pageSize)
			.skip((page - 1) * pageSize)

		return {
			allAffiliations,
			page,
			pages: Math.ceil(count / pageSize),
			pageSize,
			search: query.search,
		}
	}
	getAffiliationDetails = async affiliationId => {
		const affiliation = await Affiliation.findById(affiliationId)
		if (!affiliation) {
			throw new HttpException(404, 'resource not found')
		}
		return affiliation
	}

	getAllAffiliationsList = async () => {
		const allAffiliations = await Affiliation.find({}, 'name uid').sort({
			_id: -1,
		})
		return allAffiliations
	}
	updateAffiliation = async (affiliationId, body) => {
		const affiliation = await Affiliation.findById(affiliationId)
		if (!affiliation) {
			throw new HttpException(404, 'resource not found')
		}
		await Affiliation.updateOne({ _id: affiliationId }, { $set: body })
		return affiliation
	}

	updateAffiliationStatus = async affiliationId => {
		const affiliation = await Affiliation.findById(affiliationId)
		if (!affiliation) {
			throw new HttpException(404, 'resource not found')
		}
		affiliation.active = !affiliation.active
		await affiliation.save()
		return affiliation
	}
	updateAffiliationTokenByName = async affiliationId => {
		const affiliation = await Affiliation.findById(affiliationId)
		if (!affiliation) {
			throw new HttpException(404, 'resource not found')
		}
		affiliation.active = !affiliation.active
		await affiliation.save()
		return affiliation
	}

	findAffiliationTokenByName = async name => {
		const affiliationToken = await AffiliationToken.findOne({ name })

		return affiliationToken
	}

	upsertAffiliationToken = async ({
		name,
		accessToken,
		createdBy,
		updatedBy,
	}) => {
		try {
			// Find the entity by name
			let affiliationToken = await AffiliationToken.findOne({ name })

			if (affiliationToken) {
				// If the entity exists, update the accessToken and updatedBy
				affiliationToken.accessToken = accessToken
				affiliationToken.updatedBy = updatedBy
				await affiliationToken.save()
				console.log('Affiliation token updated successfully:', affiliationToken)
			} else {
				// If the entity doesn't exist, create a new one
				affiliationToken = new AffiliationToken({
					name,
					accessToken,
					createdBy,
					updatedBy,
				})
				await affiliationToken.save()
				console.log('Affiliation token created successfully:', affiliationToken)
			}

			return affiliationToken
		} catch (error) {
			console.error('Error upserting affiliation token:', error)
			throw error
		}
	}
}
