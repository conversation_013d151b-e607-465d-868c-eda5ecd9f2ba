import { HttpException } from '../exceptions/httpException.js'
import { StoreLog } from '../models/stores/log.js'

export class StoreLogService {
	createStoreLog = async body => {
		const storeLog = await StoreLog.create(body)
		if (!storeLog) {
			throw new HttpException(404, 'filed to create new store log')
		}
		return storeLog
	}
	getAllStoreLogs = async query => {
		const pageSize = Number(query.pageSize) || 5
		const page = Number(query.page) || 1
		const sort = query.sort ? query.sort : { _id: -1 }

		const keyword = query.search
			? {
					$or: [
						{
							name: {
								$regex: query.search,
								$options: 'i',
							},
						},
					],
			  }
			: {}
		if (query.admin) {
			keyword.createdBy = query.admin
		}
		const count = await StoreLog.countDocuments({ ...keyword })
		const allStoreLogs = await StoreLog.find({ ...keyword })
			.sort(sort)
			.limit(pageSize)
			.skip((page - 1) * pageSize)

		return {
			search: query.search,
			allStoreLogs,
			page,
			pageSize,
			pages: Math.ceil(count / pageSize),
		}
	}
}
