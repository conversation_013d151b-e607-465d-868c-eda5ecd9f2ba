import { HttpException } from '../exceptions/httpException.js'
import { Banner } from '../models/banners/banner.js'

export class GiftCardSliderService {
	createGiftCardSlider = async payload => {
		const giftCardSlider = await Banner.create(payload)
		if (!giftCardSlider) {
			throw new HttpException(404, 'filed to create gift card slider')
		}
		return giftCardSlider
	}

	getAllGiftCardSliders = async query => {
		const pageSize = Number(query.pageSize) || 5
		const page = Number(query.page) || 1
		const sort = query.sort ? query.sort : { _id: -1 }

		const keyword = query.search
			? {
				$or: [
					{
						name: {
							$regex: query.search,
							$options: 'i',
						},
					},
				],
			}
			: {}
		keyword.type = 'giftCard'
		if (query.admin) {
			keyword.createdBy = query.admin
		}
		const count = await Banner.countDocuments({ ...keyword })
		const allGiftCardSliders = await Banner.find({ ...keyword })
			.populate('createdBy', 'name active')
			.sort(sort)
			.limit(pageSize)
			.skip((page - 1) * pageSize)

		return {
			search: query.search,
			allGiftCardSliders,
			page,
			pageSize,
			pages: Math.ceil(count / pageSize),
		}
	}

	getGiftCardSliderDetails = async sliderId => {
		const giftCardSlider = await Banner.findById(sliderId).populate(
			'createdBy',
			'name active',
		)
		if (!giftCardSlider) {
			throw new HttpException(404, 'resource not found!')
		}
		return giftCardSlider
	}

	updateGiftCardSlider = async (sliderId, payload) => {
		const giftCardSlider = await Banner.findById(sliderId)

		if (!giftCardSlider) {
			throw new HttpException(404, 'resource not found!')
		}
		await Banner.updateOne({ _id: sliderId }, { $set: payload })
		return giftCardSlider
	}

	deleteGiftCardSlider = async sliderId => {
		const giftCardSlider = await Banner.findById(sliderId)

		if (!giftCardSlider) {
			throw new HttpException(404, 'resource not found!')
		}

		await Banner.updateOne({ _id: sliderId, }, { $set: { active: !giftCardSlider.active } })
		return giftCardSlider
	}

	updateGiftCardSliderPriority = async (sliderId, priority) => {
		const giftCardSlider = await Banner.findById(sliderId)

		if (!giftCardSlider) {
			throw new HttpException(404, 'resource not found!')
		}
		await Banner.updateOne({ _id: sliderId, }, { $set: { priority } })
		return giftCardSlider
	}
}
