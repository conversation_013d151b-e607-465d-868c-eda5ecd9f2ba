import { HttpException } from "../exceptions/httpException.js";
import { SubCategory } from "../models/categories/subCategory.js";

export class SubCategoryService {
    createSubCategory = async (payload) => {
        const subCategory = await SubCategory.create(payload);
        if (!subCategory) {
            throw new HttpException(404, "filed to create new sub category!");
        }
        return subCategory;
    };

    updateSubCategory = async (id, body) => {
        const subCategory = await SubCategory.findById(id);
        if (!subCategory) {
            throw new HttpException(404, "resource not found!");
        }
        await SubCategory.updateOne({ _id: id }, { $set: body });
        return subCategory;
    };

    getSubCategoryDetails = async (id) => {
        const subCategory = await SubCategory.findById(id).populate(
            "category",
            "name"
        );
        if (!subCategory) {
            throw new HttpException(404, "resource not found!");
        }
        return subCategory;
    };

    getAllSubCategories = async (query) => {
        const pageSize = Number(query.pageSize) || 10;
        const page = Number(query.page) || 1;

        const keyword = query.search
            ? {
                $or: [
                    {
                        name: {
                            $regex: query.search,
                            $options: "i",
                        },
                    },
                ],
            }
            : {};

        if (query.category) {
            keyword.category = query.category;
        }
        const count = await SubCategory.countDocuments({ ...keyword });
        const allSubCategories = await SubCategory.find({ ...keyword })
            .populate("category", "name")
            .populate("createdBy", "name ")
            .sort({ createdAt: -1 })
            .limit(pageSize)
            .skip((page - 1) * pageSize);

        return {
            allSubCategories,
            page,
            pages: Math.ceil(count / pageSize),
            search: query.search,
        };
    };
    getAllSubCategoriesList = async () => {
        const allSubCategories = await SubCategory.find();
        return allSubCategories;
    };

    deleteSubCategory = async (id) => {
        const subcategory = await SubCategory.findById(id);
        if (!subcategory) {
            throw new HttpException(404, "resource not found!");
        }

        await SubCategory.findByIdAndDelete(id);
        return subcategory;
    };
}
