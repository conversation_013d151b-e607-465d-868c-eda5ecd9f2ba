import mongoose from "mongoose";
import { HttpException } from "../exceptions/httpException.js";
import { MissingCashback } from "../models/payments/missingCashback.js";
import { User } from "../models/user/user.js";
import { getAwsInvoiceUrl } from "../utils/awsFileHandler.js";
import { Clicks } from "../models/clicks.js";
import { convertDatesToUTC } from "../utils/dateFormatter.js";

export class MissingCashbackService {
  getAllMissingCashback = async (query) => {
    const pageSize = Number(query.pageSize) || 5;
    const page = Number(query.page) || 1;
    const sort = query.sort ? query.sort : { _id: -1 };

    const keyword = {};

    // Initialize keyword.$or if it doesn't exist
    keyword.$or = keyword.$or || [];
    keyword.$and = keyword.$and || [];

    if (query.search?.trim()) {
      const searchTerm = query.search.trim();

      // Prepare the conditions for $or query
      const queryConditions = [
        { name: { $regex: searchTerm, $options: "i" } },
        { email: { $regex: searchTerm, $options: "i" } },
      ];

      // Add uid condition only if searchTerm is a valid number
      if (!Number.isNaN(Number(searchTerm))) {
        queryConditions.push({ uid: Number(searchTerm) });
      }

      // Fetch matching users
      const matchingUsers = await User.find(
        { $or: queryConditions },
        { _id: 1 } // Project only the _id field
      );

      const userIds = matchingUsers.map((user) => user._id);

      // Add user IDs to the keyword filter if found
      if (userIds.length) {
        keyword.$or = keyword.$or || []; // Ensure $or exists
        keyword.$or.push({ user: { $in: userIds } });
      }
    }

    if (query.startDate && query.endDate) {
      const { startUTC, endUTC } = convertDatesToUTC(
        query.startDate,
        query.endDate
      );
      // Find clicks within date range
      const clicks = await Clicks.find({
        createdAt: {
          $gte: startUTC,
          $lte: endUTC,
        },
      }).select("_id");

      const clickIds = clicks.map((click) => click._id);

      // Add click IDs filter
      keyword.$and = keyword.$and || [];
      keyword.$and.push({
        click: { $in: clickIds },
      });
    }

    if (query.affiliation) {
      keyword.$and = keyword.$and || [];
      //keyword.$and.push({ affiliation: query.affiliation });
      keyword.$and.push({
        affiliation: new mongoose.Types.ObjectId(query.affiliation),
      });
    }

    if (query.store) {
      keyword.$and = keyword.$and || [];
      keyword.$and.push({
        store:
          query.store.trim().length === 24
            ? new mongoose.Types.ObjectId(query.store)
            : query.store,
      });
    }

    if (query.status && query.status !== "all") {
      keyword.$and = keyword.$and || [];
      keyword.$and.push({
        status: query.status,
      });
    }

    if (keyword.$or.length === 0) {
      // keyword.$or = undefined; // Remove empty $or
      delete keyword.$or;
    }

    if (keyword.$and.length === 0) {
      // keyword.$and = undefined;
      delete keyword.$and;
    }

    if (query.user) {
      keyword.user = query.user;
    }
    const count = await MissingCashback.countDocuments({ ...keyword });
    const allMissedCashback = await MissingCashback.find({ ...keyword })
      .populate("user", "name email uid")
      .populate("store", "uid name ")
      .populate("click", "uid createdAt ")
      .sort(sort)
      .limit(pageSize)
      .skip((page - 1) * pageSize);

    // get aws signed invoice url

    const allData = await Promise.all(
      allMissedCashback.map(async (item) => {
        if (item?.invoice?.location) {
          const data = await getAwsInvoiceUrl(item.invoice.location);
          item.invoice = data;
        }
        return item;
      })
    );

    return {
      search: query.search,
      allMissedCashback: allData,
      page,
      pageSize,
      pages: Math.ceil(count / pageSize),
    };
  };
  rejectMissingCashback = async (id, adminId) => {
    const missingCashback = await MissingCashback.findById(id);
    if (!missingCashback) {
      throw new HttpException(404, "resource not found");
    }
    missingCashback.status = "cancelled";
    missingCashback.updatedBy = adminId;
    await missingCashback.save();
    return missingCashback;
  };

  confirmedMissingCashback = async (id, adminId) => {
    const missingCashback = await MissingCashback.findById(id);
    if (!missingCashback) {
      throw new HttpException(404, "resource not found");
    }
    missingCashback.status = "confirmed";
    missingCashback.updatedBy = adminId;
    await missingCashback.save();
    return missingCashback;
  };

  getMissingCashbackDetails = async (id) => {
    const missingCashback = await MissingCashback.findById(id)
      .populate("user", "name email count")
      .populate("store", "count name ")
      .populate("affiliate", "count name");
    if (!missingCashback) {
      throw new HttpException(404, "resource not found");
    }
    return missingCashback;
  };

  updateMissingCashbackNotes = async (id, notes, adminId) => {
    const missingCashback = await MissingCashback.findById(id);
    if (!missingCashback) {
      throw new HttpException(404, "resource not found");
    }
    missingCashback.notes = notes;
    missingCashback.updatedBy = adminId;
    await missingCashback.save();
    return missingCashback;
  };

  updateMissingCashbackStatus = async (id, status, adminId) => {
    const missingCashback = await MissingCashback.findById(id);
    if (!missingCashback) {
      throw new HttpException(404, "resource not found");
    }
    missingCashback.status = status;
    missingCashback.updatedBy = adminId;
    await missingCashback.save();
    return missingCashback;
  };

  deleteMissingCashback = async (id) => {
    const missingCashback = await MissingCashback.findByIdAndDelete(id);
    console.log(missingCashback, "misisng to delte");
    if (!missingCashback) {
      throw new HttpException(404, "resource not found");
    }
    return missingCashback;
  };
}
