import { HttpException } from "../exceptions/httpException.js";
import { GiftCard } from "../models/giftCards/giftCard.js";
import { GiftCardSchema } from "../models/meilisearch/giftCard.js";
import { StoreCategory } from "../models/categories/storeCategory.js";
import { MeiliSearchService } from "../config/meilisearch.js";

export class GiftCardService {
  meilisearch = new MeiliSearchService();
  createGiftCard = async (payload) => {
    const giftCard = await GiftCard.create(payload);
    if (!giftCard) {
      throw new HttpException(404, "filed to create gift card!");
    }

    const newGiftCard = await GiftCard.findById(giftCard._id)
      .populate("relatedCbStore")
      .populate("relatedInstantStore", "name")
      .populate("categories.category", "name")
      .populate("categories.subCategories", "name")
      .exec();

    const categories = [];
    const subcategories = [];
    newGiftCard.categories.map((item) => {
      categories.push({ name: item?.category?.name });
      item?.subCategories?.map((subCat) => {
        if (!subcategories.includes({ name: subCat?.name })) {
          subcategories.push({ name: subCat?.name });
        }
      });
    });

    const storecategories = await StoreCategory.find(
      { store: newGiftCard.relatedCbStore._id },
      { name: 1, _id: 0 }
    ).exec();

    const giftCardDocument = new GiftCardSchema({
      id: newGiftCard._id,
      uid: newGiftCard.uid,
      url: newGiftCard.logo.secureUrl,
      active: newGiftCard.active,
      name: newGiftCard.name,
      description: newGiftCard.description,
      cashbackGiving: newGiftCard.cashbackGiving,
      relatedInstantStore: newGiftCard.relatedInstantStore.name,
      categories: categories,
      subcategories: subcategories,
      storecategories: storecategories,
    });

    await this.meilisearch.addDocuments(giftCardDocument);
    return giftCard;
  };

  getAllGiftCards = async (query) => {
    const pageSize = Number(query.pageSize) || 5;
    const page = Number(query.page) || 1;
    const sort = { _id: -1 };

    const keyword = query.search
      ? {
          $or: [
            {
              name: {
                $regex: query.search,
                $options: "i",
              },
            },
          ],
        }
      : {};
    if (query.admin) {
      keyword.createdBy = query.admin;
    }
    const count = await GiftCard.countDocuments({ ...keyword });
    const allGiftCards = await GiftCard.find({ ...keyword })
      .populate("createdBy", "name active")
      .populate("updatedBy", "name active")
      .populate("relatedCbStore", "name")
      .sort(sort)
      .limit(pageSize)
      .skip((page - 1) * pageSize);

    return {
      search: query.search,
      allGiftCards,
      page,
      pageSize,
      pages: Math.ceil(count / pageSize),
    };
  };

  getAllGiftCardList = async () => {
    const allGiftCards = await GiftCard.find().sort({ _id: -1 });
    return allGiftCards;
  };

  getGiftCardDetails = async (giftCardId) => {
    const giftCard = await GiftCard.findById(giftCardId)
      .populate("createdBy", "name active")
      .populate("updatedBy", "name active")
      .populate("categories.category", "name ")
      .populate("categories.subCategories", "name ")
      .populate("relatedCbStore", "name")
      .populate("relatedInstantStore", "name");

    if (!giftCard) {
      throw new HttpException(404, "resource not found!");
    }
    return giftCard;
  };

  updateGiftCard = async (giftCardId, body) => {
    const giftCard = await GiftCard.findById(giftCardId);
    if (!giftCard) {
      throw new HttpException(404, "resource not found!");
    }
    await GiftCard.updateOne({ _id: giftCardId }, { $set: body });
    const updatedGiftCard = await GiftCard.findById(giftCardId)
      .populate("relatedCbStore")
      .populate("categories.category", "name ")
      .populate("categories.subCategories", "name ")
      .populate("relatedInstantStore", "name")
      .exec();

    const categories = [];
    const subcategories = [];
    updatedGiftCard.categories.map((item) => {
      categories.push({ name: item?.category?.name });
      item?.subCategories?.map((subCat) => {
        if (!subcategories.includes({ name: subCat?.name })) {
          subcategories.push({ name: subCat?.name });
        }
      });
    });

    const storecategories = await StoreCategory.find(
      { store: updatedGiftCard.relatedCbStore._id },
      { name: 1, _id: 0 }
    ).exec();

    const giftCardDocument = new GiftCardSchema({
      id: updatedGiftCard._id,
      uid: updatedGiftCard.uid,
      url: updatedGiftCard.logo.secureUrl,
      active: updatedGiftCard.active,
      name: updatedGiftCard.name,
      description: updatedGiftCard.description,
      cashbackGiving: updatedGiftCard.cashbackGiving,
      relatedInstantStore: updatedGiftCard.relatedInstantStore,
      categories: categories,
      subcategories: subcategories,
      storecategories: storecategories,
    });

    await this.meilisearch.updateDocuments(giftCardDocument);

    return giftCard;
  };

  deleteGiftCard = async (id, adminId) => {
    const giftCard = await GiftCard.findById(id);
    if (!giftCard) {
      throw new HttpException(404, "resource not found!");
    }
    await GiftCard.updateOne(
      { _id: id },
      {
        $set: {
          active: !giftCard.active,
          updatedBy: adminId,
        },
      }
    );
    return giftCard;
  };

  updateGiftCardPriority = async (id, priority, adminId) => {
    const giftCard = await GiftCard.findById(id);
    if (!giftCard) {
      throw new HttpException(404, "resource not found!");
    }
    await GiftCard.updateOne(
      { _id: id },
      {
        $set: {
          priority,
          updatedBy: adminId,
        },
      }
    );
    return giftCard;
  };
}
