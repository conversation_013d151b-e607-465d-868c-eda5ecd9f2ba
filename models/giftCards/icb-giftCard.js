import { Schema, Types, model } from "mongoose";

const icbGiftCardSchema = new Schema(
  {
    uid: { type: Number, unique: true, index: true },
    giftCard: { type: Types.ObjectId, required: true, ref: "GiftCard" },
    giftCardNumber: { type: String, required: true, unique: true },
    orderId: { type: String, required: true, unique: true },
    giftCardPin: { type: String, required: true },
    mobile: { type: Number },
    email: { type: String, required: true },
    name: { type: String, required: true },
    msg: { type: Number, default: "ICB Gift Card" },
    isRedeemed: { type: Boolean, default: false },
    buyer: { type: Types.ObjectId, ref: "User", required: true },
    amount: { type: Number, required: true },
    redeemDate: { type: Date, default: null },
    expiryDate: { type: String, required: true },
    active: { type: Boolean, default: true },
    createdBy: { type: Types.ObjectId, ref: "Admin" },
    updatedBy: { type: Types.ObjectId, ref: "Admin" },
  },
  { timestamps: true }
);
icbGiftCardSchema.index(
  { "giftCard.uid": 1 },
  { unique: true, partialFilterExpression: { "giftCard.uid": { $ne: null } } }
);

icbGiftCardSchema.pre("save", async function (next) {
  if (!this.isNew) {
    return next();
  }
  const lastDocument = await this.constructor.findOne().sort({ _id: -1 });
  if (lastDocument) {
    this.uid = lastDocument.uid + 1;
  } else {
    this.uid = 1;
  }
  next();
});

export const IcbGiftCard = model("IcbGiftCard", icbGiftCardSchema);
