import mongoose from "mongoose";

const offerSchema = new mongoose.Schema(
  {
    uid: { type: Number, unique: true, index: true },
    name: { type: String, required: true },
    giftCard: {
      type: mongoose.Types.ObjectId,
      ref: "GiftCard",
      required: true,
    },
    rateGetting: { type: String },
    rateGiving: { type: String },
    startDate: { type: Date, required: true },
    endDate: { type: Date, required: true },
    terms: { type: String, required: true },
    active: { type: Boolean, default: true },
    createdBy: { type: mongoose.Types.ObjectId, ref: "Admin", required: true },
    updatedBy: { type: mongoose.Types.ObjectId, ref: "Admin" },
  },
  { timestamps: true }
);
// Define a pre-save hook to generate the value for the "uid" field
offerSchema.pre("save", async function (next) {
  if (!this.isNew) {
    return next();
  }
  const lastDocument = await this.constructor.findOne().sort({ _id: -1 });
  if (lastDocument) {
    this.uid = lastDocument.uid + 1;
  } else {
    this.uid = 1;
  }
  next();
});
export const GiftCardOffer = mongoose.model("GiftCardOffer", offerSchema);
