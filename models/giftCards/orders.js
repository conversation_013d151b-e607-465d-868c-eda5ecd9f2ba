import { Schema, Types, model } from "mongoose";

const cardSchema = new Schema({
  amount: { type: Number },
  quantity: { type: Number },
  _id: false,
});

const giftCardOrderSchema = new Schema(
  {
    uid: { type: Number, unique: true, index: true },
    user: { type: Types.ObjectId, required: true, ref: "User" },
    giftCard: { type: Types.ObjectId, required: true, ref: "GiftCard" },
    totalAmount: { type: Number, required: true },
    cards: [cardSchema],
    icbBalance: { type: Number, default: 0, required: true },
    mobile: { type: Number },
    email: { type: String, required: true },
    name: { type: String, required: true },
    paymentType: {
      type: String,
      enum: ["balance", "razorpay"],
      required: true,
    },
    orderId: { type: String },
    msg: { type: String },
    paymentSession: { type: Types.ObjectId, ref: "PaymentSession" },
    paymentVerified: { type: Boolean, default: false },
    updatedBy: { type: Types.ObjectId, ref: "Admin" },
  },
  { timestamps: true }
);

giftCardOrderSchema.pre("save", async function (next) {
  if (!this.isNew) {
    return next();
  }
  const lastDocument = await this.constructor.findOne().sort({ _id: -1 });
  if (lastDocument) {
    this.uid = lastDocument.uid + 1;
  } else {
    this.uid = 1;
  }
  next();
});

export const GiftCardOrder = model("GiftCardOrder", giftCardOrderSchema);
