import { Schema, Types, model } from "mongoose";
import { CategorySchema, ImageSchema } from "../common.model.js";

const giftCardSchema = new Schema(
  {
    uid: { type: Number, unique: true, index: true },
    name: { type: String, required: true }, //
    logo: ImageSchema,
    image: ImageSchema,
    categories: [CategorySchema],
    relatedGiftCard: [{ type: Types.ObjectId, ref: "GiftCard" }],
    discountGetting: { type: Number },
    cashbackGiving: { type: Number, min: 0, max: 100, required: true },
    validity: { type: Date, required: true },
    notes: { type: String },
    howToUse: [{ type: String, required: true }],
    terms: { type: String, required: true },
    description: { type: String, required: true },
    priority: { type: Number, default: 0 },
    qwickcilverId: { type: String },
    tncMail: { type: String },
    qwickcilverImage: ImageSchema,
    denominations: [{ type: Number }],
    isCustomDenomination: { type: Boolean, default: false },
    cardType: { type: String, enum: ["icb", "qwickcilver"], default: "icb" },
    refresh: { type: String, enum: ["yes", "no"], default: "no" },
    minimumAmount: { type: Number, required: true },
    maximumAmount: { type: Number, required: true },
    relatedCbStore: { type: Types.ObjectId, ref: "Store" },
    relatedInstantStore: { type: Types.ObjectId, ref: "Store" },
    provider: { type: String },
    active: { type: Boolean, default: true },
    createdBy: { type: Types.ObjectId, ref: "Admin", required: true },
    updatedBy: { type: Types.ObjectId, ref: "Admin" },
  },
  { timestamps: true }
);
giftCardSchema.index({
  cashbackGiving: -1,
});

// Define a pre-save hook to generate the value for the "uid" field
giftCardSchema.pre("save", async function (next) {
  if (!this.isNew) {
    return next();
  }
  const lastDocument = await this.constructor.findOne().sort({ _id: -1 });
  if (lastDocument) {
    this.uid = lastDocument.uid + 1;
  } else {
    this.uid = 1;
  }
  next();
});

export const GiftCard = model("GiftCard", giftCardSchema);
