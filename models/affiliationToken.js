import { Schema, Types, model } from "mongoose";

const affiliationTokenSchema = new Schema(
    {
        uid: { type: Number, unique: true, index: true },
        name: { type: String, required: true },
        createdBy: { type: Types.ObjectId, ref: "Admin", required: true },
        updatedBy: { type: Types.ObjectId, ref: "Admin" },
        accessToken: { type: String, required: true },
    },
    { timestamps: true }
);

// Define a pre-save hook to generate the value for the "uid" field
affiliationTokenSchema.pre("save", async function(next) {
    if (!this.isNew) {
        return next();
    }
    const lastDocument = await this.constructor.findOne().sort({ _id: -1 });
    if (lastDocument) {
        this.uid = lastDocument.uid + 1;
    } else {
        this.uid = 1;
    }
    next();
});

export const AffiliationToken = model("AffiliationToken", affiliationTokenSchema);
