import { model, Types, Schema } from "mongoose";

const bankAccountDetailsSchema = new Schema(
  {
    uid: { type: Number, unique: true, index: true }, // Unique identifier
    user: { type: Types.ObjectId, ref: "User", required: true }, // Reference to User entity
    holderName: { type: String, required: true }, // Account holder's name
    bankName: { type: String, required: true }, // Bank name
    branchName: { type: String, required: true }, // Branch name
    accountNumber: { type: String, required: true, trim: true }, // Account number
    ifsc: { type: String, required: true }, // IFSC code
    upi: { type: String }, // Optional UPI ID
    active: { type: Boolean, default: true }, // Active status
  },
  { timestamps: true } // Automatically manages createdAt and updatedAt fields
);

// Define a pre-save hook to generate the value for the "uid" field
bankAccountDetailsSchema.pre("save", async function (next) {
  if (!this.isNew) {
    return next();
  }
  const lastDocument = await this.constructor.findOne().sort({ _id: -1 });
  if (lastDocument) {
    this.uid = lastDocument.uid + 1;
  } else {
    this.uid = 1;
  }
  next();
});

// Export the BankAccountDetails model
export const BankAccountDetails = model(
  "BankAccountDetails",
  bankAccountDetailsSchema
);

// import { Schema, Types, model } from "mongoose";

// const accountSchema = new Schema(
//   {
//     uid: { type: Number, unique: true, index: true },
//     user: { type: Types.ObjectId, ref: "User" },
//     holderName: { type: String, required: true },
//     bankName: { type: String, required: true },
//     branchName: { type: String, required: true },
//     accountNumber: { type: String, required: true },
//     ifsc: { type: String, required: true },
//     upi: { type: String },
//     active: { type: Boolean, default: true },
//   },
//   { timestamps: true }
// );

// // Define a pre-save hook to generate the value for the "uid" field
// accountSchema.pre("save", async function (next) {
//   if (!this.isNew) {
//     return next();
//   }
//   const lastDocument = await this.constructor.findOne().sort({ _id: -1 });
//   if (lastDocument) {
//     this.uid = lastDocument.uid + 1;
//   } else {
//     this.uid = 1;
//   }
//   next();
// });
// export const BankAccountDetails = model("BankAccountDetails", accountSchema);
