import mongoose from "mongoose";

const checkMailSchema = new mongoose.Schema(
  {
    uid: { type: Number, unique: true, index: true },
    user: { type: mongoose.Types.ObjectId, ref: "user" },
    fullName: { type: String },
    address: { type: String },
    city: { type: String },
    state: { type: String },
    pinCode: { type: String },
    phone: { type: String },
    active: { type: Boolean, default: true },
  },
  { timestamps: true }
);

// Define a pre-save hook to generate the value for the "uid" field
checkMailSchema.pre("save", async function (next) {
  if (!this.isNew) {
    return next();
  }
  const lastDocument = await this.constructor.findOne().sort({ _id: -1 });
  if (lastDocument) {
    this.uid = lastDocument.uid + 1;
  } else {
    this.uid = 1;
  }
  next();
});
export const PaymentRequest = mongoose.model(
  "checkMaillingdetails",
  checkMailSchema
);
