import { Types, model, Schema } from "mongoose";
import { FileSchema } from "../common.model.js";

const missingCashbackSchema = new Schema(
  {
    uid: { type: Number, unique: true, index: true },
    click: { type: Types.ObjectId, ref: "Click", required: true },
    store: { type: Types.ObjectId, ref: "Store", required: true },
    invoice: FileSchema,
    complaintId: { type: String },
    coupon: { type: Boolean, required: true },
    orderId: { type: String, required: true },
    title: { type: String },
    message: { type: String, required: true },
    paidAmount: { type: Number, required: true },
    userType: { type: String, enum: ["new", "old"] },
    platform: { type: String, enum: ["web", "mobile"] },
    status: {
      type: String,
      enum: [
        "pending",
        "tracked",
        "confirmed",
        "cancelled",
        "not-solved",
        "solved",
        "rejected",
        "forwarded",
      ],
      default: "not-solved",
    },
    user: { type: Types.ObjectId, ref: "User", required: true },
    notes: { type: String },
    updatedBy: { type: Types.ObjectId, ref: "Admin" },
  },
  { timestamps: true }
);

// Define a pre-save hook to generate the value for the "uid" field
missingCashbackSchema.pre("save", async function (next) {
  if (!this.isNew) {
    return next();
  }
  const lastDocument = await this.constructor.findOne().sort({ _id: -1 });
  if (lastDocument) {
    this.uid = lastDocument.uid + 1;
  } else {
    this.uid = 1;
  }
  next();
});
export const MissingCashback = model("MissingCashback", missingCashbackSchema);
