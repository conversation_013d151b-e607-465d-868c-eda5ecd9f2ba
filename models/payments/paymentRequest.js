import { Types, model, Schema } from "mongoose";

const paymentRequestSchema = new Schema(
  {
    uid: { type: Number, unique: true, index: true },
    withdrawer: { type: Types.ObjectId, ref: "User", required: true },
    referenceId: { type: String, required: true, unique: true, index: true },
    withdrawAmount: { type: Number, required: true },
    paymentType: { type: String, enum: ["bank", "upi"], required: true },
    status: {
      type: String,
      default: "pending",
      enum: ["approved", "pending", "rejected"],
      required: true,
    },
    withdrawDate: { type: Date },
    requestedDate: { type: Date },
    updatedBy: { type: Types.ObjectId, ref: "Admin" },
  },
  { timestamps: true }
);

// Define a pre-save hook to generate the value for the "uid" field
paymentRequestSchema.pre("save", async function (next) {
  if (!this.isNew) {
    return next();
  }
  const lastDocument = await this.constructor.findOne().sort({ _id: -1 });
  if (lastDocument) {
    this.uid = lastDocument.uid + 1;
  } else {
    this.uid = 1;
  }
  next();
});
export const PaymentRequest = model("PaymentRequest", paymentRequestSchema);
