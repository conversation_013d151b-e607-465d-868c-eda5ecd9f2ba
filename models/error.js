import { Schema, Types, model } from "mongoose";

const ErrorSchema = new Schema({
  uid: { type: Number, unique: true, index: true },
  message: { type: String },
  statusCode: { type: Number },
  stackTrace: { type: [] },

  body: { type: String },
  user: { type: Types.ObjectId, ref: "User" },
  admin: { type: Types.ObjectId, ref: "Admin" },
  params: { type: String },
  query: { type: String },
  requestUrl: { type: String },
  requestHeaders: { type: String },
  createdAt: { type: Date, default: Date.now() },
});

ErrorSchema.pre("save", async function (next) {
  if (!this.isNew) {
    return next();
  }
  const lastDocument = await this.constructor.findOne().sort({ _id: -1 });
  if (lastDocument) {
    this.uid = Number(lastDocument.uid + 1);
  } else {
    this.uid = 1;
  }
  next();
});

export const ErrorTracker = model("Error", ErrorSchema);
