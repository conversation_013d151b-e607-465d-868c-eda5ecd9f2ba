import { Schema, Types, model } from "mongoose";

const categoryLogSchema = new Schema(
  {
    uid: { type: Number, unique: true, index: true },
    updateBy: {
      type: Types.ObjectId,
      ref: "Admin",
      required: true,
    },
    store: { type: Types.ObjectId, ref: "Store" },
    oldContent: { type: String },
    updatedContent: { type: String, required: true },
  },
  { timestamps: true }
);

// Define a pre-save hook to generate the value for the "uid" field
categoryLogSchema.pre("save", async function (next) {
  if (!this.isNew) {
    return next();
  }
  const lastDocument = await this.constructor.findOne().sort({ _id: -1 });
  if (lastDocument) {
    this.uid = lastDocument.uid + 1;
  } else {
    this.uid = 1;
  }
  next();
});

export const CategoryLog = model("CategoryLog", categoryLogSchema);
