import { Schema, model, Types } from "mongoose";
import { ImageSchema } from "../common.model.js";

const categorySchema = new Schema(
  {
    uid: { type: Number, unique: true, index: true },
    name: { type: String, required: true },
    image: ImageSchema,
    trending: { type: Boolean, default: false },
    trendingPriority: { type: Number, default: 1 },
    priority: { type: Number, default: 1 },
    active: { type: Boolean, default: true },
    createdBy: { type: Types.ObjectId, ref: "Admin" },
    updatedBy: { type: Types.ObjectId, ref: "Admin" },
    isTop: { type: Boolean, default: false },
  },
  { timestamps: true }
);

// Define a pre-save hook to generate the value for the "uid" field
categorySchema.pre("save", async function (next) {
  if (!this.isNew) {
    return next();
  }
  const lastDocument = await this.constructor.findOne().sort({ _id: -1 });
  if (lastDocument) {
    this.uid = lastDocument.uid + 1;
  } else {
    this.uid = 1;
  }
  next();
});

export const Category = model("Category", categorySchema);
