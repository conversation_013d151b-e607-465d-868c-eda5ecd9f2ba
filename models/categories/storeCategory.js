import { ImageSchema } from "../common.model.js";
import { Schema, model, Types } from "mongoose";

const storeCategorySchema = new Schema(
  {
    uid: { type: Number, unique: true, index: true },
    store: { type: Types.ObjectId, required: true, ref: "Store" },
    affiliation: { type: Types.ObjectId, required: true, ref: "Affiliation" },
    name: { type: String },
    description: { type: String },
    givingType : { type: String, enum: ["amount", "percent"] },
    oldUserOfferAmount: { type: Number },
    newUserOfferAmount: { type: Number },
    oldUserOfferPercent: { type: Number },
    newUserOfferPercent: { type: Number },
    gettingType: { type: String, enum: ["amount", "percent"] },
    gettingNewUserRate: { type: Number },
    gettingOldUserRate: { type: Number },
    sectionLink: { type: String },
    couponCode: { type: String },
    type: { type: String },
    device: { type: String, enum: ["app", "website"] },
    weeklyImage: ImageSchema,
    dateExpiry: { type: Date },
    notes: { type: String },
    active: { type: Boolean, default: true },
    createdBy: { type: Types.ObjectId, ref: "Admin", required: true },
    updatedBy: { type: Types.ObjectId, ref: "Admin" },
  },
  { timestamps: true }
);

// Define a pre-save hook to generate the value for the "uid" field
storeCategorySchema.pre("save", async function (next) {
  if (!this.isNew) {
    return next();
  }
  const lastDocument = await this.constructor.findOne().sort({ _id: -1 });
  if (lastDocument) {
    this.uid = lastDocument.uid + 1;
  } else {
    this.uid = 1;
  }
  next();
});

export const StoreCategory = model("StoreCategory", storeCategorySchema);
