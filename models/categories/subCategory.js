import { Schema, Types, model } from "mongoose";
import { ImageSchema } from "../common.model.js";

const subCategorySchema = new Schema(
  {
    uid: { type: Number, unique: true, index: true },
    category: { type: Types.ObjectId, ref: "Category" },
    name: { type: String },
    description: { type: String },
    image: ImageSchema,
    priority: { type: Number, default: 1 },
    active: { type: Boolean, default: true },
    createdBy: { type: Types.ObjectId, ref: "Admin", required: true },
    updatedBy: { type: Types.ObjectId, ref: "Admin" },
  },
  { timestamps: true }
);
// Define a pre-save hook to generate the value for the "uid" field
subCategorySchema.pre("save", async function (next) {
  if (!this.isNew) {
    return next();
  }
  const lastDocument = await this.constructor.findOne().sort({ _id: -1 });
  if (lastDocument) {
    this.uid = lastDocument.uid + 1;
  } else {
    this.uid = 1;
  }
  next();
});

export const SubCategory = model("SubCategory", subCategorySchema);
