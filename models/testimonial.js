import mongoose from "mongoose";
import { ImageSchema } from "./common.model.js";

const testimonialSchema = new mongoose.Schema(
  {
    uid: { type: Number, unique: true, index: true },
    reviewerName: { type: String, required: true },
    reviewerAvatar: ImageSchema,
    rating: { type: Number, enum: [1, 2, 3, 4, 5], default: 1, required: true },
    review: { type: String },
    active: { type: Boolean, default: true },
  },
  { timestamps: true }
);

// Define a pre-save hook to generate the value for the "uid" field
testimonialSchema.pre("save", async function (next) {
  if (!this.isNew) {
    return next();
  }
  const lastDocument = await this.constructor.findOne().sort({ _id: -1 });
  if (lastDocument) {
    this.uid = lastDocument.uid + 1;
  } else {
    this.uid = 1;
  }
  next();
});

export const Testimonial = mongoose.model("Testimonial", testimonialSchema);
