import { Schema, Types, model } from "mongoose";
import { ImageSchema } from "../common.model.js";

const mobileStorySchema = new Schema(
  {
    uid: { type: Number, unique: true, index: true },
    image: ImageSchema,
    duration: { type: Number, default: 1000 },
    title: { type: String },
    description: { type: String },
    buttonText: { type: String },
    redirectUrl: { type: String, required: true },
    expiryDate: { type: Date, required: true },
    createdBy: { type: Types.ObjectId, required: true, ref: "Admin" },
    updatedBy: { type: Types.ObjectId, ref: "Admin" },
    store: { type: Types.ObjectId, required: true, ref: "Store" },
    active: { type: Boolean, default: true },
  },
  { timestamps: true }
);

// Define a pre-save hook to generate the value for the "uid" field
mobileStorySchema.pre("save", async function (next) {
  if (!this.isNew) {
    return next();
  }
  const lastDocument = await this.constructor.findOne().sort({ _id: -1 });
  if (lastDocument) {
    this.uid = lastDocument.uid + 1;
  } else {
    this.uid = 1;
  }
  next();
});

export const MobileStory = model("MobileStory", mobileStorySchema);
