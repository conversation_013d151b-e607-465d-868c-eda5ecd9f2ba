import { Schema, Types, model } from "mongoose";
import { ImageSchema } from "../common.model.js";

const bannerSchema = new Schema(
  {
    uid: { type: Number, unique: true, index: true },
    mobileBanner: ImageSchema,
    desktopBanner: ImageSchema,
    redirectUrl: { type: String },
    termsContent: { type: String, required: true, default: "en" },
    termsTitle: { type: String, required: true, default: "en" },
    expiryDate: { type: Date, required: true },
    createdBy: { type: Types.ObjectId, required: true, ref: "Admin" },
    updatedBy: { type: Types.ObjectId, ref: "Admin" },
    active: { type: Boolean, default: true },
    type: { type: String, enum: ["context", "giftCard"] },
    priority: { type: Number, default: 1 },
  },
  { timestamps: true }
);

// Define a pre-save hook to generate the value for the "uid" field
bannerSchema.pre("save", async function (next) {
  if (!this.isNew) {
    return next();
  }
  const lastDocument = await this.constructor.findOne().sort({ _id: -1 });
  if (lastDocument) {
    this.uid = lastDocument.uid + 1;
  } else {
    this.uid = 1;
  }
  next();
});

export const Banner = model("Banner", bannerSchema);
