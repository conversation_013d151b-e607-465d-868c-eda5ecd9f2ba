import { model, Schema, Types } from "mongoose";
import { ImageSchema } from "./common.model.js";

const quickAccessSchema = new Schema(
  {
    uid: { type: Number, unique: true, index: true },
    title: { type: String },
    redirectUrl: { type: String },
    icon: ImageSchema,
    createdBy: { type: Types.ObjectId, ref: "Admin" },
    updatedBy: { type: Types.ObjectId, ref: "Admin" },
    active: { type: Boolean, default: true },
  },
  { timestamps: true }
);

// Define a pre-save hook to generate the value for the "uid" field
quickAccessSchema.pre("save", async function (next) {
  if (!this.isNew) {
    return next();
  }
  const lastDocument = await this.constructor.findOne().sort({ _id: -1 });
  if (lastDocument) {
    this.uid = lastDocument.uid + 1;
  } else {
    this.uid = 1;
  }
  next();
});

export const QuickAccess = model("QuickAccess", quickAccessSchema);
