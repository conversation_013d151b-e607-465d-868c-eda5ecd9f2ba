import { Schema, Types, model } from "mongoose";
import validator from "validator";
import { AssignUniqueID } from "../../utils/uniqueCodeGenerator.js";
import { ImageSchema } from "../common.model.js";

const userSchema = new Schema(
  {
    uid: { type: Number, unique: true, index: true },
    name: { type: String },
    avatar: ImageSchema,
    email: {
      type: String,
      validate: [validator.isEmail, "Please enter valid email address"],
      required: true,
    },
    mobile: {
      type: Number,
      unique: true,
      sparse: true,
      default: null,
      index: true,
      minlength: [10, "Please enter a valid phone number"],
    },
    referral: { type: Types.ObjectId, ref: "User" },
    referralCode: { type: String },
    fbUserId: { type: String },
    googleId: { type: String },
    address: { type: String },
    postcode: { type: String },
    userNotes: { type: String },
    dateRegistered: { type: Date, default: new Date() },
    lastLoggedDate: { type: Date },
    ambassador: { type: String },
    rcBlock: { type: Boolean, default: false },
    balance: { type: Number, default: 0, required: true },
    totalEarned: { type: Number, default: 0, required: true },
    pendingBalance: { type: Number, default: 0, required: true },
    giftCardBalance: { type: Number, default: 0, required: true },
    rewardPoints: { type: Number, default: 0, required: true },
    pendingRewardPoints: { type: Number, default: 0, required: true },
    specialCampaign: { type: String },
    campaignDone: { type: String },
    isWithdrawReminded: { type: String },
    withdrawRemindDate: { type: Date },
    safeUser: { type: String },
    mobileVerified: { type: Boolean, default: false },
    sendNotification: { type: Boolean, default: false },
    updatedBy: { type: Types.ObjectId, ref: "Admin" },
    personalInterest: [
      {
        type: Types.ObjectId,
        ref: "PersonalInterest",
        _id: false,
      },
    ],
    status: {
      type: String,
      default: "inactive",
      enum: ["inactive", "active", "blocked"],
    },
    notes: { type: String },
    migrated: { type: Boolean, default: false },
  },
  { timestamps: true }
);

// Define a pre-save hook to generate the value for the "uid" field and encrypting password before saving user
userSchema.pre("save", async function (next) {
  if (!this.isNew) {
    return next();
  }
  const lastDocument = await this.constructor.findOne().sort({ _id: -1 });
  if (lastDocument) {
    this.uid = lastDocument.uid + 1;
  } else {
    this.uid = 1;
  }
  this.referralCode = await AssignUniqueID();
  if (this.mobile === null) {
    this.mobile = undefined;
  }
  next();
});

export const User = model("User", userSchema);
