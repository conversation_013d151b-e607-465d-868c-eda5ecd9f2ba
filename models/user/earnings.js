import { Schema, model, Types } from 'mongoose'
import { hash } from 'ohash'

const earningSchema = new Schema(
	{
		uid: { type: Number, unique: true, index: true },
		referenceId: { type: String, unique: true, index: true },
		store: { type: Types.ObjectId, required: false, ref: 'Store' },
		offer: { type: Types.ObjectId, ref: 'Offer' },
		user: { type: Types.ObjectId, required: false, ref: 'User' },
		referralUser: { type: Types.ObjectId, ref: 'User' }, // Referral user who referred the user
		storeCategory: {
			type: Types.ObjectId,
			ref: 'StoreCategory',
		},
		affiliation: {
			type: Types.ObjectId,
			ref: 'Affiliation',
		},
		cashbackAmount: { type: Number, default: 0 },
		referralCommission: { type: Number, default: 0 },
		amountGot: { type: Number, default: 0 },
		amountPromised: { type: Number, default: 0 },
		saleAmount: { type: Number, default: 0 },
		percentGot: { type: Number, default: 0 },
		notes: { type: String },
		orderCount: { type: Number },
		advertiserInfo: { type: String },
		otherInfo: { type: String },
		remarks: { type: String },
		status: {
			type: String,
			default: 'pending',
			enum: [
				'pending',
				'tracked_for_confirm',
				'tracked_for_cancel',
				'confirmed',
				'cancelled',
			],
		},
		earningsType: {
			type: String,
			enum: ['click', 'missing', 'referral'],
			default: 'click',
			required: true,
		},
		rewardPoint: { type: Boolean, default: false, required: true },
		click: { type: Types.ObjectId, required: false, ref: 'Click' },
		orderUniqueId: { type: String },
		offerFromPartner: { type: String },
		categoryPercentOrAmount: { type: String },
		confirmDate: { type: Date },
		dateConfirmedCancelled: { type: Date },
		autoUpdated: { type: Boolean, default: false },
		trackingTime: { type: Date },
		createdBy: { type: Types.ObjectId, ref: 'Admin' },
		updatedBy: { type: Types.ObjectId, ref: 'Admin' },
		migrated: { type: Boolean, default: false },
		oldId: { type: Number },
		purchaseDate: { type: Date },
		clickDate: { type: Date },
		isShareAndEarn: { type: Boolean, default: false },
	},
	{ timestamps: true },
)

earningSchema.pre('save', async function (next) {
	if (!this.isNew) {
		return next()
	}

	// Find the document with the highest UID
	const maxUidDocument = await this.constructor
		.findOne()
		.sort({ uid: -1 }) // Sort by UID in descending order
		.select('uid') // Only retrieve the UID field for efficiency

	// If a document exists, increment the UID by 1, otherwise set it to 1
	if (maxUidDocument) {
		this.uid = maxUidDocument.uid + 1
	} else {
		this.uid = 1
	}

	console.table({
		uid: this.uid,
		orderUniqueId: this.orderUniqueId,
	})

	// Generate the referenceId based on the new uid and other fields
	this.referenceId = `CBERN${hash({
		uid: this.uid,
		user: this.user,
		store: this.store,
		affiliation: this.affiliation,
		createdBy: this.createdBy,
	}).toUpperCase()}`

	// Set the initial status to "pending"
	this.status = 'pending'

	next()
})

// Hook to set autoUpdated to false on update
// earningSchema.pre(['findOneAndUpdate', 'updateMany'], function(next) {
//     this.set({ autoUpdated: false });
//     next();
// });
//
export const Earnings = model('Earning', earningSchema)

// Earnings.syncIndexes();
