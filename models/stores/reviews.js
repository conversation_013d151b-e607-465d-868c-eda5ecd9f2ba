import mongoose, { Types, Schema } from "mongoose";
import { model } from "mongoose";

const reviewSchema = new Schema(
  {
    uid: { type: Number, unique: true, index: true },
    store: { type: Types.ObjectId, required: true, ref: "Store" },
    reviewer: { type: Types.ObjectId, required: true, ref: "User" },
    rating: { type: Number, default: 1, enum: [1, 2, 3, 4, 5], required: true },
    status: {
      type: String,
      default: "awaiting_to_approve",
      enum: ["approved", "awaiting_to_approve", "rejected"],
      required: true,
    },
    review: { type: String },
    active: { type: Boolean, default: false },
  },
  { timestamps: true }
);

reviewSchema.pre("save", async function (next) {
  if (!this.isNew) {
    return next();
  }
  const lastDocument = await this.constructor.findOne().sort({ _id: -1 });
  if (lastDocument) {
    this.uid = lastDocument.uid + 1;
  } else {
    this.uid = 1;
  }
  next();
});

export const StoreReviews = model("Review", reviewSchema);
