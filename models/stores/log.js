import mongoose from "mongoose";

const storeLogSchema = new mongoose.Schema(
  {
    uid: { type: Number, unique: true, index: true },
    updateBy: {
      type: mongoose.Schema.ObjectId,
      ref: "admins",
      required: true,
    },
    store: { type: mongoose.Schema.ObjectId, ref: "stores" },
    offer: { type: mongoose.Schema.ObjectId, ref: "offers" },
    oldContent: { type: String },
    updatedContent: { type: String, required: true },
  },
  { timestamps: true }
);

// Define a pre-save hook to generate the value for the "uid" field
storeLogSchema.pre("save", async function (next) {
  if (!this.isNew) {
    return next();
  }
  const lastDocument = await this.constructor.findOne().sort({ _id: -1 });
  if (lastDocument) {
    this.uid = lastDocument.uid + 1;
  } else {
    this.uid = 1;
  }
  next();
});

export const StoreLog = mongoose.model("storelog", storeLogSchema);
