import mongoose, { Types, model } from "mongoose";
import { CategorySchema, ImageSchema } from "../common.model.js";

const storeSchema = new mongoose.Schema(
  {
    uid: { type: Number, unique: true, index: true },
    name: { type: String, required: true, unique: true },
    logo: ImageSchema,
    banner: ImageSchema,
    offerWarning: { type: String },
    categories: [CategorySchema],
    affiliateLink: {
      type: String,
      // required: true
    },
    affiliation: {
      type: Types.ObjectId,
      ref: "Affiliation",
      // required: true
    },
    campaignType: { type: String },
    storeOffer: { type: String },
    relatedStores: [{ type: Types.ObjectId, ref: "Store" }],
    minimumAmount: { type: Number, default: 0, required: true },
    maximumAmount: { type: Number, default: 0, required: true },
    description: { type: String },
    detailedDescription: { type: String },
    reliability: { type: Number },
    trackable: { type: Boolean, default: false },
    priority: { type: Number, default: 0 },
    autoCheck: { type: Boolean, default: true },
    homeOffer: { type: String },
    isSpecial: {
      type: String,
      enum: ["none", "special", "new", "hight", "100%"],
      default: "none",
    },
    storeWarning: { type: String },
    storeTopWarning: { type: String },
    topWarningLink: { type: String },
    topWarningShowInactive: { type: String },
    warningType: { type: String, default: "all" },
    storeHowToGet: { type: String, required: true },
    storeTerms: { type: String, required: true },
    noAppSaleCategory: { type: Boolean, default: false },
    noMobileWebSaleCategory: { type: Boolean, default: false },
    noDesktopWebSaleCategory: { type: Boolean, default: false },
    giftCard: { type: Types.ObjectId, ref: "GiftCard" },
    instantStore: { type: Types.ObjectId, ref: "Store" },
    cashbackAmount: { type: Number, default: 0 },
    cashbackPercent: { type: Number, default: 0 },
    offerType: {
      type: String,
      enum: ["flat", "upto"],
      default: "upto",
    },
    cashbackType: {
      type: String,
      enum: ["reward", "cashback"],
      default: "cashback",
    },
    isInstant: { type: Boolean, default: false },
    deepLinkEnable: { type: Boolean, default: false },
    trackingTime: { type: String, required: true },
    confirmationTime: { type: String, required: true },
    importantPoints: { type: String },

    missingAccepted: { type: Boolean, default: false },
    bgColor: { type: String, required: true, default: "#FFFFFF" },
    trending: { type: Boolean, default: false },
    ratesTotal: { type: Number },
    ratesCount: { type: Number },
    affiliateOfferId: { type: String },
    active: { type: Boolean, default: true },
    isDeleted: { type: Boolean, default: false },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: "Admin",
      // required: true
    },
    updatedBy: { type: mongoose.Types.ObjectId, ref: "Admin" },
  },
  { timestamps: true }
);

// Define a pre-save hook to generate the value for the "uid" field
storeSchema.pre("save", async function (next) {
  if (!this.isNew) {
    return next();
  }
  const lastDocument = await this.constructor.findOne().sort({ _id: -1 });
  if (lastDocument) {
    this.uid = lastDocument.uid + 1;
  } else {
    this.uid = 1;
  }
  next();
});

export const Store = model("Store", storeSchema);
