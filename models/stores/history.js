import mongoose from "mongoose";
import { ImageSchema } from "../common.model.js";

const storeCategoryHistorySchema = new mongoose.Schema(
  {
    uid: { type: Number, unique: true, index: true },
    storeCategoryUid: { type: Number },
    storeCategoryId: { type: mongoose.Types.ObjectId, ref: "StoreCategory" },
    store: { type: mongoose.Types.ObjectId, ref: "Store" },
    affiliation: { type: mongoose.Types.ObjectId, ref: "Affiliation" },
    name: { type: String },
    sectionCashback: { type: Number, default: 0 },
    newUserCb: { type: Number, default: 0 },
    gettingNewUserRate: { type: Number, default: 0 },
    gettingOldUserRate: { type: Number, default: 0 },
    givingType: { type: String, enum: ["amount", "percent"] },
    oldUserOfferAmount: { type: Number },
    newUserOfferAmount: { type: Number },
    oldUserOfferPercent: { type: Number },
    newUserOfferPercent: { type: Number },
    gettingType: { type: String, enum: ["amount", "percent"] },
    link: { type: String },
    sectionLink: { type: String },
    couponCode: { type: String },
    type: { type: String },
    device: { type: String, enum: ["app", "website"] },
    weeklyImage: ImageSchema,
    dateExpiry: { type: Date },
    notes: { type: String },
    active: { type: Boolean, default: true },
    createdBy: { type: mongoose.Types.ObjectId, ref: "Admin", required: true },
    updatedBy: { type: mongoose.Types.ObjectId, ref: "Admin" },
  },
  { timestamps: true }
);

// Define a pre-save hook to generate the value for the "uid" field
storeCategoryHistorySchema.pre("save", async function (next) {
  if (!this.isNew) {
    return next();
  }

  try {
    // Find the last document inserted, sorted by uid
    const lastDocument = await this.constructor.findOne().sort({ uid: -1 });

    if (lastDocument) {
      // If a document exists, increment its uid
      this.uid = lastDocument.uid + 1;
    } else {
      // If no documents exist, start the uid sequence from 1
      this.uid = 1;
    }

    next(); // Call the next middleware or save the document
  } catch (error) {
    // Handle any potential errors during the database query
    return next(error);
  }
});

export const StoreCategoryHistory = mongoose.model(
  "StoreCategoryHistory",
  storeCategoryHistorySchema
);
