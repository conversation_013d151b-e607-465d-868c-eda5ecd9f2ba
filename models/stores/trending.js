import mongoose from "mongoose";

const trendingSchema = new mongoose.Schema(
  {
    uid: { type: Number, unique: true, index: true },
    store: { type: mongoose.Types.ObjectId, required: true, ref: "Store" },
    createdBy: { type: mongoose.Types.ObjectId, required: true, ref: "Admin" },
    priority: { type: Number, default: 1 },
  },
  { timestamps: true }
);

trendingSchema.pre("save", async function (next) {
  if (!this.isNew) {
    return next();
  }
  const lastDocument = await this.constructor.findOne().sort({ _id: -1 });
  if (lastDocument) {
    this.uid = lastDocument.uid + 1;
    this.priority = lastDocument.priority + 1;
  } else {
    this.uid = 1;
  }
  next();
});

export const TrendingStore = mongoose.model("TrendingStore", trendingSchema);
