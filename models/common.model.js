import { Schema, Types } from "mongoose";

export const ImageSchema = new Schema({
  publicId: { type: String },
  secureUrl: { type: String },
});

export const FileSchema = new Schema({
  key: { type: String },
  location: { type: String },
});

export const CategorySchema = new Schema({
  category: { type: Types.ObjectId, ref: "Category" },
  subCategories: [{ type: Types.ObjectId, ref: "SubCategory" }],
  _id: false,
});
