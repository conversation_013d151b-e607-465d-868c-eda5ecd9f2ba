import { model, Types, Schema } from "mongoose";
import { CategorySchema, ImageSchema } from "../common.model.js";

const offerSchema = new Schema(
  {
    uid: { type: Number, unique: true, index: true },
    store: { type: Types.ObjectId, required: true, ref: "Store" }, //
    storeCategory: { type: Types.ObjectId, ref: "StoreCategory" }, //
    categories: [CategorySchema], //
    title: { type: String, required: true },
    userType: { type: String, enum: ["new", "existing", "both"] },
    offer: { type: String, required: true },
    discount: {
      type: Number,
      // required: true
    }, //
    itemPrice: { type: Number }, //
    url: { type: String },
    productImage: ImageSchema, //
    description: { type: String, required: true }, //
    affiliation: {
      type: Types.ObjectId,
      ref: "Affiliation",
      required: true,
    }, //
    couponCode: { type: String }, //
    isCoupon: { type: Boolean, default: false }, //
    link: { type: String, required: true }, //
    offerPercent: { type: Number, min: 0, max: 100 }, //
    offerAmount: { type: Number }, //
    howToGet: { type: String }, //
    terms: { type: String, required: true }, //
    priority: { type: Number, default: 0, index: true },
    trendingPriority: { type: Number, default: 0, index: true },
    repeatBy: { type: String }, //
    stockEnds: { type: Boolean, default: false, required: true }, //
    dateExpiry: { type: Date, index: true, required: true }, //
    dateStart: { type: Date, required: true }, //
    importantUpdate: { type: String }, //
    keySpecs: { type: String }, //
    offerType: { type: String, enum: ["flat", "upto"], default: "upto" },
    visibility: { type: Boolean, default: false },
    missedDeal: { type: Boolean, default: false },
    trending: { type: Boolean, default: false },
    active: { type: Boolean, default: true },
    createdBy: { type: Types.ObjectId, ref: "Admin", required: true },
    updatedBy: { type: Types.ObjectId, ref: "Admin" },
    migrated: { type: Boolean, default: false },
    isAutoGenerated: { type: Boolean, default: false },
    hideCbTag: { type: Boolean, default: false },
  },
  { timestamps: true }
);
offerSchema.index({
  priority: -1,
  dateExpiry: -1,
});
// Define a pre-save hook to generate the value for the "uid" field
offerSchema.pre("save", async function (next) {
  if (!this.isNew) {
    return next();
  }
  const lastDocument = await this.constructor.findOne().sort({ _id: -1 });
  if (lastDocument) {
    this.uid = lastDocument.uid + 1;
  } else {
    this.uid = 1;
  }
  next();
});

export const Offer = model("Offer", offerSchema);
