import { Schema, Types, model } from "mongoose";
import { ImageSchema } from "../common.model.js";

const onGoingSalesSchema = new Schema(
    {
        uid: { type: Number, unique: true, index: true },
        saleName: { type: String, required: true },
        saleStartDate: { type: Date, required: true },
        saleEndDate: { type: Date, required: true },
        saleLogo: ImageSchema,
        active: { type: Boolean, default: true },
        offers: [
            {
                type: Types.ObjectId,
                required: true,
                ref: "Offer",
            },
        ],
        createdBy: { type: Types.ObjectId, required: true, ref: "Admin" },
        updatedBy: { type: Types.ObjectId, ref: "Admin" },
    },
    { timestamps: true }
);

onGoingSalesSchema.pre("save", async function(next) {
    if (!this.isNew) {
        return next();
    }
    const lastDocument = await this.constructor.findOne().sort({ _id: -1 });
    if (lastDocument) {
        this.uid = lastDocument.uid + 1;
        this.priority = lastDocument.priority + 1;
    } else {
        this.uid = 1;
    }
    next();
});

export const OnGoingSaleOffers = model("OngoingSaleOffers", onGoingSalesSchema);
