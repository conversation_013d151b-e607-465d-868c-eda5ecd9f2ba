import mongoose, { Schema, Types } from 'mongoose'

const clickSchema = new Schema(
	{
		uid: { type: Number, unique: true, index: true },
		referenceId: { type: String, unique: true, required: true, index: true },
		user: { type: Types.ObjectId, ref: 'User' },
		storeCategory: { type: Types.ObjectId, ref: 'StoreCategory' },
		offer: { type: Types.ObjectId, ref: 'Offer' },
		store: { type: Types.ObjectId, ref: 'Store' },
		affiliation: {
			type: Types.ObjectId,
			ref: 'Affiliation',
			required: true,
		},
		userIp: { type: String },
		userCity: { type: String },
		title: { type: String, required: true },
		url: { type: String, required: true },
		type: { type: String, enum: ['offer', 'express', 'rates'], required: true },
		device: { type: String, enum: ['mobile', 'desktop', 'tablet', 'others'] },
		status: {
			type: String,
			enum: ['clicked', 'tracked', 'confirmed', 'cancelled'],
			default: 'clicked',
			required: true,
		},
		link: {
			type: Types.ObjectId,
			ref: 'Link',
			required: false,
		},
		orderIds: { type: [String], default: [] },
	},
	{ timestamps: true },
)

clickSchema.pre('save', async function (next) {
	if (!this.isNew) {
		return next()
	}
	const lastDocument = await this.constructor.findOne().sort({ _id: -1 })
	if (lastDocument) {
		this.uid = lastDocument.uid + 1
	} else {
		this.uid = 1
	}
	next()
})

export const Clicks = mongoose.model('Click', clickSchema)
