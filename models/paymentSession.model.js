import { Types, model } from "mongoose";
import { Schema } from "mongoose";

const paymentSessionScehama = new Schema(
  {
    user: { type: Types.ObjectId, ref: "User", required: true },
    lockedBalance: { type: Number },
    expiryDate: {
      type: Date,
      required: true,
      default: new Date(new Date().getTime() + 3 * 60000),
    },
    status: {
      type: String,
      enum: ["active", "completed", "cancelled", "expired"],
      default: "active",
      required: true,
    },
  },
  {
    timestamps: true,
  }
);

export const PaymentSession = model("PaymentSession", paymentSessionScehama);
