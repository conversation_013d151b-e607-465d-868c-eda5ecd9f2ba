import mongoose from "mongoose";

const middlewareSchema = new mongoose.Schema(
  {
    uid: { type: Number, unique: true, index: true },
    level: { type: Number, required: true, default: 1, unique: true },
    name: { type: String, required: true, unique: true },
    createdBy: { type: mongoose.Types.ObjectId, ref: "Admin", required: true },
    updatedBy: { type: mongoose.Types.ObjectId, ref: "Admin" },
    features: [{ type: String }],
    active: { type: Boolean, default: true },
  },
  { timestamps: true }
);

// Define a pre-save hook to generate the value for the "uid" field
middlewareSchema.pre("save", async function (next) {
  if (!this.isNew) {
    return next();
  }
  const lastDocument = await this.constructor.findOne().sort({ _id: -1 });
  if (lastDocument) {
    this.uid = lastDocument.uid + 1;
  } else {
    this.uid = 1;
  }
  next();
});

export const Middleware = mongoose.model("Middleware", middlewareSchema);
