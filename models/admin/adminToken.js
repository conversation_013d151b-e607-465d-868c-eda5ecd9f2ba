import { Schema, model } from 'mongoose'


const adminTokenSchema = new Schema({
	admin: {
		type: Schema.Types.ObjectId,
		required: true, rer: "Admin"
	},
	token: {
		type: String,
		required: true,
	},
	createdAt: {
		type: Date,
		default: Date.now,
		expires: 30 * 86400, // 30 days
	},
	session: {
		type: Boolean,
		default: true,
	},
})

const AdminToken = model('AdminToken', adminTokenSchema)

export default AdminToken
