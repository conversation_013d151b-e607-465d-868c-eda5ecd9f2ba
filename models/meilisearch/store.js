export class StoreSchema {
  constructor({
    id,
    uid,
    active,
    url,
    name,
    description,
    detailedDescription,
    offerWarning,
    giftCard,
    categories,
    subcategories,
    storecategories,
  }) {
    this.type = "store"; // Hardcoded to ensure the type property is correctly set
    this.id = id;
    this.uid = uid;
    this.active = active;
    this.url = url
    this.name = name;
    this.description = description;
    this.detailedDescription = detailedDescription;
    this.offerWarning = offerWarning;
    this.giftCard = giftCard; // Expecting { name: string }
    this.categories = categories; // Expecting [{ name: string }]
    this.subcategories = subcategories; // Expecting [{ name: string }]
    this.storecategories = storecategories; // Expecting [{ name: string }]
  }
}
