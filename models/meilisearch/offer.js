export class OfferSchema {
  constructor({
    id,
    uid,
    active,
    title,
    url,
    offerType,
    description,
    couponCode,
    offerPercent,
    offerAmount,
    offerWarning,
    store,
    dateExpiry,
    categories,
    subcategories,
    storecategories,
    newUserOffer,
    oldUserOffer,
  }) {
    this.type = "offer"; // Hardcoded to ensure the type property is correctly set
    this.id = id;
    this.uid = uid;
    this.active = active;
    this.title = title;
    this.url = url;
    this.description = description;
    this.offerPercent = offerPercent;
    this.offerAmount = offerAmount;
    this.offerWarning = offerWarning;
    this.store = store; // Expecting { name: string }
    this.categories = categories; // Expecting [{ name: string }]
    this.subcategories = subcategories; // Expecting [{ name: string }]
    this.storecategories = storecategories; // Expecting [{ name: string }]
    this.couponCode = couponCode;
    this.offerType = offerType;
    this.dateExpiry = dateExpiry;
    this.newUserOffer = newUserOffer;
    this.oldUserOffer = oldUserOffer;
  }
}
