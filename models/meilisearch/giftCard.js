export class GiftCardSchema {
  constructor({
    id,
    uid,
    url,
    active,
    name,
    description,
    cashbackGiving,
    relatedInstantStore,
    categories,
    subcategories,
    storecategories,
  }) {
    this.type = "giftCard"; // Hardcoded to ensure the type property is correctly set
    this.id = id;
    this.uid = uid;
    this.url = url
    this.active = active;
    this.name = name;
    this.description = description;
    this.cashbackGiving = cashbackGiving;
    this.relatedInstantStore = relatedInstantStore; // Expecting { name: string }
    this.categories = categories; // Expecting [{ name: string }]
    this.subcategories = subcategories; // Expecting [{ name: string }]
    this.storecategories = storecategories; // Expecting [{ name: string }]
  }
}
