import { Router } from "express";
import { IcbGiftCardController } from "../controllers/icbGiftCardOrderController.js";
import {
  isAdmin,
  authorizedAdminAccess,
} from "../middlewares/authMiddleware.js";
import { GET_ALL_ICB_GIFTCARDS } from "../utils/constants.js";

const router = Router();

const { getAllIcbGiftCardOrders } = new IcbGiftCardController();

router
  .route("/all-orders")
  .get(
    isAdmin,
    authorizedAdminAccess(GET_ALL_ICB_GIFTCARDS),
    getAllIcbGiftCardOrders
  );

export default router;
