import express from "express";
import { TempController } from "../controllers/tempController.js";
// import { isAdmin } from "../middlewares/authMiddleware.js"; // Assuming admin access is required - Comment out or remove if not used elsewhere

const router = express.Router();
const { getEarningsWithFlipkartReward } = new TempController();

// Route for the new endpoint
router.route("/earnings-flipkart-reward").post(getEarningsWithFlipkartReward);

export default router;
