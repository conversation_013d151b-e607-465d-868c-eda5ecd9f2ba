import { Router } from "express";
import { MobileStoryController } from "../../controllers/banners/mobileStoryController.js";
import {
  isAdmin,
  authorizedAdminAccess,
} from "../../middlewares/authMiddleware.js";
import { validate } from "../../validations/validate.js";
import {
  createMobileStoryVal,
  updateMobileStoryParamsVal,
  updateMobileStoryVal,
} from "../../validations/mobileStory.validation.js";
import {
  CREATE_STORIES,
  DELETE_STORIES,
  GET_ALL_STORIES,
  UPDATE_STORIES,
} from "../../utils/constants.js";

const router = Router();
const {
  createMobileStory,
  getAllMobileStories,
  updateMobileStory,
  updateMobileStoryActiveStatus,
  deleteMobileStory,
  getStoryDetails,
} = new MobileStoryController();

router
  .route("/create")
  .post(
    validate(createMobileStoryVal),
    isAdmin,
    authorizedAdminAccess(CREATE_STORIES),
    createMobileStory
  );
router
  .route("/get-all-stories")
  .get(isAdmin, authorizedAdminAccess(GET_ALL_STORIES), getAllMobileStories);
router
  .route("/update/:storyId")
  .put(
    validate(updateMobileStoryVal),
    isAdmin,
    authorizedAdminAccess(UPDATE_STORIES),
    updateMobileStory
  );
router
  .route("/details/:storyId")
  .post(
    validate(updateMobileStoryParamsVal),
    isAdmin,
    authorizedAdminAccess(GET_ALL_STORIES),
    getStoryDetails
  );
router
  .route("/update-status/:storyId")
  .patch(
    validate(updateMobileStoryParamsVal),
    isAdmin,
    authorizedAdminAccess(UPDATE_STORIES),
    updateMobileStoryActiveStatus
  );
router
  .route("/delete/:storyId")
  .delete(
    validate(updateMobileStoryParamsVal),
    isAdmin,
    authorizedAdminAccess(DELETE_STORIES),
    deleteMobileStory
  );

export default router;
