import { Router } from "express";
import { BannerController } from "../../controllers/banners/bannerController.js";
import {
    isAdmin,
    authorizedAdminAccess,
} from "../../middlewares/authMiddleware.js";
import { validate } from "../../validations/validate.js";
import {
    createBannerVal,
    updateBannerParamsVal,
    updateBannerPriorityVal,
} from "../../validations/banner.validation.js";
import {
    CREATE_BANNER,
    DELETE_BANNER,
    GET_ALL_BANNERS,
    UPDATE_BANNER,
} from "../../utils/constants.js";

const router = Router();

const {
    createBanner,
    getAllBanners,
    deleteBanner,
    updateBanner,
    updateBannerPriority,
    updateBannerActiveStatus,
    getBannerDetails,
} = new BannerController();

router
    .route("/create")
    .post(
        validate(createBannerVal),
        isAdmin,
        authorizedAdminAccess(CREATE_BANNER),
        createBanner
    );
router
    .route("/get-all-banners")
    .get(isAdmin, authorizedAdminAccess(GET_ALL_BANNERS), getAllBanners);
router
    .route("/update/:bannerId")
    .put(isAdmin, authorizedAdminAccess(UPDATE_BANNER), updateBanner);
router
    .route("/details/:bannerId")
    .post(
        validate(updateBannerParamsVal),
        isAdmin,
        authorizedAdminAccess(GET_ALL_BANNERS),
        getBannerDetails
    );
router
    .route("/update-status/:bannerId")
    .patch(
        validate(updateBannerParamsVal),
        isAdmin,
        authorizedAdminAccess(UPDATE_BANNER),
        updateBannerActiveStatus
    );
router
    .route("/delete/:bannerId")
    .delete(
        validate(updateBannerParamsVal),
        isAdmin,
        authorizedAdminAccess(DELETE_BANNER),
        deleteBanner
    );

router
    .route("/update-priority/:bannerId")
    .patch(
        validate(updateBannerPriorityVal),
        isAdmin,
        authorizedAdminAccess(UPDATE_BANNER),
        updateBannerPriority
    );



export default router;
