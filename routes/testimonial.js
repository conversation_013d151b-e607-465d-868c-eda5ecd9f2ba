import { Router } from "express";
import {
  authorizedAdminAccess,
  isAdmin,
} from "../middlewares/authMiddleware.js";
import { TestimonialController } from "../controllers/testimonialController.js";
import { validate } from "../validations/validate.js";
import {
  createTestimonialVal,
  updateTestimonialVal,
  updateTestimonialWithParamsVal,
} from "../validations/testimonial.validation.js";

import {
  CREATE_TESTIMONIALS,
  GET_ALL_TESTIMONIALS,
  DELETE_TESTIMONIALS,
  UPDATE_TESTIMONIALS,
} from "../utils/constants.js";

const router = Router();

const {
  createTestimonial,
  getAllTestimonials,
  getTestimonialDetails,
  updateTestimonials,
  updateTestimonialActiveStatus,
} = new TestimonialController();

router
  .route("/create")
  .post(
    validate(createTestimonialVal),
    isAdmin,
    authorizedAdminAccess(CREATE_TESTIMONIALS),
    createTestimonial
  );
router
  .route("/all-testimonials")
  .get(
    isAdmin,
    authorizedAdminAccess(GET_ALL_TESTIMONIALS),
    getAllTestimonials
  );
router
  .route("/details/:testimonialId")
  .post(
    isAdmin,
    authorizedAdminAccess(GET_ALL_TESTIMONIALS),
    getTestimonialDetails
  );
router
  .route("/update/:testimonialId")
  .put(
    validate(updateTestimonialVal),
    isAdmin,
    authorizedAdminAccess(UPDATE_TESTIMONIALS),
    updateTestimonials
  );
router
  .route("/update-status/:testimonialId")
  .patch(
    validate(updateTestimonialWithParamsVal),
    isAdmin,
    authorizedAdminAccess(DELETE_TESTIMONIALS),
    updateTestimonialActiveStatus
  );

export default router;
