import { Router } from "express";
import { QuickAccessController } from "../controllers/quickAccessController.js";
import {
  isAdmin,
  authorizedAdminAccess,
} from "../middlewares/authMiddleware.js";
import {
  CREATE_QUICK_ACCESS,
  DELETE_QUICK_ACCESS,
  GET_ALL_QUICK_ACCESS,
  UPDATE_QUICK_ACCESS,
} from "../utils/constants.js";

const router = Router();

const {
  createQuickAccess,
  getAllQuickAccess,
  getQuickAccessDetails,
  updateActiveStatusQuickAccess,
  updateQuickAccess,
  deleteQuickAccess,
} = new QuickAccessController();

router
  .route("/create")
  .post(isAdmin, authorizedAdminAccess(CREATE_QUICK_ACCESS), createQuickAccess);
router
  .route("/get-all-quick-access")
  .get(isAdmin, authorizedAdminAccess(GET_ALL_QUICK_ACCESS), getAllQuickAccess);
router
  .route("/details/:quickAccessId")
  .post(
    isAdmin,
    authorizedAdminAccess(GET_ALL_QUICK_ACCESS),
    getQuickAccessDetails
  );
router
  .route("/update/:quickAccessId")
  .put(isAdmin, authorizedAdminAccess(UPDATE_QUICK_ACCESS), updateQuickAccess);
router
  .route("/update-status/:quickAccessId")
  .patch(
    isAdmin,
    authorizedAdminAccess(UPDATE_QUICK_ACCESS),
    updateActiveStatusQuickAccess
  );
router
  .route("/delete/:quickAccessId")
  .delete(
    isAdmin,
    authorizedAdminAccess(DELETE_QUICK_ACCESS),
    deleteQuickAccess
  );

export default router;
