import { Router } from "express";
import { StoreController } from "../../controllers/stores/storeController.js";
import {
  authorizedAdminAccess,
  isAdmin,
} from "../../middlewares/authMiddleware.js";
import {
  CREATE_STORE,
  UPDATE_STORE,
  VIEW_STORE,
} from "../../utils/constants.js";
import {
  createStoreVal,
  updateStoreIsSpecialVal,
  updateStorePriorityVal,
  updateStoreVal,
  updateStoreWithParamsVal,
} from "../../validations/store.validation.js";
import { validate } from "../../validations/validate.js";

const router = Router();

const {
  createStore,
  getAllStores,
  getAllStoresList,
  getStoreDetails,
  updateAutoCheckStatus,
  updateStatus,
  updateIsSpecialLimit,
  updateStore,
  updateStoreActiveStatus,
  updateStoreDeepLinkStatus,
  updateStorePriority,
  updateStoreDeleteStatus,
} = new StoreController();
router
  .route("/create")
  .post(
    validate(createStoreVal),
    isAdmin,
    authorizedAdminAccess(CREATE_STORE),
    createStore
  );

router
  .route("/details/:storeId")
  .post(
    validate(updateStoreWithParamsVal),
    isAdmin,
    authorizedAdminAccess(VIEW_STORE),
    getStoreDetails
  );

router
  .route("/all-stores")
  .get(isAdmin, authorizedAdminAccess(VIEW_STORE), getAllStores);

router
  .route("/update/auto-check/:storeId")
  .patch(
    validate(updateStoreWithParamsVal),
    isAdmin,
    authorizedAdminAccess(VIEW_STORE),
    updateAutoCheckStatus
  );

router
  .route("/:storeId/priority")
  .patch(
    validate(updateStorePriorityVal),
    isAdmin,
    authorizedAdminAccess(VIEW_STORE),
    updateStorePriority
  );

router
  .route("/delete/:storeId")
  .patch(
    validate(updateStoreWithParamsVal),
    isAdmin,
    authorizedAdminAccess(VIEW_STORE),
    updateStoreDeleteStatus
  );

router
  .route("/deep-link/:storeId")
  .patch(
    validate(updateStoreWithParamsVal),
    isAdmin,
    authorizedAdminAccess(VIEW_STORE),
    updateStoreDeepLinkStatus
  );

router
  .route("/status/:storeId")
  .patch(
    validate(updateStoreWithParamsVal),
    isAdmin,
    authorizedAdminAccess(VIEW_STORE),
    updateStatus
  );

router
  .route("/is-special/:storeId")
  .patch(
    validate(updateStoreIsSpecialVal),
    isAdmin,
    authorizedAdminAccess(VIEW_STORE),
    updateIsSpecialLimit
  );

router.route("/all-store-list").post(isAdmin, getAllStoresList);

router
  .route("/update/:storeId")
  .put(
    validate(updateStoreVal),
    isAdmin,
    authorizedAdminAccess(UPDATE_STORE),
    updateStore
  );

// router
//   .route("/filter")
//   .post(isAdmin, authorizedAdminAccess(VIEW_STORE), filterStores);

// router.route("/all-stores").get(isAdmin, allStores);
// router
//   .route("/search")
//   .post(isAdmin, authorizedAdminAccess(VIEW_STORE), searchStores);
export default router;
