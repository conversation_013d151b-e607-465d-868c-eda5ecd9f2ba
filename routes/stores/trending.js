import { Router } from 'express'
import { TrendingStoreController } from '../../controllers/stores/trendingController.js'
import {
    authorizedAdminAccess,
    isAdmin,
} from '../../middlewares/authMiddleware.js'
import {
    CREATE_TRENDING_STORE,
    DELETE_TRENDING_STORES,
    UPDATE_TRENDING_STORE,
    VIEW_ALL_TRENDING_STORES,
} from '../../utils/constants.js'
import {
    createTrendingStoreVal,
    updateTrendingStorePriorityVal,
    updateTrendingStoreWithParamsVal,
} from '../../validations/trendingStore.validation.js'
import { validate } from '../../validations/validate.js'

const router = Router()
const {
    createTrendingStore,
    updateTrendingStoresPriority,
    getAllTrendingStores,
    deleteTrendingStore,
} = new TrendingStoreController()
router
    .route('/create')
    .post(
        validate(createTrendingStoreVal),
        isAdmin,
        authorizedAdminAccess(CREATE_TRENDING_STORE),
        createTrendingStore,
    )
router
    .route('/all-trending-stores')
    .get(
        isAdmin,
        authorizedAdminAccess(VIEW_ALL_TRENDING_STORES),
        getAllTrendingStores,
    )
router
    .route('/:storeId/update-priority')
    .put(
        validate(updateTrendingStorePriorityVal),
        isAdmin,
        authorizedAdminAccess(UPDATE_TRENDING_STORE),
        updateTrendingStoresPriority,
    )
router
    .route('/delete/:storeId')
    .patch(
        validate(updateTrendingStoreWithParamsVal),
        isAdmin,
        authorizedAdminAccess(DELETE_TRENDING_STORES),
        deleteTrendingStore,
    )
// router
//   .route("/:storeId/delete/from-stores")
//   .patch(
//     isAdmin,
//     authorizedAdminAccess(DELETE_TRENDING_STORES),
//     removeTrendingFromStore
//   );

export default router
