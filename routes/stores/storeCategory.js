import express from 'express'
import { StoreCategoryController } from '../../controllers/stores/categoryController.js'
import { StoreCategoryHistoryController } from '../../controllers/stores/storeCategoryHistoryController.js'
import {
	authorizedAdminAccess,
	isAdmin,
} from '../../middlewares/authMiddleware.js'
import {
	CREATE_STORE_CASHBACK_RATE,
	DELETE_STORE_CASHBACK_RATES,
	FILTER_STORE_CASHBACK_RATES_HISTORY,
	UPDATE_STORE_CASHBACK_RATES,
	VIEW_ALL_STORE_CASHBACK_RATES,
} from '../../utils/constants.js'
import {
	createStoreCategoryVal,
	updateStoreCategoryVal,
	updateStoreCategoryWithParamsVal,
} from '../../validations/storeCategory.validation.js'
import { validate } from '../../validations/validate.js'

const {
	createStoreCategory,
	getAllStoreCategories,
	getAllStoreCategoryList,
	getStoreCategoryDetails,
	updateStoreCategory,
	updateStoreCategoryActiveStatus,
} = new StoreCategoryController()

const router = express.Router()

const { getAllStoreCategoryHistory } = new StoreCategoryHistoryController()

router
	.route('/create')
	.post(
		validate(createStoreCategoryVal),
		isAdmin,
		authorizedAdminAccess(CREATE_STORE_CASHBACK_RATE),
		createStoreCategory,
	)
router.route('/all-categories').get(isAdmin, getAllStoreCategories)
router
	.route('/all-categories-list')
	.get(
		isAdmin,
		authorizedAdminAccess(VIEW_ALL_STORE_CASHBACK_RATES),
		getAllStoreCategoryList,
	)

router
	.route('/update/:categoryId')
	.post(
		validate(updateStoreCategoryWithParamsVal),
		isAdmin,
		authorizedAdminAccess(UPDATE_STORE_CASHBACK_RATES),
		getStoreCategoryDetails,
	)
	.put(
		validate(updateStoreCategoryVal),
		isAdmin,
		authorizedAdminAccess(UPDATE_STORE_CASHBACK_RATES),
		updateStoreCategory,
	)
	.delete(
		validate(updateStoreCategoryWithParamsVal),
		isAdmin,
		authorizedAdminAccess(DELETE_STORE_CASHBACK_RATES),
		updateStoreCategoryActiveStatus,
	)

//store category history
router
	.route('/all-history')
	.get(
		isAdmin,
		authorizedAdminAccess(FILTER_STORE_CASHBACK_RATES_HISTORY),
		getAllStoreCategoryHistory,
	)

export default router
