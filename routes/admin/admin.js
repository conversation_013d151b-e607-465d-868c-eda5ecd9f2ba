import express from "express";
const router = express.Router();
import {
  AuthController,
  logout,
  refreshToken,
} from "../../controllers/admin/auth.js";
import {
  authorizedAdminAccess,
  authorizedAdminAccessMultiple,
  isAdmin,
  superAdminAccess,
} from "../../middlewares/authMiddleware.js";
import {
  ACCESS_ADMIN_LOGS,
  ADD_NEW_ADMIN,
  BLOCK_ADMIN,
  CHANGE_ADMIN_STATUS,
  VIEW_ALL_ADMINS,
  VIEW_ALL_ADMINS_FILTER,
} from "../../utils/constants.js";

import { AdminController } from "../../controllers/admin/adminController.js";
import {
  adminLoginSchema,
  createAdminSchema,
  updateAdminDetailsWithParams,
  updateAdminPasswordSchema,
  updateAdminRoleSchema,
  updateAdminSchema,
} from "../../validations/admin.validation.js";
import { validate } from "../../validations/validate.js";

const {
  createAdmin,
  updateAdminDetails,
  getAdminDetails,
  getAllAdminLogs,
  getAllAdmins,
  updateAdminRole,
  updateAdminActiveStatus,
  getAllAdminsList,
  resetAdminPassword,
  deleteAdmin,
  updateAdminPassword
} = new AdminController();

const { adminLogin } = new AuthController();

router.route("/auth/sign-in").post(validate(adminLoginSchema), adminLogin);
router
  .route("/create")
  .post(
    validate(createAdminSchema),
    isAdmin,
    authorizedAdminAccess(ADD_NEW_ADMIN),
    createAdmin
  );
router
  .route("/update-role/:adminId")
  .patch(
    validate(updateAdminRoleSchema),
    isAdmin,
    authorizedAdminAccess(CHANGE_ADMIN_STATUS),
    updateAdminRole
  );
router
  .route("/change-block-status/:adminId")
  .patch(
    validate(updateAdminDetailsWithParams),
    isAdmin,
    authorizedAdminAccess(BLOCK_ADMIN),
    updateAdminActiveStatus
  );
router
  .route("/all-admins-list")
  .get(isAdmin, authorizedAdminAccessMultiple([VIEW_ALL_ADMINS, VIEW_ALL_ADMINS_FILTER]), getAllAdminsList);

router
  .route("/get-all-admins")
  .get(isAdmin, authorizedAdminAccess(VIEW_ALL_ADMINS), getAllAdmins);
router
  .route("/details/:adminId")
  .post(
    validate(updateAdminDetailsWithParams),
    isAdmin,
    authorizedAdminAccess(VIEW_ALL_ADMINS),
    getAdminDetails
  );

router
  .route("/update/:adminId")
  .put(
    validate(updateAdminSchema),
    isAdmin,
    authorizedAdminAccess(ADD_NEW_ADMIN),
    updateAdminDetails
  );

router
  .route("/update-password/:adminId")
  .put(
    validate(updateAdminPasswordSchema),
    isAdmin,
    authorizedAdminAccess(ADD_NEW_ADMIN),
    updateAdminPassword
  );

router.route("/auth/logout").post(logout);

router.route("/auth/refresh-token").post(refreshToken);

router
  .route("/logs/all-logs")
  .get(isAdmin, authorizedAdminAccess(ACCESS_ADMIN_LOGS), getAllAdminLogs);

// reset password
router
  .route("/reset-password")
  .put(
    validate(updateAdminPassword),
    isAdmin,
    superAdminAccess(),
    resetAdminPassword
  );
// delete admin
router
  .route("/delete/:adminId")
  .delete(
    validate(updateAdminDetailsWithParams),
    isAdmin,
    superAdminAccess(),
    deleteAdmin
  );

export default router;
