import express from "express";
import { AffiliationController } from "../../controllers/affiliationController.js";
import {
  authorizedAdminAccess,
  isAdmin,
} from "../../middlewares/authMiddleware.js";
import {
  CREATE_AFFILIATION,
  DELETE_AFFILIATION,
  UPDATE_AFFILIATION,
  VIEW_ALL_AFFILIATIONS,
} from "../../utils/constants.js";
import {
  createAffiliationSchema,
  updateAffiliationDetailsWithParams,
  updateAffiliationSchema,
} from "../../validations/affiliation.validation.js";
import { validate } from "../../validations/validate.js";
const router = express.Router();

const {
  createAffiliation,
  getAffiliationDetails,
  getAllAffiliations,
  getAllAffiliationsList,
  updateAffiliation,
  updateAffiliationActiveStatus,
} = new AffiliationController();

router
  .route("/all-affiliation-list")
  .get(
    isAdmin,
    authorizedAdminAccess(VIEW_ALL_AFFILIATIONS),
    getAllAffiliationsList
  );
router
  .route("/get-all-affiliations")
  .get(
    isAdmin,
    authorizedAdminAccess(VIEW_ALL_AFFILIATIONS),
    getAllAffiliations
  );

router
  .route("/details/:affiliationId")
  .post(
    isAdmin,
    authorizedAdminAccess(VIEW_ALL_AFFILIATIONS),
    getAffiliationDetails
  );
router
  .route("/create")
  .post(
    validate(createAffiliationSchema),
    isAdmin,
    authorizedAdminAccess(CREATE_AFFILIATION),
    createAffiliation
  );
router
  .route("/update/:affiliationId")
  .put(
    validate(updateAffiliationSchema),
    isAdmin,
    authorizedAdminAccess(UPDATE_AFFILIATION),
    updateAffiliation
  );
router
  .route("/delete/:affiliationId")
  .patch(
    validate(updateAffiliationDetailsWithParams),
    isAdmin,
    authorizedAdminAccess(DELETE_AFFILIATION),
    updateAffiliationActiveStatus
  );

export default router;
