import express from 'express'
import { OfferController } from '../../controllers/offerController.js'
import { getAdminBasedStores } from '../../controllers/stores/storeController.js'
import {
	authorizedAdminAccess,
	isAdmin,
} from '../../middlewares/authMiddleware.js'
import {
	ADD_NEW_OFFER,
	DELETE_OFFER,
	EDIT_OFFER,
	VIEW_OFFER,
} from '../../utils/constants.js'
const router = express.Router()
const {
	createOffer,
	getAllOffers,
	getAllOffersList,
	getOfferDetails,
	updateOffer,
	updateOfferActiveStatus,
} = new OfferController()

router.post(
	'/:offerId',
	isAdmin,
	authorizedAdminAccess(VIEW_OFFER),
	getOfferDetails,
)
router.post(
	'/all-offers',
	isAdmin,
	authorizedAdminAccess(VIEW_OFFER),
	getAllOffers,
)
router.post(
	'/all-offers-list',
	isAdmin,
	authorizedAdminAccess(VIEW_OFFER),
	getAllOffersList,
)

router.post(
	'/admin-based/:adminId',
	isAdmin,
	authorizedAdminAccess(VIEW_OFFER),
	getAdminBasedStores,
)
router.post(
	'/create/:storeId',
	isAdmin,
	authorizedAdminAccess(ADD_NEW_OFFER),
	createOffer,
)
router.patch(
	'/status/:offerId',
	isAdmin,
	authorizedAdminAccess(DELETE_OFFER),
	updateOfferActiveStatus,
)

router.put(
	'/update/:offerId',
	isAdmin,
	authorizedAdminAccess(EDIT_OFFER),
	updateOffer,
)

// router.post(
//   "/store/:storeId/all-offers",
//   isAdmin,
//   authorizedAdminAccess(VIEW_OFFER),
//   getAllOfferInStore
// );
export default router
