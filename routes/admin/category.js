import { Router } from "express";
const router = Router();

import {
  CategoryController,
  SubCategoryController,
} from "../../controllers/categoryController.js";
import {
  authorizedAdminAccess,
  isAdmin,
} from "../../middlewares/authMiddleware.js";
import {
  ADD_NEW_CATEGORY,
  CREATE_SUB_CATEGORY,
  DELETE_CATEGORY,
  DELETE_SUB_CATEGORY,
  REMOVE_SUBCATEGORY,
  UPDATE_CATEGORY,
  UPDATE_SUB_CATEGORY,
  VIEW_CATEGORIES,
} from "../../utils/constants.js";
import {
  createCategoryVal,
  removeSubcategoryVal,
  updateCategoryVal,
  updateCategoryWithParamsVal,
  updateCategoryTrendingPriorityVal,
} from "../../validations/category.validation.js";
import {
  createSubCategoryVal,
  updateSubCategoryVal,
  updateSubCategoryWithParamsVal,
} from "../../validations/subCategory.validation.js";
import { validate } from "../../validations/validate.js";

const {
  createCategory,
  getAllCategories,
  getAllCategoriesList,
  getCategoryDetails,
  updateCategory,
  deleteCategory,
  removeSubcategory,
  updateCategoryTrendingStatus,
  updateCategoryTrendingPriority,
  updateCategoryPriority,
  updateCategoryTopStatus,
} = new CategoryController();
const {
  createSubCategory,
  getAllSubCategories,
  getAllSubCategoriesList,
  getSubCategoryDetails,
  deleteSubCategory,
  updateSubCategory,
} = new SubCategoryController();
// categories route
router
  .route("/all-categories")
  .get(isAdmin, authorizedAdminAccess(VIEW_CATEGORIES), getAllCategories); // get all categories

router
  .route("/all-categories-list")
  .get(isAdmin, authorizedAdminAccess(VIEW_CATEGORIES), getAllCategoriesList); // get all categories

router
  .route("/create")
  .post(
    validate(createCategoryVal),
    isAdmin,
    authorizedAdminAccess(ADD_NEW_CATEGORY),
    createCategory
  );
router
  .route("/details/:categoryId")
  .post(
    validate(updateCategoryWithParamsVal),
    isAdmin,
    authorizedAdminAccess(ADD_NEW_CATEGORY),
    getCategoryDetails
  );

router
  .route("/update/:categoryId")
  .put(
    validate(updateCategoryVal),
    isAdmin,
    authorizedAdminAccess(UPDATE_CATEGORY),
    updateCategory
  );
router
  .route("/update/trending-status/:categoryId")
  .patch(
    validate(updateCategoryWithParamsVal),
    isAdmin,
    authorizedAdminAccess(UPDATE_CATEGORY),
    updateCategoryTrendingStatus
  );

router
  .route("/update/top-status/:categoryId")
  .patch(
    validate(updateCategoryWithParamsVal),
    isAdmin,
    authorizedAdminAccess(UPDATE_CATEGORY),
    updateCategoryTopStatus
  );

router
  .route("/update/trending-priority/:categoryId")
  .put(
    validate(updateCategoryTrendingPriorityVal),
    isAdmin,
    authorizedAdminAccess(UPDATE_CATEGORY),
    updateCategoryTrendingPriority
  );

router
  .route("/update/priority/:categoryId")
  .put(
    validate(updateCategoryTrendingPriorityVal),
    isAdmin,
    authorizedAdminAccess(UPDATE_CATEGORY),
    updateCategoryPriority
  );
router
  .route("/update/remove-subcategories/:categoryId")
  .patch(
    validate(removeSubcategoryVal),
    isAdmin,
    authorizedAdminAccess(REMOVE_SUBCATEGORY),
    removeSubcategory
  );

router
  .route("/delete/:categoryId")
  .patch(
    validate(updateCategoryWithParamsVal),
    isAdmin,
    authorizedAdminAccess(DELETE_CATEGORY),
    deleteCategory
  );

router
  .route("/sub/all-categories")
  .get(isAdmin, authorizedAdminAccess(VIEW_CATEGORIES), getAllSubCategories);
router
  .route("/sub/all-categories-list")
  .get(
    isAdmin,
    authorizedAdminAccess(VIEW_CATEGORIES),
    getAllSubCategoriesList
  );

router
  .route("/sub/create/:categoryId")
  .post(
    validate(createSubCategoryVal),
    isAdmin,
    authorizedAdminAccess(CREATE_SUB_CATEGORY),
    createSubCategory
  );

router
  .route("/sub/update/:categoryId")
  .put(
    validate(updateSubCategoryVal),
    isAdmin,
    authorizedAdminAccess(UPDATE_SUB_CATEGORY),
    updateSubCategory
  );

router
  .route("/sub/delete/:categoryId")
  .delete(
    validate(updateSubCategoryWithParamsVal),
    isAdmin,
    authorizedAdminAccess(DELETE_SUB_CATEGORY),
    deleteSubCategory
  );

router
  .route("/sub/details/:categoryId")
  .post(
    validate(updateSubCategoryWithParamsVal),
    isAdmin,
    authorizedAdminAccess(VIEW_CATEGORIES),
    getSubCategoryDetails
  );

export default router;
