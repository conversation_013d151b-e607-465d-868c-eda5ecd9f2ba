import express from 'express'
import { MiddlewareController } from '../../controllers/middlewareController.js'
import {
	authorizedAdminAccess,
	isAdmin,
} from '../../middlewares/authMiddleware.js'
import {
	CREATE_MIDDLEWARE,
	UPDATE_MIDDLEWARE,
	VIEW_ALL_ACTIONS,
	VIEW_ALL_MIDDLEWARES,
} from '../../utils/constants.js'
import {
	createMiddlewareVal,
	updateMiddlewareFeatureVal,
	updateMiddlewareNameVal,
	updateMiddlewareWithParamsVal,
} from '../../validations/middleware.validation.js'
import { validate } from '../../validations/validate.js'
const router = express.Router()

const {
	createMiddleware,
	getAllMiddlewareActions,
	getAllMiddlewares,
	updateMiddlewareName,
	updateMiddlewareStatus,
	addNewFeatureToMiddleware,
	removeNewFeatureFromMiddleware,
} = new MiddlewareController()

router
	.route('/create')
	.post(
		validate(createMiddlewareVal),
		isAdmin,
		authorizedAdminAccess(CREATE_MIDDLEWARE),
		createMiddleware,
	)
router
	.route('/add-features/:middlewareId')
	.put(
		validate(updateMiddlewareFeatureVal),
		isAdmin,
		authorizedAdminAccess(UPDATE_MIDDLEWARE),
		addNewFeatureToMiddleware,
	)
router
	.route('/remove-features/:middlewareId')
	.put(
		validate(updateMiddlewareFeatureVal),
		isAdmin,
		authorizedAdminAccess(UPDATE_MIDDLEWARE),
		removeNewFeatureFromMiddleware,
	)
router
	.route('/update/:middlewareId')
	.patch(
		validate(updateMiddlewareNameVal),
		isAdmin,
		authorizedAdminAccess(UPDATE_MIDDLEWARE),
		updateMiddlewareName,
	)
router
	.route('/change-status/:middlewareId')
	.patch(
		validate(updateMiddlewareWithParamsVal),
		isAdmin,
		authorizedAdminAccess(UPDATE_MIDDLEWARE),
		updateMiddlewareStatus,
	)
router
	.route('/view-all-actions')
	.get(
		isAdmin,
		authorizedAdminAccess(VIEW_ALL_ACTIONS),
		getAllMiddlewareActions,
	)
router.route('/view').get(isAdmin, getAllMiddlewares)

export default router
