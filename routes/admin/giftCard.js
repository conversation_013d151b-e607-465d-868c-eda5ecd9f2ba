import express from "express";
import { GiftCardSliderController } from "../../controllers/giftCards/sliderController.js";
import {
  Authentication,
  authorizedAdminAccess,
  isAdmin,
} from "../../middlewares/authMiddleware.js";

import { GiftCardController } from "../../controllers/giftCards/giftCardController.js";
import { giftCardOfferController } from "../../controllers/giftCards/offerController.js";
import { GiftCardOrderController } from "../../controllers/giftCards/orderController.js";
import {
  CREATE_GIFT_CARD,
  CREATE_GIFT_CARD_OFFERS,
  CREATE_GIFT_CARD_SLIDERS,
  DELETE_GIFT_CARD,
  DELETE_GIFT_CARD_OFFER,
  DELETE_GIFT_CARD_SLIDER,
  UPDATE_GIFT_CARD,
  UPDATE_GIFT_CARD_OFFER,
  UPDATE_GIFT_CARD_SLIDER,
  VIEW_ALL_GIFT_CARDS,
  VIEW_ALL_GIFT_CARD_OFFERS,
  VIEW_ALL_GIFT_CARD_SLIDER,
  VIEW_GIFT_CARD_OFFER_DETAILS,
} from "../../utils/constants.js";
import { VIEW_ALL_GIFT_CARD_ORDERS } from "../../utils/constants.js";
import {
  createGiftCardVal,
  updateGiftCardPriorityVal,
  updateGiftCardVal,
  updateGiftCardWithParamsVal,
} from "../../validations/giftCard.validation.js";
import {
  createGiftCardOfferVal,
  updateGiftCardOfferVal,
  updateGiftCardOfferWithParamsVal,
} from "../../validations/giftCardOffer.validation.js";
import { createGiftCardOrderVal } from "../../validations/giftCardOrder.validation.js";
import {
  createGiftCardSliderVal,
  updateGiftCardSliderPriorityVal,
  updateGiftCardSliderVal,
  updateGiftCardSliderWithParamsVal,
} from "../../validations/giftCardSlider.validation.js";
import { validate } from "../../validations/validate.js";
const router = express.Router();

const { isUser } = new Authentication();
const {
  createGiftCard,
  getGiftCardDetails,
  getAllGiftCards,
  getAllGiftCardsList,
  updateGiftCard,
  updateGiftCardPriority,
  deleteGiftCard,
} = new GiftCardController();
const {
  createGiftCardSlider,
  getAllGiftCardSliders,
  getGiftCardSliderDetails,
  updateGiftCardSlider,
  updateGiftCardSliderPriority,
  deleteGiftCardSlider,
} = new GiftCardSliderController();

const {
  createGiftCardOrder,
  getAllGiftCardOrders,
  getGiftCardOrderDetails,
  updateGiftCardOrder,
} = new GiftCardOrderController();

const {
  createGiftCardOffer,
  getAllGiftCardOffers,
  getGiftCardOfferDetails,
  updateGiftCardOffer,
  deleteGiftCardOffer,
} = new giftCardOfferController();
// gift cards
router
  .route("/all-gift-cards")
  .get(isAdmin, authorizedAdminAccess(VIEW_ALL_GIFT_CARDS), getAllGiftCards);
router
  .route("/gift-cards-list")
  .get(
    isAdmin,
    authorizedAdminAccess(VIEW_ALL_GIFT_CARDS),
    getAllGiftCardsList
  );
router
  .route("/create")
  .post(
    validate(createGiftCardVal),
    isAdmin,
    authorizedAdminAccess(CREATE_GIFT_CARD),
    createGiftCard
  );

router
  .route("/update/:giftCardId")
  .put(
    validate(updateGiftCardVal),
    isAdmin,
    authorizedAdminAccess(UPDATE_GIFT_CARD),
    updateGiftCard
  );
router
  .route("/update/priority/:giftCardId")
  .patch(
    validate(updateGiftCardPriorityVal),
    isAdmin,
    authorizedAdminAccess(UPDATE_GIFT_CARD),
    updateGiftCardPriority
  );
router
  .route("/delete/:giftCardId")
  .delete(
    validate(updateGiftCardWithParamsVal),
    isAdmin,
    authorizedAdminAccess(DELETE_GIFT_CARD),
    deleteGiftCard
  );
router
  .route("/details/:giftCardId")
  .post(
    validate(updateGiftCardWithParamsVal),
    isAdmin,
    authorizedAdminAccess(VIEW_ALL_GIFT_CARDS),
    getGiftCardDetails
  );

// gift card sliders
router
  .route("/sliders/all-sliders")
  .get(
    isAdmin,
    authorizedAdminAccess(VIEW_ALL_GIFT_CARD_SLIDER),
    getAllGiftCardSliders
  );
router
  .route("/sliders/create")
  .post(
    validate(createGiftCardSliderVal),
    isAdmin,
    authorizedAdminAccess(CREATE_GIFT_CARD_SLIDERS),
    createGiftCardSlider
  );
router
  .route("/sliders/update/:sliderId")
  .put(
    validate(updateGiftCardSliderVal),
    isAdmin,
    authorizedAdminAccess(UPDATE_GIFT_CARD_SLIDER),
    updateGiftCardSlider
  );
router
  .route("/sliders/details/:sliderId")
  .post(
    validate(updateGiftCardSliderWithParamsVal),
    isAdmin,
    authorizedAdminAccess(VIEW_ALL_GIFT_CARD_SLIDER),
    getGiftCardSliderDetails
  );
router
  .route("/sliders/update-priority/:sliderId")
  .patch(
    validate(updateGiftCardSliderPriorityVal),
    isAdmin,
    authorizedAdminAccess(UPDATE_GIFT_CARD_SLIDER),
    updateGiftCardSliderPriority
  );
router
  .route("/sliders/delete/:sliderId")
  .delete(
    validate(updateGiftCardSliderWithParamsVal),
    isAdmin,
    authorizedAdminAccess(DELETE_GIFT_CARD_SLIDER),
    deleteGiftCardSlider
  );

// gift card offers
router
  .route("/offers/all-offers")
  .get(
    isAdmin,
    authorizedAdminAccess(VIEW_ALL_GIFT_CARD_OFFERS),
    getAllGiftCardOffers
  );
router
  .route("/offers/create")
  .post(
    validate(createGiftCardOfferVal),
    isAdmin,
    authorizedAdminAccess(CREATE_GIFT_CARD_OFFERS),
    createGiftCardOffer
  );
router
  .route("/offers/update/:offerId")
  .put(
    validate(updateGiftCardOfferVal),
    isAdmin,
    authorizedAdminAccess(UPDATE_GIFT_CARD_OFFER),
    updateGiftCardOffer
  );
router
  .route("/offers/details/:offerId")
  .post(
    validate(updateGiftCardOfferWithParamsVal),
    isAdmin,
    authorizedAdminAccess(VIEW_GIFT_CARD_OFFER_DETAILS),
    getGiftCardOfferDetails
  );
router
  .route("/offers/delete/:offerId")
  .delete(
    validate(updateGiftCardOfferWithParamsVal),
    isAdmin,
    authorizedAdminAccess(DELETE_GIFT_CARD_OFFER),
    deleteGiftCardOffer
  );

// gift card orders
router
  .route("/orders/create")
  .post(validate(createGiftCardOrderVal), isUser, createGiftCardOrder);

router
  .route("/orders/all-orders")
  .get(
    isAdmin,
    authorizedAdminAccess(VIEW_ALL_GIFT_CARD_ORDERS),
    getAllGiftCardOrders
  );

router
  .route("/orders/details/:oderId")
  .post(
    isAdmin,
    authorizedAdminAccess(VIEW_ALL_GIFT_CARD_ORDERS),
    getGiftCardOrderDetails
  );

router
  .route("/orders/update/:oderId")
  .put(
    isAdmin,
    authorizedAdminAccess(VIEW_ALL_GIFT_CARD_ORDERS),
    updateGiftCardOrder
  );

export default router;
