import express from 'express'
import { StoreReviewController } from '../../controllers/stores/reviewController.js'
import {
	authorizedAdminAccess,
	isAdmin,
} from '../../middlewares/authMiddleware.js'
import { isUser } from '../../middlewares/user/authMiddleware.js'
import {
	DELETE_STORE_REVIEWS,
	GET_ALL_STORE_REVIEWS,
	UPDATE_STORE_REVIEWS,
} from '../../utils/constants.js'

const router = express.Router()

const {
	createStoreReview,
	updateStoreReview,
	updateStoreReviewActiveStatus,
	permanentDeleteStoreReview,
	getAllStoreReviews,
} = new StoreReviewController()

router.route('/review/:storeId').post(isUser, createStoreReview)

router
	.route('/:reviewId')
	.put(isAdmin, authorizedAdminAccess(UPDATE_STORE_REVIEWS), updateStoreReview)
	.patch(
		isAdmin,
		authorizedAdminAccess(DELETE_STORE_REVIEWS),
		updateStoreReviewActiveStatus,
	)
	.delete(
		isAdmin,
		authorizedAdminAccess(DELETE_STORE_REVIEWS),
		permanentDeleteStoreReview,
	)

router
	.route('/get-all-reviews')
	.get(
		isAdmin,
		authorizedAdminAccess(GET_ALL_STORE_REVIEWS),
		getAllStoreReviews,
	)

// router
//   .route("/filter")
//   .post(isAdmin, authorizedAdminAccess(GET_ALL_STORE_REVIEWS), filterReviews);
export default router
