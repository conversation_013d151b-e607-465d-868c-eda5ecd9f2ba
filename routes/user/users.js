import express from "express";
import { UserController } from "../../controllers/user/userController.js";
import {
  authorizedAdminAccess,
  isAdmin,
} from "../../middlewares/authMiddleware.js";
import { UPDATE_USER_DETAILS, VIEW_ALL_USERS } from "../../utils/constants.js";
import {
  updateUserNoteVal,
  updateUserWithParamsVal,
} from "../../validations/user.validation.js";
import { validate } from "../../validations/validate.js";

const router = express.Router();

const {
  getAllUsers,
  getAllUsersList,
  updateUserActiveStatus,
  writeUserNotes,
  getUserStatistics,
} = new UserController();

router
  .route("/all-users")
  .get(isAdmin, authorizedAdminAccess(VIEW_ALL_USERS), getAllUsers);

router
  .route("/all-user-list")
  .get(isAdmin, authorizedAdminAccess(VIEW_ALL_USERS), getAllUsersList);

router
  .route("/user-statistics/:userId")
  .get(isAdmin, authorizedAdminAccess(VIEW_ALL_USERS), getUserStatistics);

router
  .route("/update-status/:userId")
  .patch(
    validate(updateUserWithParamsVal),
    isAdmin,
    authorizedAdminAccess(UPDATE_USER_DETAILS),
    updateUserActiveStatus
  );

router
  .route("/write-note/:userId")
  .put(
    validate(updateUserNoteVal),
    isAdmin,
    authorizedAdminAccess(UPDATE_USER_DETAILS),
    writeUserNotes
  );

export default router;
