import express from "express";
import { EarningController } from "../../controllers/user/earningController.js";
import { isAdmin } from "../../middlewares/authMiddleware.js";
import { authorizedAdminAccess } from "../../middlewares/authMiddleware.js";
import {
    CREATE_EARNINGS,
    DELETE_EARNINGS,
    GET_ALL_EARNINGS,
    GET_APPROVED_EARNINGS,
    UPDATE_EARNINGS,
} from "../../utils/constants.js";

const router = express.Router();
const {
    getAllEarnings,
    getAllPreApprovedEarnings,
    cancelEarningsBulk,
    createEarnings,
    confirmEarnings,
    cancelEarnings,
    getEarningDetails,
    updateEarning,
    earningTrackedToConfirm,
    earningTrackedToCancel,
    permanentDeleteEarning,
    getAllAutoTrackedEarnings
} = new EarningController();

router
    .route("/create")
    .post(isAdmin, authorizedAdminAccess(CREATE_EARNINGS), createEarnings);
router
    .route("/all-earnings")
    .get(isAdmin, authorizedAdminAccess(GET_ALL_EARNINGS), getAllEarnings);

router
    .route("/all-auto-tracked")
    .get(isAdmin, authorizedAdminAccess(GET_ALL_EARNINGS), getAllAutoTrackedEarnings);

router
    .route("/pre-approved/all-earnings")
    .get(
        isAdmin,
        authorizedAdminAccess(GET_ALL_EARNINGS),
        getAllPreApprovedEarnings
    );

router
    .route("/details/:earningId")
    .post(isAdmin, authorizedAdminAccess(GET_ALL_EARNINGS), getEarningDetails);

router
    .route("/update/confirm")
    .patch(isAdmin, authorizedAdminAccess(UPDATE_EARNINGS), confirmEarnings);

router
    .route("/update/cancel-bulk")
    .patch(isAdmin, authorizedAdminAccess(UPDATE_EARNINGS), cancelEarningsBulk);

router
    .route("/update/cancel/:earningId")
    .patch(isAdmin, authorizedAdminAccess(UPDATE_EARNINGS), cancelEarnings);

router
    .route("/update/:earningId")
    .put(isAdmin, authorizedAdminAccess(UPDATE_EARNINGS), updateEarning);

router
    .route("/tracked-for-confirm/:earningId")
    .patch(
        isAdmin,
        authorizedAdminAccess(UPDATE_EARNINGS),
        earningTrackedToConfirm
    );

router
    .route("/tracked-for-cancel/:earningId")
    .patch(
        isAdmin,
        authorizedAdminAccess(UPDATE_EARNINGS),
        earningTrackedToCancel
    );

router
    .route("/delete/:earningId")
    .delete(
        isAdmin,
        authorizedAdminAccess(DELETE_EARNINGS),
        permanentDeleteEarning
    );

export default router;
