import { Router } from "express";
import { PersonalInterestController } from "../../controllers/user/personalInterestController.js";
import { validate } from "../../validations/validate.js";
import {
  authorizedAdminAccess,
  isAdmin,
} from '../../middlewares/authMiddleware.js'
import { createPersonalInterestVal } from "../../validations/personalInterest.js";

const router = Router()

const { createPersonalInterest, getAllPersonalInterest } = new PersonalInterestController()

router.route('/create').post(validate(createPersonalInterestVal), isAdmin, createPersonalInterest)
router.route('/all-personal-interests').get(isAdmin, getAllPersonalInterest)


export default router