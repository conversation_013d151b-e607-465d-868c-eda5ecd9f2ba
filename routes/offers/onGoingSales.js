import { Router } from "express";
import { OnGoingSaleOfferController } from "../../controllers/offer/onGoingSaleOfferController.js";
import {
    isAdmin,
    authorizedAdminAccess,
} from "../../middlewares/authMiddleware.js";
import { validate } from "../../validations/validate.js";
import {
    createOnGoingSaleVal,
    updateOnGoingSaleWithParamsVal,
    updateOnGoingSaleVal,
} from "../../validations/onGoingSale.validation.js";
import {
    CREATE_ONGOING_SALES,
    DELETE_ONGOING_SALES,
    GET_ALL_ONGOING_SALES,
    UPDATE_ONGOING_SALES,
} from "../../utils/constants.js";

const router = Router();

const {
    createOnGoingSale,
    getAllOnGoingSales,
    getOnGoingSaleDetails,
    updateOnGoingSale,
    deleteOnGoingSale,
    handleBlockStatus
} = new OnGoingSaleOfferController();

router
    .route("/create")
    .post(
        validate(createOnGoingSaleVal),
        isAdmin,
        authorizedAdminAccess(CREATE_ONGOING_SALES),
        createOnGoingSale
    );
router
    .route("/all-ongoing-sales")
    .get(
        isAdmin,
        authorizedAdminAccess(GET_ALL_ONGOING_SALES),
        getAllOnGoingSales
    );
router
    .route("/update/:offerId")
    .put(
        validate(updateOnGoingSaleVal),
        isAdmin,
        authorizedAdminAccess(UPDATE_ONGOING_SALES),
        updateOnGoingSale
    );

router
    .route("/change-status/:offerId")
    .put(
        validate(updateOnGoingSaleWithParamsVal),
        isAdmin,
        authorizedAdminAccess(UPDATE_ONGOING_SALES),
        handleBlockStatus
    );

router
    .route("/details/:offerId")
    .post(
        validate(updateOnGoingSaleWithParamsVal),
        isAdmin,
        authorizedAdminAccess(GET_ALL_ONGOING_SALES),
        getOnGoingSaleDetails
    );
router
    .route("/delete/:offerId")
    .delete(
        validate(updateOnGoingSaleWithParamsVal),
        isAdmin,
        authorizedAdminAccess(DELETE_ONGOING_SALES),
        deleteOnGoingSale
    );

export default router;
