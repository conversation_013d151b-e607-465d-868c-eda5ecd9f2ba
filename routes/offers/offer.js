import express from "express";
import { OfferController } from "../../controllers/offer/offerController.js";
import {
  authorizedAdminAccess,
  isAdmin,
} from "../../middlewares/authMiddleware.js";
import {
  ADD_NEW_OFFER,
  CREATE_CASHBACK,
  DELETE_CASHBACK,
  DELETE_OFFER,
  EDIT_CASHBACK,
  EDIT_OFFER,
  UPDATE_COMMISSIONS,
  UPDATE_TRENDING_OFFER,
  VIEW_ALL_CASHBACK,
  VIEW_ALL_TRENDING_OFFERS,
  VIEW_OFFER,
} from "../../utils/constants.js";
import {
  createOfferVal,
  updateOfferPriorityVal,
  updateOfferTrendingPriorityVal,
  updateOfferVal,
  updateOfferWithParamsVal,
} from "../../validations/offer.validation.js";
import { validate } from "../../validations/validate.js";
import { convertParamsToUTC } from "../../middlewares/timeConversionMiddleware.js";

const router = express.Router();

const {
  createOffer,
  getAllOffers,
  getAllOffersList,
  getOfferDetails,
  updateOffer,
  updateOfferPriority,
  updateOfferActiveStatus,
  updateOfferTrendingStatus,
  getAllTrendingOffers,
  updateOfferTrendingPriority,
  updateOfferMissedStatus,
} = new OfferController();

router
  .route("/all-offers")
  .get(isAdmin, authorizedAdminAccess(VIEW_OFFER), getAllOffers);

router
  .route("/all-offers-list")
  .get(isAdmin, authorizedAdminAccess(VIEW_OFFER), getAllOffersList);

router
  .route("/create")
  .post(
    validate(createOfferVal),
    convertParamsToUTC(["dateExpiry", "dateStart"]),
    isAdmin,
    authorizedAdminAccess(ADD_NEW_OFFER),
    createOffer
  );

router
  .route("/details/:offerId")
  .post(
    validate(updateOfferWithParamsVal),
    isAdmin,
    authorizedAdminAccess(EDIT_OFFER),
    getOfferDetails
  );
router
  .route("/update/:offerId")
  .put(
    validate(updateOfferVal),
    convertParamsToUTC(["dateExpiry", "dateStart"]),
    isAdmin,
    authorizedAdminAccess(EDIT_OFFER),
    updateOffer
  );

router
  .route("/delete/:offerId")
  .delete(
    validate(updateOfferWithParamsVal),
    isAdmin,
    authorizedAdminAccess(DELETE_OFFER),
    updateOfferActiveStatus
  );

router
  .route("/update-priority/:offerId")
  .patch(
    validate(updateOfferPriorityVal),
    isAdmin,
    authorizedAdminAccess(EDIT_OFFER),
    updateOfferPriority
  );

router
  .route("/trending/all-offers")
  .get(
    isAdmin,
    authorizedAdminAccess(VIEW_ALL_TRENDING_OFFERS),
    getAllTrendingOffers
  );
router
  .route("/trending/status/:offerId")
  .patch(
    validate(updateOfferWithParamsVal),
    isAdmin,
    authorizedAdminAccess(UPDATE_TRENDING_OFFER),
    updateOfferTrendingStatus
  );
router
  .route("/trending/update-priority/:offerId")
  .put(
    validate(updateOfferTrendingPriorityVal),
    isAdmin,
    authorizedAdminAccess(UPDATE_TRENDING_OFFER),
    updateOfferTrendingPriority
  );

router
  .route("/missed/status/:offerId")
  .patch(
    validate(updateOfferWithParamsVal),
    isAdmin,
    authorizedAdminAccess(EDIT_OFFER),
    updateOfferMissedStatus
  );

export default router;
