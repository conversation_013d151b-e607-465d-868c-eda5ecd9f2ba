import express from "express";
import { PaymentRequestController } from "../../controllers/payments/requestController.js";
import {
  authorizedAdminAccess,
  isAdmin,
} from "../../middlewares/authMiddleware.js";
import {
  DELETE_PAYMENT_REQUEST,
  GET_ALL_PAYMENT_REQUESTS,
  GET_PAYMENT_REQUEST_DETAILS,
  UPDATE_PAYMENT_REQUEST,
} from "../../utils/constants.js";

const {
  getAllPaymentRequests,
  getPaymentRequestDetails,
  deletePaymentRequest,
  rejectPaymentRequest,
  approvePaymentRequest,
} = new PaymentRequestController();

const router = express.Router();

router
  .route("/update/:requestId")
  .put(isAdmin, authorizedAdminAccess(UPDATE_PAYMENT_REQUEST));
router
  .route("/details/:requestId")
  .post(
    isAdmin,
    authorizedAdminAccess(GET_PAYMENT_REQUEST_DETAILS),
    getPaymentRequestDetails
  );
router
  .route("/approve/:requestId")
  .patch(
    isAdmin,
    authorizedAdminAccess(UPDATE_PAYMENT_REQUEST),
    approvePaymentRequest
  );
router
  .route("/reject/:requestId")
  .patch(
    isAdmin,
    authorizedAdminAccess(UPDATE_PAYMENT_REQUEST),
    rejectPaymentRequest
  );
router
  .route("/all-requests")
  .get(
    isAdmin,
    authorizedAdminAccess(GET_ALL_PAYMENT_REQUESTS),
    getAllPaymentRequests
  );
export default router;
