import express from "express";
import { MissingCashbackController } from "../../controllers/payments/missingCashbackController.js";
import {
    authorizedAdminAccess,
    isAdmin,
} from "../../middlewares/authMiddleware.js";
import {
    DELETE_MISSING_CASHBACK,
    GET_ALL_MISSING_CASHBACK,
    UPDATE_MISSING_CASHBACK,
} from "../../utils/constants.js";

const router = express.Router();

const {
    getAllMissingCashbacks,
    getMissingCashbackDetails,
    deleteMissingCashback,
    updateMissingCashbackNotes,
    updateMissingCashbackStatus
} = new MissingCashbackController();

// user
router.route("/details/:cashbackId").post(isAdmin, getMissingCashbackDetails);

router
    .route("/update-notes/:cashbackId")
    .patch(
        isAdmin,
        authorizedAdminAccess(UPDATE_MISSING_CASHBACK),
        updateMissingCashbackNotes
    );

router
    .route("/update-status/:cashbackId")
    .patch(
        isAdmin,
        authorizedAdminAccess(UPDATE_MISSING_CASHBACK),
        updateMissingCashbackStatus
    );



// admin
router
    .route("/all-missing-cashback")
    .get(
        isAdmin,
        authorizedAdminAccess(GET_ALL_MISSING_CASHBACK),
        getAllMissingCashbacks
    );
router
    .route("/delete/:cashbackId")
    .delete(
        isAdmin,
        authorizedAdminAccess(DELETE_MISSING_CASHBACK),
        deleteMissingCashback
    );

export default router;
