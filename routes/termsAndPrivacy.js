import express from "express";
import { TermsAndPrivacyController } from "../controllers/termsAndPrivacyController.js";
import {
  authorizedAdminAccess,
  isAdmin,
} from "../middlewares/authMiddleware.js";
import {
  createTermsAndPrivacyVal,
  updateTermsAndPrivacyVal,
  updateTermsAndPrivacyWithParamsVal,
  updateTermsAndPrivacyTypeVal,
} from "../validations/termsAndPrivacy.validation.js";
import { validate } from "../validations/validate.js";
import {
  CREATE_TERMS_AND_PRIVACY,
  DELETE_TERMS_AND_PRIVACY,
  GET_ALL_TERMS_AND_PRIVACY,
  UPDATE_TERMS_AND_PRIVACY,
} from "../utils/constants.js";
const router = express.Router();

const {
  createTermsAndPrivacy,
  getAllTermsAndPrivacies,
  getTermsAndPrivacyDetails,
  updateTermsAndPrivacy,
  updateTermsAndPrivacyActiveStatus,
  updateTermsAndPrivacyType,
  getTermsAndPrivacyLogs,
} = new TermsAndPrivacyController();

router
  .route("/create")
  .post(
    validate(createTermsAndPrivacyVal),
    isAdmin,
    authorizedAdminAccess(CREATE_TERMS_AND_PRIVACY),
    createTermsAndPrivacy
  );

router
  .route("/all-terms-and-privacies")
  .get(
    isAdmin,
    authorizedAdminAccess(GET_ALL_TERMS_AND_PRIVACY),
    getAllTermsAndPrivacies
  );

router
  .route("/update/:id")
  .put(
    validate(updateTermsAndPrivacyVal),
    isAdmin,
    authorizedAdminAccess(UPDATE_TERMS_AND_PRIVACY),
    updateTermsAndPrivacy
  );

router
  .route("/update-status/:id")
  .patch(
    validate(updateTermsAndPrivacyWithParamsVal),
    isAdmin,
    authorizedAdminAccess(DELETE_TERMS_AND_PRIVACY),
    updateTermsAndPrivacyActiveStatus
  );

router
  .route("/update-type/:id")
  .patch(
    validate(updateTermsAndPrivacyTypeVal),
    isAdmin,
    authorizedAdminAccess(UPDATE_TERMS_AND_PRIVACY),
    updateTermsAndPrivacyType
  );

router
  .route("/details/:id")
  .post(
    validate(updateTermsAndPrivacyWithParamsVal),
    isAdmin,
    authorizedAdminAccess(GET_ALL_TERMS_AND_PRIVACY),
    getTermsAndPrivacyDetails
  );

router
  .route("/logs")
  .get(
    isAdmin,
    authorizedAdminAccess(GET_ALL_TERMS_AND_PRIVACY),
    getTermsAndPrivacyLogs
  );

export default router;
