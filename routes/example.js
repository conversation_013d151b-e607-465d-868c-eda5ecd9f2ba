import { Router } from 'express'

const router = Router()
/**
 * @swagger
 * /test/example:
 *   get:
 *     tags:
 *     description: sample api for testing
 *     summary: sample api for testing
 *     responses:
 *       200:
 *         description: to test the method.
 */
router.route('/example').get(async (req, res) => {
	try {
		res.status(200).json({ success: true, message: 'testing method success' })
	} catch (error) {
		console.log(error);
		res.status(500).json({ success: true, message: error.message })
	}

})

router.route('/example/:id').post((req, res) => {
	res.status(200).json({ success: true, response: 'example' })
})

export default router
