import express from "express";
import { <PERSON>lick<PERSON>ontroller } from "../controllers/clickController.js";
import {
  authorizedAdminAccess,
  isAdmin,
} from "../middlewares/authMiddleware.js";
import { GET_CLICKS, GET_CLICK_DETAILS } from "../utils/constants.js";
const router = express.Router();

const { getAllClicks, getClickDetails } = new ClickController();

router
  .route("/get-all-clicks")
  .get(isAdmin, authorizedAdminAccess(GET_CLICKS), getAllClicks);

router
  .route("/details/:clickId")
  .post(isAdmin, authorizedAdminAccess(GET_CLICK_DETAILS), getClickDetails);

//

export default router;
