{"name": "icb-admin", "version": "1.0.0", "description": "A web-based application that provides cashback rewards to customers for their purchases", "main": "index.js", "type": "module", "scripts": {"start": "bun index.js", "dev": "bun --hot index.js"}, "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.556.0", "@aws-sdk/s3-request-presigner": "^3.556.0", "@getbrevo/brevo": "^2.2.0", "@onesignal/node-onesignal": "5.0.0-alpha-01", "bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "cloudinary": "^2.3.0", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.4.1", "eslint": "^9.22.0", "eslint-plugin-jsdoc": "^50.6.6", "express": "^4.18.2", "express-async-handler": "^1.2.0", "express-fileupload": "^1.4.3", "express-rate-limit": "^7.1.5", "jsonwebtoken": "^9.0.2", "mailgun.js": "^10.2.1", "meilisearch": "^0.38.0", "moment": "^2.30.1", "moment-timezone": "^0.5.46", "mongoose": "^8.1.1", "node-cron": "^3.0.3", "nodemon": "^3.1.0", "ohash": "^1.1.3", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "uuid": "^9.0.1", "validator": "^13.11.0", "yup": "^1.3.3"}, "devDependencies": {"@biomejs/biome": "1.6.2"}}