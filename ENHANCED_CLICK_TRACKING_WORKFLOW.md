# Enhanced Click Tracking Workflow - Three-Scenario Implementation

## Overview

The Enhanced Click Tracking Workflow implements a sophisticated three-scenario approach to handle order processing with proper precedence and comprehensive duplicate prevention. This system ensures maximum conversion tracking accuracy while maintaining data integrity.

## Architecture

### Core Components

1. **ClickTrackingOrchestrator** - Main workflow coordinator
2. **DuplicateDetector** - Enhanced scenario-aware duplicate detection
3. **EarningCreator** - Handles earning record creation
4. **ClickMatcher** - Manages click lookup and validation
5. **CurrencyConverter** - Handles currency conversion
6. **StoreValidator** - Validates store settings
7. **NotificationManager** - Manages notifications

### Three-Scenario Precedence Logic

```
INPUT: Conversation Data
├── orderID (conversionData.adId)
└── clickID (conversionData.affiliateInfo1 || conversionData.affiliateInfo2)

SCENARIO PRECEDENCE (Highest to Lowest Priority):

1. DUPLICATE PREVENTION (Priority: HIGHEST)
   ├── Check: earnings.findOne({ orderUniqueId: orderID })
   ├── Action: SKIP if duplicate found
   └── Metrics: scenario1_duplicatesPrevented++

2. CLICK-TO-EARNING CONVERSION (Priority: MEDIUM)
   ├── Condition: orderID exists AND clickID exists
   ├── Process: Find click → Validate → Create earning with attribution
   ├── Update: click.status = 'tracked'
   └── Metrics: scenario2_clickToEarning++

3. STANDALONE EARNING CREATION (Priority: LOWEST)
   ├── Condition: orderID exists (no click attribution) OR fallback
   ├── Process: Create earning without click reference
   ├── Auto-generate: orderID if missing (AUTO-{partner}-{timestamp}-{random})
   └── Metrics: scenario3_standaloneEarning++
```

## Workflow Flow Diagram

```mermaid
flowchart TD
    A[Conversion Data Input] --> B[Extract orderID & clickID]
    B --> C{Scenario-Aware Duplicate Check}
    
    C -->|Duplicate Found| D[SCENARIO 1: Skip Processing]
    D --> E[Log Duplicate Prevention]
    E --> F[Update Metrics]
    F --> Z[End]
    
    C -->|No Duplicate| G[Currency Conversion]
    G --> H{Data Available?}
    
    H -->|orderID + clickID| I[SCENARIO 2: Click-to-Earning]
    I --> J[Find Click Record]
    J --> K{Click Valid?}
    K -->|Yes| L[Validate Store Settings]
    L --> M{Store Valid?}
    M -->|Yes| N[Create Earning with Click Attribution]
    N --> O[Update Click Status to 'tracked']
    O --> P[Send Notifications]
    P --> Q[Update Metrics: scenario2++]
    Q --> Z
    
    K -->|No| R[SCENARIO 3: Standalone Earning]
    M -->|No| R
    H -->|orderID only| R
    H -->|No identifiers| S[Generate Auto OrderID]
    S --> R
    
    R --> T[Create Standalone Earning]
    T --> U[Send Notifications (no click)]
    U --> V[Update Metrics: scenario3++]
    V --> Z
```

## Implementation Details

### Enhanced DuplicateDetector

The `DuplicateDetector` class now includes scenario-aware duplicate checking:

```javascript
async checkForDuplicatesWithScenarios(conversionData) {
    // Returns detailed scenario information for decision-making
    return {
        isDuplicate: boolean,
        scenario: number,           // Recommended scenario (1, 2, or 3)
        orderIdExists: boolean,
        clickIdExists: boolean,
        existingEarning: object,
        duplicateType: string,      // 'orderID' or 'clickID'
        recommendedAction: string,  // 'skip_processing', 'proceed_scenario_2', etc.
        reason: string
    }
}
```

### Scenario Handler Methods

#### Scenario 1: Duplicate Prevention
```javascript
async handleScenario1_DuplicatePrevention(conversionData, scenarioCheck) {
    // Skip processing for duplicate orders
    // Log comprehensive duplicate information
    // Update scenario-specific metrics
    // Return skip result with existing earning reference
}
```

#### Scenario 2: Click-to-Earning Conversion
```javascript
async handleScenario2_ClickToEarning(convertedData, scenarioCheck) {
    // Find and validate click record
    // Check click status (not already 'tracked')
    // Validate store settings
    // Create earning with click attribution
    // Update click status to 'tracked'
    // Fallback to Scenario 3 on any validation failure
}
```

#### Scenario 3: Standalone Earning Creation
```javascript
async handleScenario3_StandaloneEarning(convertedData, _scenarioCheck) {
    // Generate auto orderID if needed
    // Create earning without click attribution
    // Set click, user, store fields to null
    // Send notifications without click data
}
```

### Enhanced Metrics Tracking

The system now tracks detailed scenario-specific metrics:

```javascript
metrics: {
    // General metrics
    conversionsProcessed: 0,
    earningsCreated: 0,
    duplicatesSkipped: 0,
    duplicatesByOrderId: 0,
    duplicatesByClickId: 0,
    errorsEncountered: 0,
    earningsWithClick: 0,
    earningsWithoutClick: 0,
    missingIdentifiers: 0,
    generatedOrderIds: 0,
    
    // Scenario-specific metrics
    scenario1_duplicatesPrevented: 0,
    scenario2_clickToEarning: 0,
    scenario3_standaloneEarning: 0
}
```

## Database Schema Validation

### Required Indexes

For optimal performance, ensure these indexes exist:

```javascript
// Earnings collection
db.earnings.createIndex({ "orderUniqueId": 1 })  // For duplicate detection
db.earnings.createIndex({ "referenceId": 1 })    // For click-based lookup

// Clicks collection  
db.clicks.createIndex({ "referenceId": 1 })      // For click lookup
db.clicks.createIndex({ "status": 1 })           // For status filtering
```

### Schema Relationships

```
Earnings {
    orderUniqueId: String,     // Order ID for duplicate detection
    click: ObjectId,           // Reference to Click (optional)
    user: ObjectId,            // Reference to User (optional)
    store: ObjectId,           // Reference to Store (optional)
    affiliation: ObjectId,     // Reference to Affiliation
    status: String,            // 'pending', 'confirmed', etc.
    // ... other fields
}

Clicks {
    referenceId: String,       // Unique click identifier
    status: String,            // 'clicked', 'tracked', 'confirmed', 'cancelled'
    user: ObjectId,            // Reference to User
    store: ObjectId,           // Reference to Store
    affiliation: ObjectId,     // Reference to Affiliation
    // ... other fields
}
```

## Usage Examples

### Basic Usage

```javascript
import { initializeClickTracking } from './services/clickTracking/index.js'

const orchestrator = initializeClickTracking()

// Process a single conversion
const result = await orchestrator.processConversion({
    adId: 'ORDER-12345',
    affiliateInfo1: 'CLICK-67890',
    saleAmount: 100.00,
    approvedPayout: 5.00,
    partner: 'Impact',
    datetime: new Date().toISOString()
})

console.log(`Processed with scenario ${result.scenario}`)
```

### Scenario-Specific Results

```javascript
// Scenario 1 Result (Duplicate Prevention)
{
    scenario: 1,
    action: 'skipped',
    reason: 'duplicate_prevention',
    existingEarning: { uid: 12345, ... }
}

// Scenario 2 Result (Click-to-Earning Conversion)
{
    scenario: 2,
    action: 'created',
    earning: { uid: 12346, ... },
    clickData: { referenceId: 'CLICK-67890', ... }
}

// Scenario 3 Result (Standalone Earning Creation)
{
    scenario: 3,
    action: 'created',
    earning: { uid: 12347, ... }
}
```

## Error Handling

The enhanced workflow includes comprehensive error handling:

1. **Graceful Degradation**: Scenario 2 falls back to Scenario 3 on validation failures
2. **Detailed Error Logging**: All errors include scenario context
3. **Non-blocking Processing**: Individual conversion failures don't stop batch processing
4. **Comprehensive Error Records**: Errors are tracked with full context for debugging

## Monitoring and Metrics

### Key Performance Indicators

- **Scenario Distribution**: Track percentage of conversions processed by each scenario
- **Duplicate Prevention Rate**: Monitor effectiveness of duplicate detection
- **Click Attribution Success**: Measure Scenario 2 success rate
- **Fallback Rate**: Monitor how often Scenario 2 falls back to Scenario 3
- **Processing Time**: Track performance across scenarios

### Alerting Thresholds

- Scenario 1 rate > 10% (high duplicate rate)
- Scenario 2 success rate < 70% (click attribution issues)
- Error rate > 5% (system issues)
- Processing time > 2 seconds per conversion (performance issues)

## Migration from Legacy System

The enhanced workflow maintains backward compatibility:

1. **Legacy Method Support**: `processConversionLegacy()` method available
2. **Gradual Migration**: Can be deployed alongside existing system
3. **Feature Flags**: Enable/disable enhanced workflow per partner
4. **Metrics Comparison**: Side-by-side metrics tracking for validation

## Next Steps

1. **Performance Optimization**: Monitor and optimize database queries
2. **Advanced Analytics**: Implement scenario-based reporting
3. **Machine Learning**: Potential for predictive duplicate detection
4. **Real-time Monitoring**: Dashboard for live scenario metrics
